/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 统一的Fetch封装器
 * 提供请求拦截、响应处理、错误处理、缓存、重试等功能
 */

import type {
  RequestConfig,
  ApiResponse,
  ApiError,
  RequestInterceptor,
  ResponseInterceptor,
  RequestOptions,
  FetchWrapperConfig,
  CacheItem,
  RequestQueueItem,
  LogLevel,
  RequestStats,
  HttpMethod
} from './types'

export class FetchWrapper {
  private config: FetchWrapperConfig
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private cache = new Map<string, CacheItem>()
  private requestQueue = new Map<string, RequestQueueItem[]>()
  private stats: RequestStats = {
    total: 0,
    success: 0,
    error: 0,
    cached: 0,
    retried: 0,
    averageTime: 0,
    lastRequestTime: 0
  }

  constructor(config: FetchWrapperConfig = {}) {
    this.config = {
      baseURL: '',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      },
      enableLogging: true,
      logLevel: 'info',
      enableCache: false,
      defaultCacheTime: 5 * 60 * 1000, // 5分钟
      enableDedupe: true,
      dedupeWindow: 1000, // 1秒
      enableRetry: true,
      defaultRetryCount: 3,
      defaultRetryDelay: 1000,
      errorHandlingMode: 'throw',
      ...config
    }
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 记录日志
   */
  private log(level: LogLevel, message: string, data?: any): void {
    if (!this.config.enableLogging) return
    
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error']
    const currentLevelIndex = levels.indexOf(this.config.logLevel || 'info')
    const messageLevelIndex = levels.indexOf(level)
    
    if (messageLevelIndex >= currentLevelIndex) {
      const timestamp = new Date().toISOString()
      const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`
      
      switch (level) {
        case 'debug':
          console.debug(logMessage, data)
          break
        case 'info':
          console.info(logMessage, data)
          break
        case 'warn':
          console.warn(logMessage, data)
          break
        case 'error':
          console.error(logMessage, data)
          break
      }
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(config: RequestConfig): string {
    const { url, method = 'GET', params, data } = config
    const key = `${method}:${url}`
    
    if (params) {
      const searchParams = new URLSearchParams(params).toString()
      return `${key}?${searchParams}`
    }
    
    if (data && method !== 'GET') {
      const dataStr = typeof data === 'string' ? data : JSON.stringify(data)
      return `${key}:${btoa(dataStr)}`
    }
    
    return key
  }

  /**
   * 获取缓存
   */
  private getCache<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() > item.expireTime) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }

  /**
   * 设置缓存
   */
  private setCache<T>(key: string, data: T, cacheTime?: number): void {
    const expireTime = Date.now() + (cacheTime || this.config.defaultCacheTime || 0)
    this.cache.set(key, { data, timestamp: Date.now(), expireTime })
  }

  /**
   * 构建完整URL
   */
  private buildURL(url: string, params?: Record<string, any>): string {
    let fullURL = url.startsWith('http') ? url : `${this.config.baseURL}${url}`
    
    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      
      const paramString = searchParams.toString()
      if (paramString) {
        fullURL += (fullURL.includes('?') ? '&' : '?') + paramString
      }
    }
    
    return fullURL
  }

  /**
   * 处理请求配置
   */
  private async processRequestConfig(config: RequestConfig): Promise<RequestConfig> {
    let processedConfig = { ...config }
    
    // 应用请求拦截器
    for (const interceptor of this.requestInterceptors) {
      if (interceptor.onFulfilled) {
        try {
          processedConfig = await interceptor.onFulfilled(processedConfig)
        } catch (error) {
          if (interceptor.onRejected) {
            await interceptor.onRejected(error)
          }
          throw error
        }
      }
    }
    
    // 合并默认headers
    processedConfig.headers = {
      ...this.config.headers,
      ...processedConfig.headers
    }
    
    // 设置默认超时
    if (!processedConfig.timeout) {
      processedConfig.timeout = this.config.timeout
    }
    
    return processedConfig
  }

  /**
   * 创建AbortController用于超时控制
   */
  private createTimeoutController(timeout?: number): AbortController | undefined {
    if (!timeout) return undefined
    
    const controller = new AbortController()
    setTimeout(() => controller.abort(), timeout)
    return controller
  }

  /**
   * 执行fetch请求
   */
  private async executeFetch(config: RequestConfig): Promise<Response> {
    const { url, method = 'GET', headers, data, timeout, signal, ...fetchOptions } = config
    
    const fullURL = this.buildURL(url, method === 'GET' ? config.params : undefined)
    
    // 创建超时控制器
    const timeoutController = this.createTimeoutController(timeout)
    const finalSignal = signal || timeoutController?.signal
    
    const fetchConfig: RequestInit = {
      method,
      headers,
      signal: finalSignal,
      ...fetchOptions
    }
    
    // 添加请求体
    if (data && method !== 'GET' && method !== 'HEAD') {
      if (typeof data === 'string') {
        fetchConfig.body = data
      } else if (data instanceof FormData) {
        fetchConfig.body = data
        // FormData会自动设置Content-Type，需要删除手动设置的
        if (fetchConfig.headers && typeof fetchConfig.headers === 'object') {
          delete (fetchConfig.headers as any)['Content-Type']
        }
      } else {
        fetchConfig.body = JSON.stringify(data)
      }
    }
    
    this.log('debug', `发送请求: ${method} ${fullURL}`, { config: fetchConfig })
    
    return fetch(fullURL, fetchConfig)
  }

  /**
   * 处理响应
   */
  private async processResponse(response: Response, config: RequestConfig): Promise<ApiResponse> {
    const apiResponse: ApiResponse = {
      data: null,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      config
    }
    
    // 检查响应状态
    if (!response.ok) {
      const error: ApiError = {
        message: `HTTP ${response.status}: ${response.statusText}`,
        status: response.status,
        statusText: response.statusText,
        config
      }
      
      // 尝试解析错误响应体
      try {
        const contentType = response.headers.get('content-type')
        if (contentType?.includes('application/json')) {
          error.data = await response.json()
          if (error.data?.message) {
            error.message = error.data.message
          }
        } else {
          error.data = await response.text()
        }
      } catch (e) {
        // 忽略解析错误
      }
      
      throw error
    }
    
    // 解析响应体
    try {
      const contentType = response.headers.get('content-type')
      if (contentType?.includes('application/json')) {
        apiResponse.data = await response.json()
      } else if (contentType?.includes('text/')) {
        apiResponse.data = await response.text()
      } else {
        apiResponse.data = await response.blob()
      }
    } catch (error) {
      this.log('warn', '解析响应体失败', error)
      apiResponse.data = null
    }
    
    // 应用响应拦截器
    let processedResponse = apiResponse
    for (const interceptor of this.responseInterceptors) {
      if (interceptor.onFulfilled) {
        try {
          processedResponse = await interceptor.onFulfilled(processedResponse)
        } catch (error) {
          if (interceptor.onRejected) {
            await interceptor.onRejected(error)
          }
          throw error
        }
      }
    }
    
    return processedResponse
  }

  /**
   * 重试逻辑
   */
  private async retryRequest(
    config: RequestConfig,
    options: RequestOptions,
    error: any,
    attempt: number
  ): Promise<ApiResponse> {
    const maxRetries = options.retryCount ?? this.config.defaultRetryCount ?? 3
    const retryDelay = options.retryDelay ?? this.config.defaultRetryDelay ?? 1000

    if (attempt >= maxRetries) {
      throw error
    }

    this.log('warn', `请求失败，${retryDelay}ms后进行第${attempt + 1}次重试`, error)

    await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)))

    this.stats.retried++
    return this.executeRequest(config, options, attempt + 1)
  }

  /**
   * 执行请求的核心方法
   */
  private async executeRequest(
    config: RequestConfig,
    options: RequestOptions = {},
    attempt: number = 0
  ): Promise<ApiResponse> {
    const startTime = Date.now()

    try {
      // 处理请求配置
      const processedConfig = await this.processRequestConfig(config)

      // 检查缓存
      if (this.config.enableCache && options.cache !== false) {
        const cacheKey = this.generateCacheKey(processedConfig)
        const cachedData = this.getCache(cacheKey)
        if (cachedData) {
          this.log('debug', '使用缓存数据', { cacheKey })
          this.stats.cached++
          return cachedData
        }
      }

      // 请求去重
      if (this.config.enableDedupe && options.dedupe !== false) {
        const requestKey = this.generateCacheKey(processedConfig)
        const existingRequests = this.requestQueue.get(requestKey) || []

        // 检查是否有相同的请求正在进行
        const recentRequest = existingRequests.find(
          item => Date.now() - item.timestamp < (this.config.dedupeWindow || 1000)
        )

        if (recentRequest) {
          this.log('debug', '请求去重，等待现有请求完成', { requestKey })
          return new Promise((resolve, reject) => {
            existingRequests.push({ config: processedConfig, resolve, reject, timestamp: Date.now() })
          })
        }

        // 添加到请求队列
        this.requestQueue.set(requestKey, [{
          config: processedConfig,
          resolve: () => {},
          reject: () => {},
          timestamp: Date.now()
        }])
      }

      // 执行请求
      const response = await this.executeFetch(processedConfig)
      const apiResponse = await this.processResponse(response, processedConfig)

      // 更新统计
      this.stats.total++
      this.stats.success++
      this.stats.lastRequestTime = Date.now()
      this.stats.averageTime = (this.stats.averageTime * (this.stats.total - 1) + (Date.now() - startTime)) / this.stats.total

      // 设置缓存
      if (this.config.enableCache && options.cache !== false) {
        const cacheKey = this.generateCacheKey(processedConfig)
        this.setCache(cacheKey, apiResponse, options.cacheTime)
      }

      // 处理请求队列
      if (this.config.enableDedupe) {
        const requestKey = this.generateCacheKey(processedConfig)
        const existingRequests = this.requestQueue.get(requestKey) || []
        existingRequests.forEach(item => item.resolve(apiResponse))
        this.requestQueue.delete(requestKey)
      }

      this.log('info', `请求成功: ${processedConfig.method || 'GET'} ${processedConfig.url}`, {
        status: response.status,
        time: Date.now() - startTime
      })

      return apiResponse

    } catch (error) {
      // 更新统计
      this.stats.total++
      this.stats.error++

      // 处理请求队列错误
      if (this.config.enableDedupe) {
        const requestKey = this.generateCacheKey(config)
        const existingRequests = this.requestQueue.get(requestKey) || []
        existingRequests.forEach(item => item.reject(error))
        this.requestQueue.delete(requestKey)
      }

      this.log('error', `请求失败: ${config.method || 'GET'} ${config.url}`, error)

      // 重试逻辑
      if (this.config.enableRetry && options.retry !== false && attempt < (options.retryCount ?? this.config.defaultRetryCount ?? 3)) {
        return this.retryRequest(config, options, error, attempt)
      }

      // 错误处理模式
      switch (this.config.errorHandlingMode) {
        case 'return':
          return {
            data: null,
            status: 0,
            statusText: 'Error',
            headers: new Headers(),
            config
          }
        case 'silent':
          this.log('debug', '静默处理错误', error)
          return {
            data: null,
            status: 0,
            statusText: 'Silent Error',
            headers: new Headers(),
            config
          }
        case 'throw':
        default:
          throw error
      }
    }
  }

  /**
   * 通用请求方法
   */
  async request<T = any>(config: RequestConfig, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.executeRequest(config, options)
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, params?: Record<string, any>, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'GET', params }, options)
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'POST', data }, options)
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'PUT', data }, options)
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'DELETE' }, options)
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'PATCH', data }, options)
  }

  /**
   * HEAD请求
   */
  async head(url: string, options?: RequestOptions): Promise<ApiResponse<void>> {
    return this.request<void>({ url, method: 'HEAD' }, options)
  }

  /**
   * 获取统计信息
   */
  getStats(): RequestStats {
    return { ...this.stats }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.log('info', '缓存已清除')
  }

  /**
   * 清除统计信息
   */
  clearStats(): void {
    this.stats = {
      total: 0,
      success: 0,
      error: 0,
      cached: 0,
      retried: 0,
      averageTime: 0,
      lastRequestTime: 0
    }
    this.log('info', '统计信息已清除')
  }
}
