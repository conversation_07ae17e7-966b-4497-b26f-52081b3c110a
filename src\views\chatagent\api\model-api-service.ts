/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// 大模型接口定义
export interface Model {
  id: string
  name: string
  model: string
  configName?: string
  provider?: string
  description?: string
  status?: string
  type?: string
  maxTokens?: number
  supportStreaming?: boolean
}

// 大模型API响应接口
export interface ModelResponse {
  success: boolean
  message?: string
  data?: Model[]
}

/**
 * 大模型API服务类
 * 负责与后端大模型相关接口进行交互
 */
export class ModelApiService {
  private static readonly BASE_URL = '/cpit/ai/setting/model'

  /**
   * 处理HTTP响应
   */
  private static async handleResponse(response: Response) {
    if (!response.ok) {
      try {
        const errorData = await response.json()
        throw new Error(errorData.message || `API请求失败: ${response.status}`)
      } catch (e) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
      }
    }
    return response
  }

  /**
   * 获取大模型简要列表
   */
  static async getBriefModelList(): Promise<Model[]> {
    try {
      const response = await fetch(`${this.BASE_URL}/brief/list`)
      const result = await this.handleResponse(response)
      const data = await result.json()
      
      // 如果返回的是包装对象，提取data字段
      if (data && typeof data === 'object' && data.data) {
        return data.data
      }
      
      // 如果直接返回数组
      if (Array.isArray(data)) {
        return data
      }
      
      return []
    } catch (error) {
      console.error('获取大模型列表失败:', error)
      throw error
    }
  }

  /**
   * 获取所有大模型列表（详细信息）
   */
  static async getAllModels(): Promise<Model[]> {
    try {
      const response = await fetch(`${this.BASE_URL}/list`)
      const result = await this.handleResponse(response)
      const data = await result.json()
      
      // 如果返回的是包装对象，提取data字段
      if (data && typeof data === 'object' && data.data) {
        return data.data
      }
      
      // 如果直接返回数组
      if (Array.isArray(data)) {
        return data
      }
      
      return []
    } catch (error) {
      console.error('获取大模型详细列表失败:', error)
      throw error
    }
  }

  /**
   * 根据模型ID获取详细信息
   */
  static async getModelById(id: string): Promise<Model | null> {
    try {
      const response = await fetch(`${this.BASE_URL}/${encodeURIComponent(id)}`)
      const result = await this.handleResponse(response)
      const data = await result.json()
      
      // 如果返回的是包装对象，提取data字段
      if (data && typeof data === 'object' && data.data) {
        return data.data
      }
      
      // 如果直接返回对象
      if (data && typeof data === 'object' && data.id) {
        return data
      }
      
      return null
    } catch (error) {
      console.error('获取大模型详情失败:', error)
      return null
    }
  }

  /**
   * 测试模型可用性
   */
  static async testModel(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.BASE_URL}/${encodeURIComponent(id)}/test`, {
        method: 'POST'
      })
      const result = await this.handleResponse(response)
      const data = await result.json()
      
      return data.success === true
    } catch (error) {
      console.error('测试大模型失败:', error)
      return false
    }
  }

  /**
   * 获取默认模型ID
   */
  static async getDefaultModelId(): Promise<string | null> {
    try {
      const response = await fetch(`${this.BASE_URL}/default`)
      const result = await this.handleResponse(response)
      const data = await result.json()
      
      if (data && data.id) {
        return data.id
      }
      
      return null
    } catch (error) {
      console.error('获取默认模型失败:', error)
      return null
    }
  }

  /**
   * 格式化模型显示名称
   */
  static formatModelDisplayName(model: Model): string {
    if (model.provider) {
      return `${model.name} (${model.provider})`
    }
    return model.name
  }

  /**
   * 检查模型是否支持流式输出
   */
  static isStreamingSupported(model: Model): boolean {
    return model.supportStreaming === true
  }

  /**
   * 根据类型过滤模型
   */
  static filterModelsByType(models: Model[], type: string): Model[] {
    return models.filter(model => model.type === type)
  }

  /**
   * 根据提供商过滤模型
   */
  static filterModelsByProvider(models: Model[], provider: string): Model[] {
    return models.filter(model => model.provider === provider)
  }
}
