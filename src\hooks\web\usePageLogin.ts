import { useRouter } from 'vue-router';
// import { useAppStoreWithOut } from '/@/stores/modules/app';
import { useUserStore } from '/@/stores/modules/user';
// const appStore = useAppStoreWithOut();
const userStore = useUserStore();
import { warpperEnv } from '@build/index';
const { VITE_LOGIN_PAGE, VITE_PROJECT_BASE_URL } = warpperEnv();
export const pageLogin = (appStore) => {
  if (appStore.projectConfig?.useLoginPage) {
    const router = useRouter();
    if (router.currentRoute.value.query.artifact) {
      userStore.pageLogin(router.currentRoute.value.query.artifact).finally(() => {});
    } else {
      window.open(
        `${VITE_LOGIN_PAGE}/login/login.shtml?callbackURL=${VITE_PROJECT_BASE_URL}/login`,
        '_self',
      );
    }
    return true;
  } else {
    return false;
  }
};
