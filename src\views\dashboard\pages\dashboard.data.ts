import { FormOptions } from '/@/components/sys/BasicForm/types';
import { getDictStorage } from '/@/utils/storage/dict';
export const moduleList: FormOptions[] = [{
    label: '系统页面',
    field: 'module',
    component: 'Select',
    componentProps: {
      options: getDictStorage().biz_module ? getDictStorage().biz_module : [],
      clearable: true,
    },
    required: false,
  }
];
export const gotoList: FormOptions[] = [{
    label: '系统页面',
    field: 'module',
    component: 'Select',
    componentProps: {
      options: getDictStorage().biz_quick ? getDictStorage().biz_quick : [],
      clearable: true,
    },
    required: false,
  }
];
