import { nextTick } from 'vue';
import { defineStore } from 'pinia';
import { store } from '/@/stores';

interface DownloadState {
  downloadUrl: string | null;
}

export const useDownloadStore = defineStore({
  id: 'download',
  state: (): DownloadState => ({
    downloadUrl: null,
  }),
  getters: {
    getDownloadUrl(): string | null {
      return this.downloadUrl;
    },
  },
  actions: {
    /**
     * 设置文件下载路径
     * @param url
     */
    setDownloadUrl(url: string | null): void {
      this.downloadUrl = url;
    },
    /**
     * 执行文件下载操作
     * @param context
     * @param url
     */
    downloadFile(url) {
      this.setDownloadUrl(null);
      nextTick(() => {
        this.setDownloadUrl(url);
      });
    },
  },
});

// Need to be used outside the setup
export function useDownStoreWidthOut() {
  return useDownloadStore(store);
}
