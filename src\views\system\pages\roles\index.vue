<template>
  <div class="role-container">
    <div class="role-header">
      <h3>角色管理</h3>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchParams" class="demo-form-inline">
        <el-form-item label="角色名称">
          <el-input
            v-model="searchParams.name"
            placeholder="请输入角色名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="角色描述">
          <el-input
            v-model="searchParams.description"
            placeholder="请输入角色描述"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus"> 新增角色 </el-button>
      <el-button
        type="danger"
        @click="handleBatchDelete"
        :icon="Delete"
        :disabled="selectedIds.length === 0"
      >
        批量删除
      </el-button>
      <el-button type="info" @click="handleRefresh" :icon="Refresh"> 刷新 </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="50" />
        <el-table-column prop="name" label="角色名称" show-overflow-tooltip />
        <el-table-column prop="description" label="角色描述" show-overflow-tooltip />
        <el-table-column prop="sort" label="显示顺序" show-overflow-tooltip />
        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="handleAssignUsers(row)">
              分配用户
            </el-button>
            <el-button type="success" link size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" link size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 角色编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input v-model="formData.description" placeholder="请输入角色描述" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态">
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item label="分配权限" prop="menus">
          <el-input
            v-model="filterText"
            placeholder="请输入权限名称"
            style="margin-bottom: 10px"
          />
          <div style="margin-bottom: 10px">
            <el-button size="small" type="primary" @click="handleCheckAll"
              >全选/全不选</el-button
            >
            <el-button
              size="small"
              type="danger"
              @click="handleExpand"
              style="margin-left: 10px"
              >展开/折叠</el-button
            >
          </div>
          <el-tree
            v-if="showMenusTree"
            ref="menuTreeRef"
            class="filter-tree"
            style="
              height: 200px;
              overflow: auto;
              border: 1px solid #dcdfe6;
              padding: 10px;
              background: #fafafa;
            "
            :data="formartMenusTree"
            node-key="id"
            show-checkbox
            :check-strictly="false"
            :props="defaultProps"
            :default-expand-all="isExpand"
            :default-checked-keys="checkedMenuIds"
            :filter-node-method="filterMenusTree"
            @check="handleMenuCheck"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 分配用户对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      title="分配用户"
      width="800px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-transfer
        v-model="selectedUserIds"
        :data="userOptions"
        :titles="['未授权用户', '已授权用户']"
        :button-texts="['取消', '添加']"
        :props="{ key: 'id', label: 'username' }"
        filterable
        filter-placeholder="请输入用户名"
        style="text-align: left; display: inline-block"
      />
      <template #footer>
        <el-button @click="userDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitUsers" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick, watch } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus, Refresh, Delete } from '@element-plus/icons-vue';
  import {
    getRolePageList,
    saveRole,
    updateRole,
    deleteRoles,
    type RoleInfo,
    type RoleQueryParams
  } from '/@/api/sys/roles';
  import { getMenuList } from '/@/api/sys/menus';

  // 响应式数据
  const tableRef = ref();
  const formRef = ref();
  const menuTreeRef = ref();

  const tableData = ref<RoleInfo[]>([]);
  const loading = ref(false);
  const submitLoading = ref(false);
  const selectedIds = ref<string[]>([]);

  // 权限树相关
  const filterText = ref('');
  const showMenusTree = ref(true);
  const isExpand = ref(false);
  const formartMenusTree = ref<any[]>([]);
  const checkedMenuIds = ref<string[]>([]);
  const defaultProps = {
    children: 'children',
    label: 'title'
  };

  // 分配用户相关
  const userDialogVisible = ref(false);
  const selectedUserIds = ref<string[]>([]);
  const userOptions = ref<any[]>([]);

  const currentRoleId = ref<string>('');

  // 分页数据
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 对话框状态
  const dialogVisible = ref(false);
  const dialogTitle = ref('');
  const isEdit = ref(false);

  // 表单数据
  const formData = ref<RoleInfo>({
    name: '',
    description: '',
    sort: 0,
    status: '0',
    remark: '',
  });

  // 表单验证规则
  const formRules = {
    name: [
      { required: true, message: '请输入角色名称', trigger: 'blur' }
    ],
    description: [
      { required: true, message: '请输入角色描述', trigger: 'blur' }
    ],
    status: [
      { required: true, message: '请选择状态', trigger: 'change' }
    ]
  };

  // 搜索参数
  const searchParams = ref<RoleQueryParams>({});

  // 表格选择变化
  const handleSelectionChange = (selection: RoleInfo[]) => {
    selectedIds.value = selection.map(item => item.id!);
  };

  // 监听过滤文本变化
  watch(filterText, (val) => {
    if (menuTreeRef.value) {
      menuTreeRef.value.filter(val);
    }
  });

  // 生命周期
  onMounted(() => {
    loadRoleList();
  });

  // 加载角色列表
  const loadRoleList = async () => {
    try {
      loading.value = true;
      const params = {
        ...searchParams.value,
        currentPage: pagination.current,
        pageSize: pagination.pageSize,
      };

      const result = await getRolePageList(params);
      if (result && result.data) {
        tableData.value = result.data;
        pagination.total = result.total;
      }
    } catch (error) {
      ElMessage.error('加载角色列表失败');
      console.error('Load role list error:', error);
    } finally {
      loading.value = false;
    }
  };

  // 搜索处理
  const handleSearch = () => {
    pagination.current = 1;
    loadRoleList();
  };

  // 重置搜索
  const handleReset = () => {
    searchParams.value = {};
    pagination.current = 1;
    loadRoleList();
  };

  // 刷新
  const handleRefresh = () => {
    loadRoleList();
  };

  // 分页处理
  const handlePageChange = (page: number) => {
    pagination.current = page;
    loadRoleList();
  };

  const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.current = 1;
    loadRoleList();
  };

  // 初始化权限树
  const initMenuTree = async () => {
    try {
      const result = await getMenuList();
      console.log('菜单数据:', result);
      if (result) {
        formartMenusTree.value = handleMenuTree(result);
        console.log('格式化后的菜单树:', formartMenusTree.value);
      }
    } catch (error) {
      console.error('Load menu tree error:', error);
    }
  };

  // 树形数据转换函数
  const handleMenuTree = (data: any[]): any[] => {
    console.log('handleMenuTree 输入数据:', data);

    if (!data || !Array.isArray(data)) {
      console.log('数据为空或不是数组');
      return [];
    }

    // const tree: any[] = [];
    const childrenMap: { [key: string]: any[] } = {};

    // 构建子节点映射
    for (const item of data) {
      const pid = String(item.parentId || 0);
      if (!childrenMap[pid]) {
        childrenMap[pid] = [];
      }
      childrenMap[pid].push(item);
    }

    console.log('子节点映射:', childrenMap);

    // 递归构建树形结构
    const buildTree = (pid: string): any[] => {
      const children = childrenMap[pid] || [];
      return children.map(item => {
        const node = {
          ...item,
          id: String(item.id),
          title: item.title || item.name || item.menuName || '未命名菜单'
        };
        const childNodes = buildTree(String(item.id || ''));
        if (childNodes.length > 0) {
          node.children = childNodes;
        }
        return node;
      });
    };

    // 从根节点开始构建（parentId为0或null的节点）
    const result = buildTree('0');
    console.log('构建的树形结构:', result);
    return result;
  };

  // 新增角色
  const handleAdd = async () => {
    dialogTitle.value = '新增角色';
    isEdit.value = false;
    formData.value = {
      name: '',
      description: '',
      sort: 0,
      status: '0',
      remark: '',
    };
    checkedMenuIds.value = [];
    await initMenuTree();
    dialogVisible.value = true;
  };

  // 编辑角色
  const handleEdit = async (record: RoleInfo) => {
    dialogTitle.value = '编辑角色';
    isEdit.value = true;
    formData.value = { ...record };

    console.log('编辑角色数据:', record);

    await initMenuTree();
    dialogVisible.value = true;

    // 等待树渲染完成后设置选中状态
    nextTick(() => {
      setTimeout(() => {
        if (menuTreeRef.value && formartMenusTree.value.length > 0) {
          // 如果是admin角色，默认选中所有权限
          if (record.name === 'admin') {
            const allKeys = getAllNodeKeys(formartMenusTree.value);
            console.log('admin角色默认选中所有权限:', allKeys);
            menuTreeRef.value.setCheckedKeys(allKeys);
            checkedMenuIds.value = allKeys;
          } else {
            // 其他角色暂时也选中所有权限作为演示
            const allKeys = getAllNodeKeys(formartMenusTree.value);
            console.log('角色选中权限:', allKeys);
            menuTreeRef.value.setCheckedKeys(allKeys);
            checkedMenuIds.value = allKeys;
          }
        }
      }, 800);
    });
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const valid = await formRef.value?.validate();
      if (!valid) return;

      submitLoading.value = true;

      // 获取选中的菜单权限
      const checkedKeys = menuTreeRef.value?.getCheckedKeys() || [];
      const halfCheckedKeys = menuTreeRef.value?.getHalfCheckedKeys() || [];
      const menusIds = [...checkedKeys, ...halfCheckedKeys];

      const roleData = {
        ...formData.value,
        menusIds
      };

      if (isEdit.value) {
        await updateRole(roleData);
        ElMessage.success('更新角色成功');
      } else {
        await saveRole(roleData);
        ElMessage.success('新增角色成功');
      }

      dialogVisible.value = false;
      loadRoleList();
    } catch (error) {
      ElMessage.error(isEdit.value ? '更新角色失败' : '新增角色失败');
      console.error('Submit role error:', error);
    } finally {
      submitLoading.value = false;
    }
  };

  // 删除角色
  const handleDelete = async (record: RoleInfo) => {
    try {
      await ElMessageBox.confirm('确定要删除该角色吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      await deleteRoles(record.id!);
      ElMessage.success('删除角色成功');
      loadRoleList();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除角色失败');
        console.error('Delete role error:', error);
      }
    }
  };

  // 批量删除角色
  const handleBatchDelete = async () => {
    try {
      await ElMessageBox.confirm(`确定要删除选中的 ${selectedIds.value.length} 个角色吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      await deleteRoles(selectedIds.value.join(','));
      ElMessage.success('批量删除角色成功');
      selectedIds.value = [];
      loadRoleList();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('批量删除角色失败');
        console.error('Batch delete roles error:', error);
      }
    }
  };

  // 分配用户
  const handleAssignUsers = async (record: RoleInfo) => {
    try {
      currentRoleId.value = record.id!;
      // TODO: 加载所有用户和角色已有用户
      // const allUsers = await getAllUsers();
      // const roleUsers = await getRoleUsers(record.id!);

      // 模拟数据，实际应该从API获取
      userOptions.value = [
        { id: '1', username: 'admin', key: '1' },
        { id: '2', username: 'user', key: '2' },
      ];
      selectedUserIds.value = ['1']; // 模拟角色已有用户

      userDialogVisible.value = true;
    } catch (error) {
      ElMessage.error('加载用户信息失败');
      console.error('Load users error:', error);
    }
  };



  // 提交用户分配
  const handleSubmitUsers = async () => {
    try {
      submitLoading.value = true;
      // TODO: 调用分配用户API
      // await assignRoleUsers(currentRoleId.value, selectedUserIds.value);

      ElMessage.success('分配用户成功');
      userDialogVisible.value = false;
    } catch (error) {
      ElMessage.error('分配用户失败');
      console.error('Assign users error:', error);
    } finally {
      submitLoading.value = false;
    }
  };

  // 权限树相关方法
  const filterMenusTree = (value: string, data: any) => {
    if (!value) return true;
    return data.title.indexOf(value) !== -1;
  };

  const handleMenuCheck = (data: any, checked: any) => {
    // 菜单选择变化处理
    console.log('Menu check changed:', data, checked);
  };

  const handleCheckAll = () => {
    const tree = menuTreeRef.value;
    if (!tree) return;

    const checkedKeys = tree.getCheckedKeys();
    if (checkedKeys && checkedKeys.length > 0) {
      tree.setCheckedKeys([]);
      checkedMenuIds.value = [];
    } else {
      const allKeys = getAllNodeKeys(formartMenusTree.value);
      tree.setCheckedKeys(allKeys);
      checkedMenuIds.value = allKeys;
    }
  };

  const getAllNodeKeys = (nodes: any[]): string[] => {
    let keys: string[] = [];
    nodes.forEach(node => {
      keys.push(String(node.id));
      if (node.children && node.children.length > 0) {
        keys = keys.concat(getAllNodeKeys(node.children));
      }
    });
    return keys;
  };

  const handleExpand = () => {
    isExpand.value = !isExpand.value;
    showMenusTree.value = false;
    nextTick(() => {
      showMenusTree.value = true;
    });
  };
</script>

<style scoped lang="scss">
  .role-container {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .role-header {
      margin-bottom: 24px;
      padding-bottom: 12px;
      border-bottom: 2px solid #f0f2f5;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .search-section {
      margin-bottom: 20px;
      padding: 20px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: 8px;
      border: 1px solid #e2e8f0;
    }

    .action-section {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .table-section {
      background: #fff;
      border-radius: 8px;
      overflow: hidden;

      .pagination-section {
        margin-top: 20px;
        padding: 16px 0;
        display: flex;
        justify-content: flex-end;
        background: #fafbfc;
        border-top: 1px solid #e2e8f0;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }
</style>
