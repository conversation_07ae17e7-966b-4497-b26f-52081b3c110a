$headerHeight: 100px;

:root {
  --el-menu-item-height: 46px;
  --el-menu-icon-width: ''; // 删除 menu icon的宽度限制
  --el-menu-base-level-padding: 16px;
  --el-header-height: $headerHeight;
  //--el-menu-hover-bg-color: var(--el-color-primary);
  //--el-menu-hover-bg-color: '#409eff' !important;
}
.el-menu {
  background-color: var(--el-menu-bg-color-theme) !important;
}

.sidebar-container {
  background: var(--el-menu-bg-color-theme) !important;
}
.nav-theme {
  // background: var(--sys-nav-bg-color-theme)
  background-color: #f9fafd !important;
}
.temp-page {
  background: var(--sys-content-bg-color-theme) !important;
}
