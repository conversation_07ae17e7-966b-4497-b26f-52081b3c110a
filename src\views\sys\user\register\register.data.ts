import { ref } from 'vue';
import { FormOptions } from '/@/components/sys/BasicForm/types';
import { checkApi } from '/@/views/upms/api/user';
import { getDictStorage } from '/@/utils/storage/dict';
const phoneReg =
  /^((\+86|0086)?\s*)((134[0-8]\d{7})|(((13([0-3]|[5-9]))|(14[5-9])|15([0-3]|[5-9])|(16(2|[5-7]))|17([0-3]|[5-8])|18[0-9]|19(1|[8-9]))\d{8})|(14(0|1|4)0\d{7})|(1740([0-5]|[6-9]|[10-12])\d{7}))$/;
const card =
  /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/;

const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
export const formData = ref({
  id: '',
  employeeId: '',
  available: '1',
  userPasswd: '',
  roleIdList: [],
  userName: '',
  postStationIdList: [],
  deptName: '',
  deptId: [],
  gender: '',
  email: '',
  industryType: '',
  phone: '',
  empName: '',
  instCode: '',
  empCode: '',
  empPost: '',
  empType: '',
  nodeCode: '',
  serviceCode: '',
  sortOrder: '',
  status: '01',
});
export const FormSchema: FormOptions[] = [
  // Text
  {
    field: 'deptId',
    label: '机构',
    component: 'Text',
    slot: 'deptId',
  },
  {
    field: 'userName',
    label: '账号名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入账号名',
      disabled: false,
    },
    slot: 'userName',
    required: true,
    rules: [
      { max: 30, message: '超过长度限制，最多30字' },
      // {
      //   validator: (_, value) => {
      //     console.log('formData', formData.value)
      //     if (sensitiveWords.length > 0) {
      //       const pattern = new RegExp(sensitiveWords.join('|'), 'i');
      //       if (pattern.test(value)) {
      //         return Promise.reject('用户名中不能包含敏感词汇！');
      //       }
      //     }
      //     return new Promise<void>((resolve, reject) => {
      //       checkApi({
      //         id: formData.value.id,
      //         userName: value,
      //       }).then((res) => {
      //         res ? resolve() : reject(res.message || '账号名已存在');
      //       });
      //     });
      //   },
      // },
    ],
  },
  {
    field: 'userPasswd',
    label: '登录密码',
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入新密码',
    },
    dynamicRules: [
      // {
      //   validator: (_, value) => {
      //     for (let i = 0; i < passwordRegArr.length; i++) {
      //       //  密码长度
      //       if (passwordRegArr[i].code === 'biz_password_reg_length') {
      //         let lengthArr: any = [];
      //         if (!!passwordRegArr[i].value) {
      //           lengthArr = passwordRegArr[i].value.split(',');
      //         } else {
      //           lengthArr = [12, 32];
      //         }
      //         const regex = new RegExp(
      //           '^.{' + lengthArr[0] + ',' + lengthArr[1] + '}$',
      //         );
      //         if (!regex.test(value)) {
      //           return Promise.reject(passwordRegArr[i].label);
      //         }
      //       }
      //       // 不能输入连续3位或3位以上相同字符
      //       if (passwordRegArr[i].code === 'biz_password_reg_same_characters') {
      //         const regex = /(\w)*(\w)\2{2}(\w)*/g;
      //         if (regex.test(value)) {
      //           return Promise.reject(passwordRegArr[i].label);
      //         }
      //       }
      //       // 不能输入连续字符  如123，abc等
      //       if (passwordRegArr[i].code === 'biz_password_reg_adjacent_characters') {
      //         const arr = value.split('');
      //         let flag = true;
      //         for (let i = 1; i < arr.length - 1; i++) {
      //           const fIndex = arr[i - 1].charCodeAt();
      //           const sIndex = arr[i].charCodeAt();
      //           const tIndex = arr[i + 1].charCodeAt();
      //           if (tIndex - sIndex === 1 && sIndex - fIndex === 1) {
      //             flag = false;
      //           }
      //         }
      //         if (!flag) {
      //           return Promise.reject(passwordRegArr[i].label);
      //         }
      //       }
      //       // 不能输入键盘连续3位或3位以上相同字符
      //       if (passwordRegArr[i].code === 'biz_password_reg_continuous_keyboard') {
      //         if (checkKeyboardContinuousChar(value)) {
      //           return Promise.reject(passwordRegArr[i].label);
      //         }
      //       }
      //       // 不能包含用户名
      //       if (passwordRegArr[i].code === 'biz_password_reg_include_username') {
      //         if (formData.value.userName.length > 0) {
      //           const userNameArr = [formData.value.userName];
      //           const pattern = new RegExp(userNameArr.join('|'), 'i');
      //           if (pattern.test(value)) {
      //             return Promise.reject(passwordRegArr[i].label);
      //           }
      //         }
      //         //
      //       }
      //       // 请输入大小写、数字、特殊字符俩种及以上组合
      //       if (passwordRegArr[i].code === 'biz_password_reg_combination') {
      //         const num = passwordRegArr[i].value ? passwordRegArr[i].value : '2';
      //         if (!checkPasswordStrength(value, num)) {
      //           return Promise.reject(passwordRegArr[i].label);
      //         }
      //       }
      //     }
      //     return Promise.resolve();
      //   },
      // },
    ],
    ifShow: true,
    required: true,
  },
  // {
  //   field: 'avatar',
  //   label: '头像',
  //   component: 'Input',
  //   slot: 'avatar',
  // },
  {
    label: '账号状态',
    field: 'available',
    component: 'RadioGroup',
    componentProps: {
      options: [
        {
          label: '已激活',
          value: '1',
        },
        {
          label: '已禁用',
          value: '0',
        },
        {
          label: '已锁定',
          value: '2',
        },
      ],
    },
    required: true,
  },
  {
    field: 'empName',
    label: '员工姓名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入姓名',
      disabled: false,
    },
    rules: [
      { max: 90, message: '超过长度限制，最多30字' },
      { min: 2, message: '少于长度限制，最少2字' },
    ],
    required: true,
  },

  {
    label: '手机号',
    field: 'phone',
    component: 'Input',
    componentProps: {
      placeholder: '请输入手机号',
    },
    rules: [
      {
        pattern: phoneReg,
        message: '请输入正确的手机号',
      },
      {
        validator: (rule, value) => {
          if (!value) {
            /* eslint-disable-next-line */
            return true;
          }
          return new Promise<void>((resolve, reject) => {
            const params = {
              type: 2,
              value: value,
            };
            checkApi(params).then((res) => {
              res ? resolve() : reject(res.message || '手机号码已存在');
            });
          });
        },
      },
    ],
    required: true,
  },
  {
    label: '电子邮箱',
    field: 'email',
    component: 'Slot',
    slot: 'autocomplete',
    rules: [
      {
        trigger: 'blur',
        pattern: emailReg,
        message: '请输入正确的电子邮箱',
      },
      { max: 100, message: '超过长度限制，最多100字' },
      {
        validator: (rule, value) => {
          if (!value) {
            /* eslint-disable-next-line */
            return true;
          }
          return new Promise<void>((resolve, reject) => {
            const params = {
              type: 0,
              value: value,
            };
            checkApi(params).then((res) => {
              res ? resolve() : reject(res.message || '电子邮箱已存在');
            });
          });
        },
      },
    ],
    required: true,
  },
  {
    label: '员工状态',
    field: 'status',
    component: 'RadioGroup',
    componentProps: {
      options: getDictStorage()?.biz_status ? getDictStorage().biz_status : [],
    },
    required: true,
  },
  {
    label: '身份证号',
    field: 'idCard',
    component: 'Input',
    required: true,
    rules: [
      { pattern: card, message: '请输入正确的身份证号' },
      {
        validator: (rule, value) => {
          if (!value) {
            /* eslint-disable-next-line */
            return true;
          }
          return new Promise<void>((resolve, reject) => {
            const params = {
              type: 1,
              value: value,
            };
            checkApi(params).then((res) => {
              res ? resolve() : reject(res.message || '身份证号码已存在');
            });
          });
        },
      },
    ],
  },
];
