Stack trace:
Frame         Function      Args
0007FFFFBE20  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFAD20) msys-2.0.dll+0x2118E
0007FFFFBE20  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x69BA
0007FFFFBE20  0002100469F2 (00021028DF99, 0007FFFFBCD8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBE20  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBE20  00021006A545 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFC100  00021006B9A5 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD64000000 ntdll.dll
7FFD628D0000 KERNEL32.DLL
7FFD61570000 KERNELBASE.dll
7FFD1D350000 ghijt64win10.dll
7FFD61F60000 ADVAPI32.dll
7FFD629A0000 msvcrt.dll
7FFD62820000 sechost.dll
7FFD63140000 RPCRT4.dll
7FFD63DE0000 USER32.dll
7FFD61CD0000 win32u.dll
7FFD63B40000 GDI32.dll
7FFD61960000 gdi32full.dll
000210040000 msys-2.0.dll
7FFD61AA0000 msvcp_win.dll
7FFD612D0000 ucrtbase.dll
7FFD60750000 CRYPTBASE.DLL
7FFD61D00000 bcryptPrimitives.dll
7FFD62D00000 IMM32.DLL
