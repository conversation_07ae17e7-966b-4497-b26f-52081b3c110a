import {
  checkKeyboardContinuousChar,
  checkPasswordStrength,
} from '/@/utils/tools/passwordTools';
import { getAuthStorage } from '/@/utils/storage/auth';
import { getDictStorage } from '/@/utils/storage/dict';
const passwordRegArr = getDictStorage()?.biz_password_reg;
export const PwdCheck = (pwdStr: string) => {
  const value = pwdStr.toLowerCase();
  for (let i = 0; i < passwordRegArr.length; i++) {
    //  密码长度
    if (passwordRegArr[i].code === 'biz_password_reg_length') {
      let lengthArr: any = [];
      if (!!passwordRegArr[i].value) {
        lengthArr = passwordRegArr[i].value.split(',');
      } else {
        lengthArr = [12, 32];
      }
      const regex = new RegExp('^.{' + lengthArr[0] + ',' + lengthArr[1] + '}$');
      if (!regex.test(value)) {
        return passwordRegArr[i].label;
      }
    }
    // 不能输入连续3位或3位以上相同字符
    if (passwordRegArr[i].code === 'biz_password_reg_same_characters') {
      const regex = /(\w)*(\w)\2{2}(\w)*/g;
      if (regex.test(value)) {
        return passwordRegArr[i].label;
      }
    }
    // 不能输入连续字符  如123，abc等
    if (passwordRegArr[i].code === 'biz_password_reg_adjacent_characters') {
      const arr = value.split('');
      let flag = true;
      for (let i = 1; i < arr.length - 1; i++) {
        const fIndex = arr[i - 1].charCodeAt(0);
        const sIndex = arr[i].charCodeAt(0);
        const tIndex = arr[i + 1].charCodeAt(0);
        if (tIndex - sIndex === 1 && sIndex - fIndex === 1) {
          flag = false;
        }
      }
      if (!flag) {
        return passwordRegArr[i].label;
      }
    }

    // 不能输入键盘连续3位或3位以上相同字符
    if (passwordRegArr[i].code === 'biz_password_reg_continuous_keyboard') {
      if (checkKeyboardContinuousChar(value)) {
        return passwordRegArr[i].label;
      }
    }
    // 不能包含用户名
    if (passwordRegArr[i].code === 'biz_password_reg_include_username') {
      const userNameArr: any[] = [getAuthStorage()?.userName];
      const pattern = new RegExp(userNameArr.join('|'), 'i');
      if (pattern.test(value)) {
        return passwordRegArr[i].label;
      }
    }

    // 请输入大小写、数字、特殊字符俩种及以上组合
    if (passwordRegArr[i].code === 'biz_password_reg_combination') {
      const num = passwordRegArr[i].value ? passwordRegArr[i].value : '2';
      if (!checkPasswordStrength(value, num)) {
        return passwordRegArr[i].label;
      }
    }

    // 密码不能包含敏感词
    if (passwordRegArr[i].code === 'biz_password_reg_sensitive_words') {
      const pattern = new RegExp(passwordRegArr[i].value, 'i');
      if (pattern.test(value)) {
        return passwordRegArr[i].label;
      }
    }
  }
  return null;
};
