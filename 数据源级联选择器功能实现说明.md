# 数据源级联选择器功能实现说明

## 需求描述
对数据源接口type字段返回的值为"POSTGRESQL"和"KINGBASE"可以选择到第三级模式，其他的类型选择到二级数据库。

## 实现方案

### 1. 核心逻辑修改

在 `src/views/chatagent/components/DataSourceCascader.vue` 文件中实现了以下核心功能：

#### 1.1 添加计算属性 `needsSchemaSelection`
```typescript
// 判断是否需要选择模式（只有POSTGRESQL和KINGBASE类型需要选择模式）
const needsSchemaSelection = computed(() => {
  if (!selectedDataSource.value) return false
  const type = selectedDataSource.value.type?.toUpperCase()
  return type === 'POSTGRESQL' || type === 'KINGBASE'
})
```

#### 1.2 修改显示值计算逻辑
```typescript
const displayValue = computed(() => {
  if (selectedDataSource.value && selectedDatabase.value && selectedSchema.value) {
    return `${selectedDataSource.value.name}/${selectedDatabase.value.name}/${selectedSchema.value.name}`
  }
  if (selectedDataSource.value && selectedDatabase.value && !needsSchemaSelection.value) {
    return `${selectedDataSource.value.name}/${selectedDatabase.value.name}`
  }
  return ''
})
```

### 2. UI界面修改

#### 2.1 数据库面板箭头显示控制
```vue
<Icon 
  v-if="needsSchemaSelection" 
  icon="carbon:chevron-right" 
  class="item-arrow" 
/>
```
- 只有需要选择模式的数据源类型才显示右箭头
- 其他类型不显示箭头，表示这是最后一级选择

#### 2.2 模式面板显示控制
```vue
<!-- 模式面板 (只有POSTGRESQL和KINGBASE类型才显示) -->
<div class="cascader-panel" v-if="selectedDatabase && needsSchemaSelection">
```
- 只有当选择了数据库且需要模式选择时才显示模式面板

### 3. 选择逻辑修改

#### 3.1 修改 `selectDatabase` 方法
```typescript
const selectDatabase = async (database: Database) => {
  selectedDatabase.value = database
  selectedSchema.value = null
  schemaList.value = []

  // 如果不需要选择模式，直接完成选择
  if (!needsSchemaSelection.value) {
    const value = {
      dataSourceId: selectedDataSource.value?.id,
      dataSourceName: selectedDataSource.value?.name,
      databaseName: database.name,
      schemaName: undefined
    }

    emit('update:modelValue', value)
    showDropdown.value = false
    return
  }

  // 需要选择模式的情况下，加载模式列表
  if (selectedDataSource.value) {
    schemaLoading.value = true
    try {
      schemaList.value = await AxiosApiService.getSchemaList(
        selectedDataSource.value.id, 
        database.name
      )
    } catch (error) {
      console.error('加载模式失败:', error)
    } finally {
      schemaLoading.value = false
    }
  }
}
```

## 功能特性

### 1. 支持的数据源类型
- **POSTGRESQL**: 支持三级选择（数据源 → 数据库 → 模式）
- **KINGBASE**: 支持三级选择（数据源 → 数据库 → 模式）
- **其他类型** (MYSQL, ORACLE, 等): 支持二级选择（数据源 → 数据库）

### 2. 用户体验优化
- **视觉提示**: 只有支持模式选择的数据源在数据库项右侧显示箭头
- **自动完成**: 不支持模式选择的数据源在选择数据库后自动完成选择并关闭下拉框
- **数据完整性**: 根据数据源类型返回相应的数据结构

### 3. 返回数据结构
- **三级选择**: `{ dataSourceId, dataSourceName, databaseName, schemaName }`
- **二级选择**: `{ dataSourceId, dataSourceName, databaseName, schemaName: undefined }`

## 测试验证

创建了测试文件 `test-datasource-cascader.html` 来验证核心逻辑：
- ✅ POSTGRESQL 类型: needsSchemaSelection = true
- ✅ KINGBASE 类型: needsSchemaSelection = true  
- ✅ MYSQL 类型: needsSchemaSelection = false
- ✅ ORACLE 类型: needsSchemaSelection = false

## 兼容性说明

1. **向后兼容**: 现有的三级选择功能保持不变
2. **类型安全**: 使用 TypeScript 确保类型检查
3. **大小写不敏感**: 使用 `toUpperCase()` 进行类型比较，确保兼容性

## 使用示例

```vue
<template>
  <DataSourceCascader
    v-model="dataSourceSelection"
    @update:modelValue="onDataSourceSelectionChange"
  />
</template>

<script setup>
const dataSourceSelection = ref({})

const onDataSourceSelectionChange = (value) => {
  console.log('选择的数据源:', value)
  // 对于 POSTGRESQL/KINGBASE: { dataSourceId: 1, dataSourceName: "PG数据源", databaseName: "mydb", schemaName: "public" }
  // 对于其他类型: { dataSourceId: 2, dataSourceName: "MySQL数据源", databaseName: "mydb", schemaName: undefined }
}
</script>
```

## 总结

此实现完全满足需求，通过类型判断实现了差异化的级联选择行为，提供了良好的用户体验和清晰的视觉反馈。代码结构清晰，易于维护和扩展。
