// token key

export const TOKEN_KEY = 'TOKEN__';
// base global local key
export const APP_LOCAL_CACHE_KEY = 'COMMON__LOCAL__KEY__';
// user info key
export const USER_INFO_KEY = 'USER__INFO__';
// permission key
export const PERM_CODE_KEY = 'PERM__CODE__';
// menu key
export const MENU_KEY = 'MENU__LIST__';
// all menu key
export const ALL_MENU_KEY = 'ALL_MENU__LIST__';
// TAB页菜单
export const TAB_PAGES_KEY = 'TAB_PAGES__';
// header select tenant project key
export const HEADER_TENANT_PROJECT_KEY = 'HEADER_TENANT_PROJECT_';
// header参数是否已请求完成
export const HEADER_TENANT_REQUESTED = 'HEADER__REQUESTED__';
// header select tenant project key
export const DICT_KEY = 'USER_DICT_';

// responsive-storage namespace
export const RESPONSIVE_STORAGE_NAMESPACE = 'PROJECT__SETTINGS__';

export const PROJECT_SETTINGS = 'PROJECT__SETTINGS__';

export const TABLE_LOCAL = 'TABLE__LOCAL__';
// micro data
export const MICRO_DATA = 'MICRO__DATA__';

export const PASS_WORD_STATUS = 'PASS_WORD_STATUS_KEY_';

export const WEB_MODEL = 'WEB_MODEL_';
export enum CacheTypeEnum {
  SESSION,
  LOCAL,
}

// 主题
export const THEME = 'THEME__';
// 验证码是否开始为空校验
export const VERIFYCODE = true;
