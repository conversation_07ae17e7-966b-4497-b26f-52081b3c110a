# mock

1. 使用mockjs和vite-plugin-mock开发。
2. 当mock接口url与真实接口url一致时，会自动使用mock数据。
3. 为避免不必要的数据替换，可在vite.config中配置开发环境不打包mock，或者使用替换模式。

## 替换模式

从后台api真实数据,替换成mock数据。需要做以下修改
1. mock接口的url前缀需要是 '/basic-api/*****', 与env的VITE_APP_BASE_URL_PREFIX 保持一致。
2. 修改env文件的VITE_PROXY代理，前缀与env的VITE_APP_BASE_URL_PREFIX 保持一致。


## 补充模式

即使用后台api，也使用mock，mock的一种补充，此时需要保证：
1. mock接口的url前缀需要是 '/api/*****', 与env的VITE_APP_BASE_URL_PREFIX 保持一致。
2. 修改env文件的VITE_PROXY代理，前缀与env的VITE_APP_BASE_URL_PREFIX 保持一致。
3. 注意mock接口的url，不能与已知的后台接口重复。