/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
import { defHttp } from '/@/utils/axios';

enum Api {
  datasourceList = '/api/connection/datasource/list?pageNo=1&pageSize=1000&refresh=true',
  environment = '/api/common/environment/list_all',
  driver = '/api/jdbc/driver/list',
  createDatasource = '/api/connection/datasource/create',
  updateDatasource = '/api/connection/datasource/update',
  datasource = '/api/connection/datasource',
  cloneDatasource = '/api/connection/datasource/clone',
  sshConnect = '/api/connection/ssh/pre_connect',
  datasourceConnect = '/api/connection/datasource/pre_connect',
  driverSave = '/api/jdbc/driver/save',
}

export const getDatasourceApi = () => defHttp.get({ url: `${Api.datasourceList}` });
export const getEnvironmentApi = () => defHttp.get({ url: `${Api.environment}` });
// api/jdbc/driver/list?dbType=MYSQL

// export const getDriverListApi = (id: string) =>
//   defHttp.get({ url: `${Api.driver}/${id}` });

export const getDriverListApi = (params) => defHttp.get({ url: Api.driver, params });

export const createDatasourceApi = (params) =>
  defHttp.post({ url: Api.createDatasource, params });

export const updateDatasourceApi = (params) =>
  defHttp.post({ url: Api.updateDatasource, params });

export const deleteDatasourceApi = (id: string) =>
  defHttp.delete({ url: `${Api.datasource}/${id}` });

export const cloneDatasourceApi = (params) =>
  defHttp.post({ url: Api.cloneDatasource, params });
export const sshConnectApi = (params) => defHttp.post({ url: Api.sshConnect, params });
// datasourceConnect

export const datasourceConnectApi = (params) =>
  defHttp.post({ url: Api.datasourceConnect, params });

export const getByIdAiSourceApi = (id: string) =>
  defHttp.get({ url: `${Api.datasource}/${id}` });
// datasource    /api/jdbc/driver/save

export const driverSaveApi = (params) => defHttp.post({ url: Api.driverSave, params });
