<template>
  <header>
    <div
      class="
        flex
        items-center
        px-4
        h-full
        bg-white
        border-solid border-light-blue-500
        nav-theme
      "
    >
      <sys-icon
        :type="getMenuCollapsed ? 'Expand' : 'Fold'"
        :size="18"
        @click="toggleCollapsed"
      />
      <span class="flex-1 pl-3">
        <sys-breadcrumbs :breads="breads" />
      </span>

      <LoginOut v-if="!isHorizontal" />
      <!-- <span class="el-icon-setting" title="设置" @click="drawer = !drawer">
        <el-icon><Setting /></el-icon>
      </span> -->
      <el-drawer v-model="drawer" direction="rtl" :modal="false" close-on-click-modal>
        <theme />
      </el-drawer>
    </div>

    <!-- <div class="border-b bg-white !flex justify-between h-10 scroll-pane">
      <ScrollPane v-if="appStore.getProjectConfig.useTab" />
    </div> -->
  </header>
</template>

<script lang="ts" setup name="DefaultHeader">
  import LoginOut from './Logout.vue';
  // import ScrollPane from './ScrollPane.vue';
  import { ref, watchEffect } from 'vue';
  import { useMenuSettings } from '/@/hooks/settings/useMenuSettings';
  import SysBreadcrumbs from '/@/components/sys/Breadcrumbs';
  import { useRouter } from 'vue-router';
  import { usePermissionStore } from '/@/stores/modules/permission';
  import { getAllParentMenus } from '/@/router/tools/menuTools';
  import { cloneDeep } from 'lodash-es';
  import { I18nTypeEnum, ThemeTypeEnum } from '/@/enums/appEnum';
  import { Menu } from '/@/stores/types';
  import theme from './theme/index.vue';
  // import { useAppStore } from '/@/stores/modules/app';
  // 租户相关的处理
  // const appStore = useAppStore();
  // 菜单折叠，切换横竖布局
  const { toggleCollapsed, getMenuCollapsed, isHorizontal } = useMenuSettings();
  // 处理getMenuCollapsed的错误信息 => All destructured elements are unused.
  const { currentRoute } = useRouter();
  const permissionStore = usePermissionStore();
  const menus = permissionStore.menuList;
  const breads = ref<Menu[]>([]);

  watchEffect(async () => {
    breads.value = getAllParentMenus(cloneDeep(menus), currentRoute.value.path);
  });
  const drawer = ref(false);
  defineExpose({
    getMenuCollapsed,
    I18nTypeEnum,
    ThemeTypeEnum,
  });
</script>

<style scoped>
  :deep(.el-drawer__header) {
    margin-bottom: -10px;
  }

  :deep(.el-drawer__header, .el-dialog__header) {
    border: unset !important;
  }

  .scroll-pane {
    height: 47px;
    border-bottom: solid 1px #e5e7eb;
    box-shadow: 0 10px white;
  }

  .el-icon-msg {
    margin-right: 20px;
  }
  /* .el-icon-setting {
    margin-right: 20px;
  } */
</style>
