export interface SearchOptions {
  label: string;
  field: string;
  span?: number;
  width?: number | string;
  component?:
    | 'Input'
    | 'InputNumber'
    | 'DatePicker'
    | 'DatePickerTime'
    | 'DateRangePicker'
    | 'Select'
    | 'SelectMultiple';
  labelWidth?: number;
  placeholder?: string;
  clearable?: boolean;
  slot?: string;
  componentProps?: {
    disabled?: boolean;
    clearable?: boolean;
    options?: any[];
    format?: string;
    defaultTime?: string;
    defaultValue?: string | boolean;
  };
}
