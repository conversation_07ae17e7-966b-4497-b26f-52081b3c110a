import { resolve } from 'path';
import { configDefaults, defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  test: {
    globals: true,
    environment: 'happy-dom', // 模拟 DOM
    includeSource: [...configDefaults.exclude, 'src/**/*.{js,ts,jsx,tsx,vue}'],
  },
  plugins: [vue()],
  resolve: {
    alias: {
      '@build': resolve(__dirname, './build'),
      '/@': resolve(__dirname, './src'),
      '/#': resolve(__dirname, './types'),
    },
  },
});
