/* eslint-disable prefer-promise-reject-errors */
/* eslint-disable no-shadow */
/* eslint-disable no-unused-expressions */
// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动
// The axios configuration can be changed according to the project, just change the file, other files can be left unchanged

import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import Axios from 'axios';
import type { RequestOptions, Result } from '/#/axios';
import type { AxiosTransform, CreateAxiosOptions } from './axiosTransform';
import { VAxios } from './Axios';
import { checkStatus } from './checkStatus';
// import { useGlobSetting } from '/@/hooks/setting';
import { useMessage } from '/@/hooks/web/useMesage';
import { RequestEnum, ResultEnum, ContentTypeEnum } from '/@/enums/httpEnum';
import { isString } from '../is';
// 暂时隐藏，等登录页面完善后，需要添加
import { getAuthToken } from '/@/utils/storage/auth';
import { setObjToUrlParams, deepMerge } from '../index';
// import { useErrorLogStoreWithOut } from '/@/store/modules/errorLog';
// import { useI18n } from '/@/hooks/web/useI18n';
// import t from './lang/zh-CN/sys';
import { transformI18n } from '/@/utils/i18n';
import { joinTimestamp, formatRequestDate } from './helper';
import { loadEnv, warpperEnv } from '@build/index';
// 处理首次登录header未传tenantId
import { promiseState } from '/@/utils/tools/getPromiseState';
import { ref } from 'vue';
import { useUserStore } from '/@/stores/modules/user';
import { ThrottleRequests } from '/#/axios';

export const cancelToken = Axios.CancelToken;
const source = cancelToken.source();

// const globSetting = useGlobSetting();
const { VITE_APP_BASE_URL, VITE_APP_BASE_URL_PREFIX } = warpperEnv(loadEnv());

const { createMessage } = useMessage();
const throttleRequests: ThrottleRequests = {};
/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform: AxiosTransform = {
  /**
   * @description: 处理请求数据。如果数据不是预期格式，可直接抛出错误
   */
  transformRequestHook: (res: AxiosResponse<Result>, options: RequestOptions) => {
    // const { t } = useI18n();

    console.log(res);
    const { isTransformResponse, isReturnNativeResponse } = options;
    // 是否返回原生响应头 比如：需要获取响应头时使用该属性
    if (isReturnNativeResponse) {
      return res;
    }
    // 不进行任何处理，直接返回
    // 用于页面代码可能需要直接获取code，data，message这些信息时开启
    if (!isTransformResponse) {
      return res.result;
    }
    // 错误的时候返回

    const { result } = res;
    if (!result) {
      // return '[HTTP] Request has no return value';
      throw new Error(transformI18n('api.apiRequestFailed', true));
    }
    //  这里 code，result，message为 后台统一的字段，需要在 types.ts内修改为项目自己的接口返回格式
    const { code, data, message } = result;

    // debugger;
    // 这里逻辑可以根据项目进行修改
    const hasSuccess =
      result && Reflect.has(result, 'code') && code === ResultEnum.SUCCESS;
    if (hasSuccess) {
      return data;
    }



    // const { code, result, message } = data;

    // // debugger;
    // // 这里逻辑可以根据项目进行修改
    // const hasSuccess = data && Reflect.has(data, 'code') && code === ResultEnum.SUCCESS;
    // if (hasSuccess) {
    //   return result;
    // }
    // 在此处根据自己项目的实际情况对不同的code执行不同的操作
    // 如果不希望中断当前请求，请return数据，否则直接抛出异常即可
    let timeoutMsg = '';
    switch (code) {
      case ResultEnum.TIMEOUT:
        timeoutMsg = transformI18n('api.timeoutMessage', true);
        break;
      default:
        if (message) {
          timeoutMsg = message;
        }
    }
    createMessage.error(timeoutMsg);
    // errorMessageMode=‘modal’的时候会显示modal错误弹窗，而不是消息提示，用于一些比较重要的错误
    // errorMessageMode='none' 一般是调用时明确表示不希望自动弹出错误提示
    // if (options.errorMessageMode === 'modal') {
    //   createErrorModal({ title: transformI18n('api.errorTip', true), content: timeoutMsg });
    // } else if (options.errorMessageMode === 'message') {
    //   createMessage.error(timeoutMsg);
    // }

    throw new Error(timeoutMsg || transformI18n('api.apiRequestFailed', true));
  },

  // 请求之前处理config
  beforeRequestHook: (config, options) => {
    const {
      apiUrl,
      joinPrefix,
      joinParamsToUrl,
      formatDate,
      joinTime = true,
      urlPrefix,
    } = options;

    if (joinPrefix) {
      config.url = `${urlPrefix}${config.url}`;
    }

    if (apiUrl && isString(apiUrl)) {
      config.url = `${apiUrl}${config.url}`;
    }
    const params = config.params || {};
    const data = config.data || false;
    formatDate && data && !isString(data) && formatRequestDate(data);
    // config.method?.toUpperCase() + '-' + config.url 正在请求中
    const nowTimeMS = new Date().getTime();
    const lastTimeMS =
      throttleRequests[config.method?.toUpperCase() + '-' + config.url];
    if (
      lastTimeMS &&
      nowTimeMS - lastTimeMS <= (options.cancelTimeIntetval as number)
    ) {
      if (!config.cancelToken) {
        const cancelMsg = options.cancelMsg as string;
        createMessage.warning(cancelMsg);
        config.cancelToken = source.token;
        source.cancel(cancelMsg);
      }
    }
    throttleRequests[config.method?.toUpperCase() + '-' + config.url] = nowTimeMS;
    if (config.method?.toUpperCase() === RequestEnum.GET) {
      if (!isString(params)) {
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = Object.assign(params || {}, joinTimestamp(joinTime, false));
      } else {
        // 兼容restful风格
        config.url = `${config.url + params}${joinTimestamp(joinTime, true)}`;
        config.params = undefined;
      }
    } else if (!isString(params)) {
      formatDate && formatRequestDate(params);
      if (
        Reflect.has(config, 'data') &&
        config.data &&
        Object.keys(config.data).length > 0
      ) {
        config.data = data;
        config.params = params;
      } else {
        // 非GET请求如果没有提供data，则将params视为data
        config.data = params;
        config.params = undefined;
      }
      if (joinParamsToUrl) {
        config.url = setObjToUrlParams(config.url as string, {
          ...config.params,
          ...config.data,
        });
      }
    } else {
      // 兼容restful风格
      config.url += params;
      config.params = undefined;
    }
    return config;
  },

  /**
   * @description: 请求拦截器处理
   */
  requestInterceptors: (config: AxiosRequestConfig, options: CreateAxiosOptions) => {
    // 请求之前处理config
    const token = getAuthToken();
    if (token && (config as Recordable)?.requestOptions?.withToken !== false) {
      // jwt token
      config.headers.Authorization = options.authenticationScheme
        ? `${options.authenticationScheme} ${token}`
        : token;
      // useUserStore()
      const info = ref<{ tenantId: string; projectId: string; deptId: string }>({
        tenantId: '',
        projectId: '',
        deptId: '',
      });

      // 处理首次登录 租户获取菜单需要传tenantId
      // const tenantStore = useTenantStore();
      const userStore = useUserStore();

      info.value.tenantId = userStore.getUserInfo?.tenantId || '';
      info.value.projectId = userStore.getUserInfo?.projectId || '';
      info.value.deptId = userStore.getUserInfo?.currentDept?.id || '';

      // opType区分当前接口是平台管理、租户管理、项目管理调用，根据不同的调用传不同的参数
      // opType '' 'tenant' 'project'
      const opType = config?.params?.opType || config?.data?.opType;
      if (info.value) {
        if (opType === 'system') {
          info.value.tenantId = '';
          info.value.projectId = '';
          info.value.deptId = '';
        } else if (opType === 'tenant') {
          info.value.projectId = '';
          info.value.deptId = '';
        } else if (opType === 'project') {
          // 应当携带所有参数
        }
      }

      config.headers.tenantId = info.value.tenantId;
      config.headers.projectId = info.value.projectId;
      config.headers.deptId = info.value.deptId;
    }
    return config;
  },

  /**
   * @description: 响应拦截器处理
   */
  responseInterceptors: (res: AxiosResponse<any>) => {
    return res;
  },

  /**
   * @description: 响应错误处理
   */
  responseInterceptorsCatch: async (error: any) => {
    if (Axios.isCancel(error)) {
      return Promise.reject({
        status: 999999,
        statusText: error.message,
      });
    }
    const { response, code, message, config } = error || {};
    const { cancelToken } = config;
    if (cancelToken) {
      // 用户主动使用 cancelToken时 对错误进行处理
      const state = await promiseState(cancelToken.promise);
      if (state === 'fulfilled') {
        const cancelTokenData = await cancelToken.promise;
        if (cancelTokenData) {
          // 以下 成功的返回 会经过 RequestHook的处理，需要封装 status data 等 response信息
          return Promise.resolve({
            status: 200,
            statusText: 'OK',
            data: {
              code: ResultEnum.SUCCESS,
              type: 'CanceledError',
              success: true,
              result: {
                type: 'CanceledError',
                ...cancelTokenData,
              },
            },
            config,
          });
        }
      }
    }

    const errorMessageMode = config?.requestOptions?.errorMessageMode || 'none';
    const msg: string =
      response?.data?.error?.message || response?.data?.msg || response?.data?.message;
    const err: string = error?.toString?.() ?? '';
    let errMessage = '';
    const { isTransformResponse } = config;
    try {
      if (code === 'ECONNABORTED' && message.indexOf('timeout') !== -1) {
        errMessage = transformI18n('api.apiTimeoutMessage', true);
      }
      if (err?.includes('Network Error')) {
        errMessage = transformI18n('api.networkExceptionMsg', true);
      }

      if (errMessage) {
        createMessage.error(errMessage);
        if (!isTransformResponse) {
          return Promise.reject(error);
        }
        return Promise.reject(new Error(errMessage));
      }
    } catch (error) {
      if (!isTransformResponse) {
        return Promise.reject(error);
      }
      throw new Error(`${error}`);
    }

    // checkStatus(error?.response?.status, msg || err, errorMessageMode);
    checkStatus(error?.response?.status, msg, errorMessageMode);
    if (!isTransformResponse) {
      return Promise.reject(error);
    }
    return Promise.reject(`${error}`);
  },
};

export function createAxios(opt?: Partial<CreateAxiosOptions>) {
  return new VAxios(
    deepMerge(
      {
        // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Authentication#authentication_schemes
        // authentication schemes，e.g: Bearer
        // authenticationScheme: 'Bearer',
        authenticationScheme: '',
        timeout: 10 * 10000,
        // 基础接口地址
        // baseURL: globSetting.apiUrl,
        // 接口可能会有通用的地址部分，可以统一抽取出来
        headers: { 'Content-Type': ContentTypeEnum.JSON },
        // 如果是form-data格式
        // headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
        // 数据处理方式
        transform,
        // 配置项，下面的选项都可以在独立的接口请求中覆盖
        requestOptions: {
          // 默认将prefix 添加到url
          joinPrefix: true,
          // 是否返回原生响应头 比如：需要获取响应头时使用该属性
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformResponse: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 消息提示类型
          errorMessageMode: 'message',
          // 接口baseUrl
          apiUrl: VITE_APP_BASE_URL,
          // 接口前缀
          urlPrefix: VITE_APP_BASE_URL_PREFIX,
          //  是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreCancelToken: true,
          // 是否携带token
          withToken: true,
          // 用户短时间内多次请求同一个接口的提示信息
          cancelMsg: '请求已发出，请稍后操作！',
          // 用户在多长时间内不能访问同一个接口，毫秒
          cancelTimeIntetval: 0,
        },
      },
      opt || {},
    ),
  );
}
export const defHttp = createAxios({
  requestOptions: { cancelTimeIntetval: 0 },
});

// other api url
// export const otherHttp = createAxios({
//   requestOptions: {
//     apiUrl: '/api2',  // => http://localhost/api2/xxxx
//     urlPrefix: '/xxx',
//   },
// });
