# ChatAgent API 服务迁移说明

## 概述

本文档说明了如何将 ChatAgent 相关组件中的 fetch API 调用迁移到统一的 axios 格式。

## 新的 API 服务文件

### `axios-api-service.ts`

这是一个统一的 axios API 服务文件，包含了所有 chatagent 相关的 API 调用，已经从 fetch 格式转换为 axios 格式。

## 迁移对照表

### 1. DirectApiService 迁移

**原来的 fetch 调用：**
```typescript
// direct-api-service.ts
const response = await fetch(`${this.BASE_URL}/execute`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ query })
})
```

**新的 axios 调用：**
```typescript
// axios-api-service.ts
import { AxiosApiService } from './axios-api-service'

const result = await AxiosApiService.sendMessage(query)
```

### 2. StreamingApiService 迁移

**原来的 fetch 调用：**
```typescript
// streaming-api-service.ts
const response = await fetch(url, {
  method: 'POST',
  mode: 'cors',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream'
  },
  body: JSON.stringify(request)
})
```

**新的 axios 调用：**
```typescript
// axios-api-service.ts
const result = await AxiosApiService.sendMessageWithStreaming(planTemplateId, request)
```

### 3. ModelApiService 迁移

**原来的 fetch 调用：**
```typescript
// model-api-service.ts
const response = await fetch(`${this.BASE_URL}/brief/list`)
const result = await this.handleResponse(response)
const data = await result.json()
```

**新的 axios 调用：**
```typescript
// axios-api-service.ts
const models = await AxiosApiService.getBriefModelList()
```

### 4. DataSourceApiService 迁移

**原来的 fetch 调用：**
```typescript
// datasource-api-service.ts
const response = await fetch(`${this.BASE_URL}/list?pageNo=1&pageSize=1000&refresh=true`)
```

**新的 axios 调用：**
```typescript
// axios-api-service.ts
const dataSources = await AxiosApiService.getDataSourceList()
```

### 5. AgentApiService 迁移

**原来的 fetch 调用：**
```typescript
// agent-api-service.ts
const response = await fetch(this.BASE_URL)
const result = await this.handleResponse(response)
return await result.json()
```

**新的 axios 调用：**
```typescript
// axios-api-service.ts
const agents = await AxiosApiService.getAllAgents()
```

## 使用方法

### 1. 导入服务

```typescript
import { AxiosApiService } from '../api/axios-api-service'
```

### 2. 基本用法

```typescript
// 获取模型列表
const models = await AxiosApiService.getBriefModelList()

// 获取数据源列表
const dataSources = await AxiosApiService.getDataSourceList()

// 发送消息
const result = await AxiosApiService.sendMessage('你好')

// 获取聊天历史
const history = await AxiosApiService.getChatHistory(chatId)
```

### 3. 错误处理

axios 服务会自动处理错误，但你仍然可以使用 try-catch：

```typescript
try {
  const result = await AxiosApiService.sendMessage(query)
  console.log('成功:', result)
} catch (error) {
  console.error('失败:', error)
}
```

### 4. 自定义选项

你可以传递 axios 选项：

```typescript
const result = await AxiosApiService.sendMessage(query, {
  timeout: 30000,
  errorMessageMode: 'none'
})
```

## 主要优势

1. **统一的错误处理**：使用项目统一的 axios 配置
2. **自动的请求/响应拦截**：包括认证、租户信息等
3. **更好的类型支持**：完整的 TypeScript 类型定义
4. **统一的配置**：超时、重试、取消等配置统一管理
5. **更好的调试**：统一的日志和错误处理

## 注意事项

1. **流式响应**：对于 SSE（Server-Sent Events）流式响应，axios 的处理方式与 fetch 不同，可能需要特殊处理
2. **文件上传**：如果有文件上传需求，需要使用 FormData
3. **取消请求**：axios 的取消机制与 fetch 的 AbortController 不同

## 迁移步骤

1. 将现有的 fetch 调用替换为对应的 AxiosApiService 方法
2. 移除原有的错误处理代码（axios 会自动处理）
3. 更新导入语句
4. 测试所有功能确保正常工作

## pages/index.vue 中的具体迁移

### 1. 刷新历史记录

**原来的代码：**
```typescript
const url = `/api/streaming-events/chat/sessions?page=0&size=50&userId=${encodeURIComponent(currentUserId.value)}`
const response = await fetch(url)
if (!response.ok) {
  throw new Error(`HTTP ${response.status}: ${response.statusText}`)
}
const data = await response.json()
```

**新的代码：**
```typescript
const data = await AxiosApiService.getChatSessionsPaginated(currentUserId.value, 0, 50)
```

### 2. 搜索聊天历史

**原来的代码：**
```typescript
const url = `/api/streaming-events/chat/search?userId=${encodeURIComponent(currentUserId.value)}&keyword=${encodeURIComponent(keyword)}&page=0&size=50`
const response = await fetch(url)
if (!response.ok) {
  throw new Error(`HTTP ${response.status}: ${response.statusText}`)
}
const data = await response.json()
```

**新的代码：**
```typescript
const data = await AxiosApiService.searchChatHistory(currentUserId.value, keyword, 0, 50)
```

### 3. 加载聊天历史

**原来的代码：**
```typescript
const url = `/api/streaming-events/chat/${encodeURIComponent(item.id)}/history`
const response = await fetch(url)
if (!response.ok) {
  throw new Error(`HTTP ${response.status}: ${response.statusText}`)
}
const data = await response.json()
```

**新的代码：**
```typescript
const data = await AxiosApiService.getChatHistoryById(item.id)
```

### 4. 删除聊天会话

**原来的代码：**
```typescript
const url = `/api/streaming-events/chat/session/${encodeURIComponent(item.id)}`
const response = await fetch(url, {
  method: 'DELETE'
})
if (!response.ok) {
  throw new Error(`HTTP ${response.status}: ${response.statusText}`)
}
```

**新的代码：**
```typescript
await AxiosApiService.deleteChatSession(item.id)
```

## 完整的 API 列表

### DirectApiService 相关
- `sendMessage(query: string)`
- `sendMessageWithStreamingDirect(planTemplateId: string, request: DirectStreamingRequest)`

### CommonApiService 相关
- `getDetails(planId: string)`
- `submitFormInput(planId: string, formData: any)`

### StreamingApiService 相关
- `sendMessageWithStreaming(planTemplateId: string, request: StreamingRequest)`
- `getChatHistory(chatId: string)`
- `getChatSessions(userId: string)`
- `deleteChatSession(chatId: string)`
- `getChatSessionsPaginated(userId: string, page?: number, size?: number)`
- `searchChatHistory(userId: string, keyword: string, page?: number, size?: number)`
- `getChatHistoryById(chatId: string)` - 通过聊天ID获取历史记录

### ModelApiService 相关
- `getBriefModelList()`
- `getAllModels()`
- `getModelById(id: string)`
- `testModel(id: string)`
- `getDefaultModelId()`

### DataSourceApiService 相关
- `getDataSourceList()`
- `getDatabaseList(dataSourceId: number)`
- `getSchemaList(dataSourceId: number, databaseName: string)`
- `testConnection(dataSourceId: number)`
- `getDataSourceById(id: number)`

### AgentApiService 相关
- `getAllAgents()`
- `getAgentById(id: string)`
- `createAgent(agentConfig: Omit<Agent, 'id'>)`
- `updateAgent(id: string, agentConfig: Agent)`
- `deleteAgent(id: string)`
- `getAvailableTools()`

### McpApiService 相关
- `getAllMcpServers()`
- `addMcpServer(mcpConfig: McpServerRequest)`
- `removeMcpServer(id: number)`

### AdminApiService 相关
- `getConfigsByGroup(groupName: string)`
- `batchUpdateConfigs(configs: ConfigItem[])`
- `getConfigById(id: number)`
- `updateConfig(config: ConfigItem)`

### ToolDetailApiService 相关
- `getToolDetail(thinkActId: string | number)`
- `getToolParameters(thinkActId: string | number)`
- `getActionResult(thinkActId: string | number)`
- `getBatchToolDetails(thinkActIds: (string | number)[])`
- `isToolDetailAvailable(thinkActId: string | number)`

### PlanActApiService 相关
- `generatePlan(query: string, existingJson?: string)`
- `executePlan(planTemplateId: string, rawParam?: string)`
- `savePlanTemplate(planId: string, planJson: string)`
- `getPlanVersions(planId: string)`
- `getVersionPlan(planId: string, versionIndex: number)`
- `getAllPlanTemplates()`
- `updatePlanTemplate(planId: string, query: string, existingJson?: string)`
- `deletePlanTemplate(planId: string)`

### 工具方法
- `buildToolContext(dataSourceId: number, databaseName?: string, schemaName?: string)`
- `formatModelDisplayName(model: Model)`
- `isStreamingSupported(model: Model)`
- `filterModelsByType(models: Model[], type: string)`
- `filterModelsByProvider(models: Model[], provider: string)`
- `formatParameters(parameters: any)`
- `formatResult(result: any)`
