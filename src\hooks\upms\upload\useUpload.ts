import { ref } from 'vue';
import type { UploadProps, UploadRawFile } from 'element-plus';
import { genFileId } from 'element-plus';
export default function useUpload(upload) {
  const fileVisible = ref(false);
  // const upload = ref<UploadInstance>();
  const fileData = ref<any>();
  const handleExceed: UploadProps['onExceed'] = (files) => {
    upload.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    upload.value!.handleStart(file);
  };

  const handleChange = (file) => {
    console.log('file11', file)
    fileData.value = file;
  };

  // const uploadFile = () => {
  //   console.log(fileData.value);
  //   const form = new FormData();
  //   form.append('file', fileData.value?.raw);
  //   form.append('targetId', formData.value?.id ? formData.value.id.toString() : '');
  //   form.append(
  //     'targetInstCode',
  //     formData.value?.instCode ? formData.value.instCode.toString() : '',
  //   );
  //   uploadApi(form).then(() => {
  //     fileVisible.value = false;
  //     ElMessage({
  //       type: 'success',
  //       message: '导入成功',
  //     });
  //   });
  // };
  return {
    fileVisible,
    fileData,
    handleExceed,
    handleChange,
    // uploadFile,
  };
}
