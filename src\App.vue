<template>
  <div id="app">
    <el-config-provider :locale="currentLocale">
      <router-view v-if="isRouterAlive" />
    </el-config-provider>
  </div>
  <!-- 全局的文件下载，变更downloadUrlStore.downloadUrl即可触发文件下载 -->
  <iframe style="display: none" :src="downloadUrl"></iframe>
</template>
<script setup lang="ts">
  import { ElConfigProvider } from 'element-plus';
  import zhCn from 'element-plus/es/locale/lang/zh-cn';
  import en from 'element-plus/es/locale/lang/en';
  import { computed, nextTick, provide, ref } from 'vue';
  import { projectSettings } from '/@/settings/config/projectConfig';
  import { useDownloadStore } from '/@/stores/modules/download';
  import { setTheme } from '/@/layouts/default/components/theme/element-plus.ts';
  const currentLocale = computed(() => (projectSettings.locale === 'zh' ? zhCn : en));
  const downloadUrlStore = useDownloadStore();
  const downloadUrl = computed(() => {
    const url = downloadUrlStore.getDownloadUrl;
    return url;
  });

  const isRouterAlive = ref(true);
  const reload = () => {
    isRouterAlive.value = false;
    nextTick(() => {
      isRouterAlive.value = true;
    });
  };
  setTheme();
  provide('reload', reload);
</script>
