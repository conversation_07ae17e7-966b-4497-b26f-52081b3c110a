// 基础请求方法封装

// 基础URL，可以根据环境变量配置
const baseURL = '/api';

// 通用请求头
const headers = {
  'Content-Type': 'application/json'
};

// 处理响应
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const error = await response.json().catch(() => ({}));
    throw new Error(error.message || `HTTP error! status: ${response.status}`);
  }
  return response.json();
};

// GET请求
export const get = async <T>(url: string, params?: Record<string, any>): Promise<T> => {
  const queryString = params
    ? `?${Object.entries(params)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&')}`
    : '';
  
  const response = await fetch(`${baseURL}${url}${queryString}`, {
    method: 'GET',
    headers
  });
  
  return handleResponse(response);
};

// POST请求
export const post = async <T>(url: string, data?: any): Promise<T> => {
  const response = await fetch(`${baseURL}${url}`, {
    method: 'POST',
    headers,
    body: data ? JSON.stringify(data) : undefined
  });
  
  return handleResponse(response);
};

// PUT请求
export const put = async <T>(url: string, data?: any): Promise<T> => {
  const response = await fetch(`${baseURL}${url}`, {
    method: 'PUT',
    headers,
    body: data ? JSON.stringify(data) : undefined
  });
  
  return handleResponse(response);
};

// DELETE请求
export const del = async <T>(url: string): Promise<T> => {
  const response = await fetch(`${baseURL}${url}`, {
    method: 'DELETE',
    headers
  });
  
  return handleResponse(response);
};