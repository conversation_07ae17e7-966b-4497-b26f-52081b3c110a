# 1、简介

CPVF 是基于 [Vue3](https://github.com/vuejs/vue-next)、[Vite2](https://github.com/vitejs/vite)、 [ElementPlus](https://element-plus.org/zh-CN/)、[TypeScript](https://www.typescriptlang.org/) 的前端框架，目标是为开发中大型项目提供开箱即用的解决方案。

包括二次封装组件、utils、hooks、动态菜单、权限校验、按钮级别权限控制等功能。项目会使用前端主流技术栈，可以作为项目的启动模版，以帮助你快速搭建企业级中后台产品原型。该项目会持续跟进主流技术，并将其应用在项目中。

## 1.1、脚手架主要技术栈

- Vue 3
- Typescript 5.6.2
- Vite 4.5.5

## 1.2、Recommended IDE Setup

建议安装的 vscode 插件

- [VSCode](https://code.visualstudio.com/)
- Vue3 snippets vue 提示
- [Volar](https://marketplace.visualstudio.com/items?itemName=johnsoncodehk.volar)
- EsLint
- StyleLint 样式顺序
- koroFileHeader 文件头注释
- GitLens 查看代码行提交记录

# 2、项目使用

- 建议升级 node 版本 16.13.x
- 云桌面设置 registry：`npm config set registry http://**************:8081/repository/npm-public/`
- 全局安装 pnpm `npm i -g pnpm`(请勿在项目内安装 pnpm，可能会遇到问题)
- 1. 安装依赖 `pnpm install`
- 2. 运行 `pnpm dev`
- 3. 打包 `pnpm build`
