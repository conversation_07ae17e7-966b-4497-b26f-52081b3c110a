export interface LoginParams {
  username: string;
  password: string;
}

export interface PhoneLoginParams {
  phone: string;
  code: string;
}

/**
 * @description: Login interface return value
 */
export interface LoginResultModel {
  access_token: string;
  expires_in: number;
  // license: string;
  // refresh_token: string;
  // scope: string;
  // token_type: string;
}

// export interface menuParams {
//   tenantId?: string;
//   projectId?: string;
//   isAll: Boolean; // 是否展示全部菜单 false 展示启用的，true展示全部
// }
