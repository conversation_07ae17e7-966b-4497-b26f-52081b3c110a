# 开发环境
NODE_ENV=staging
# 配置优先走mock数据还是后端代理
VITE_MOCK=false
# 是否子应用
VITE_MICRO=true

VITE_PORT=8080

# 接口基础请求地址  通常配置如：http://localhost:9000
VITE_APP_BASE_URL=
# 与VITE_APP_BASE_URL配合 通常配置 如： /api => http://localhost:9000/api
# VITE_APP_BASE_URL_PREFIX=/basic-api
VITE_APP_BASE_URL_PREFIX=''
# 代理
# VITE_PROXY = [["/basic-api","http://**********:9090/api"], ["/sys","http://**********:9090/sys"], ["/sso","http://**********:9090/sso"],["/processing","http://**********:9090/processing"], ["/upload","http://**********:9090/upload"]]
# VITE_PROXY = [["/basic-api","http://*************:9998/api"], ["/sys","http://************:9090/sys"], ["/sso","http://************:9090/sso"],["/processing","http://************:9090/processing"], ["/upload","http://************:9090/upload"]]
VITE_PROXY = [["/basic-api","http://************:9090/api"], ["/sys","http://************:9090/sys"], ["/sso","http://************:9090/sso"],["/processing","http://************:9090/processing"], ["/upload","http://************:9090/upload"]]

# 静态资源地址()
VITE_APP_STATIC_URL=

# 构建资源公共路径
VITE_PUBLIC_PATH=/

# 统一认证登录页面地址
VITE_LOGIN_PAGE=http://**************:18001

# 项目部署后的地址
VITE_PROJECT_BASE_URL=localhost:8080