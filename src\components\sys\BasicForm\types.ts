export interface FormOptions {
  field: string;
  label: string;
  required?: boolean; // required自动提示 label + 不能为空
  span?: number;
  formItemFlag?: boolean; // 是否需要消息提示
  component:
    | 'Text'
    | 'Input'
    | 'InputPassword'
    | 'InputTextArea'
    | 'InputNumber'
    | 'DatePicker'
    | 'RadioGroup'
    | 'Select'
    | 'SelectMultiple'
    | 'TreeSelect'
    | 'CheckboxGroup'
    | 'Slot'
    | 'Rate'
    | 'Switch'
    | 'TimePicker';
  colProps?: { span: number };
  slot?: string;
  componentProps?: {
    placeholder?: string;
    options?: any[];
    disabled?: boolean | Fn;
    appendText?: string;
    prependText?: string;
    data?: any[];
    showCheckbox?: boolean;
    multiple?: boolean;
    nodeKey?: string;
    defaultValue?: string;
    clearable?: boolean;
    // 下拉框组件是否需要获取键和值
    useAll?: boolean;
    valueKey?: string;
    min?: number;
    max?: number;
    rows?: number;
    minlength?: number;
    maxLength?: number;
    autocomplete?: string;
    type?: string;
    // 时间选择器
    isRange?: boolean;
    // v-model时是否只是数字
    // isNumber?: boolean;
    iconTitle?: string;
    // import { InfoFilled } from '@element-plus/icons-vue'
    iconType?: any;
    style?: any;
    step?: number;
    activeText?: string;
    inactiveText?: string;
  };
  rules?: any[];
  dynamicRules?: any[];
  ifShow?: Fn | boolean;
  show?: boolean;
  filterable?: boolean;
  onChange?: Fn;
  onInput?: Fn;
}

export interface Option {
  label: string;
  value: string | number | undefined;
}
