/**
 * 获取 租户 的字典
 */
import { MockMethod } from 'vite-plugin-mock';
enum Api {
  QUERY_CODES = '/sys/dict/codes',
}
export default [
  {
    url: Api.QUERY_CODES,
    method: 'get',
    response: () => {
      return {
        message: '成功',
        code: 1,
        result: {
          sys_gender: [
            { value: '0', label: '男', version: 18, tenantId: null, projectId: null },
            { value: '1', label: '女', version: 18, tenantId: null, projectId: null },
          ],
          biz_email_server: [
            {
              value: 'mail.chinapost.com.cn:25',
              label: '邮政邮箱- chinapost',
              version: 4,
              tenantId: null,
              projectId: null,
            },
          ],
          dic_channel_verify_status: [
            {
              value: '0',
              label: '未授权',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: '1',
              label: '已授权',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '已禁用',
              version: 6,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_job_mode_all: [
            { value: '12', label: '12', version: 11, tenantId: null, projectId: null },
            { value: '6', label: '6', version: 9, tenantId: null, projectId: null },
            { value: '3', label: '3', version: 7, tenantId: null, projectId: null },
            { value: '1', label: '1', version: 7, tenantId: null, projectId: null },
          ],
          sys_secret_key_status: [
            { value: '1', label: '启用', version: 7, tenantId: null, projectId: null },
            { value: '2', label: '禁用', version: 6, tenantId: null, projectId: null },
          ],
          biz_channel_status: [
            {
              value: '0',
              label: '已禁用',
              version: 10,
              tenantId: null,
              projectId: null,
            },
            {
              value: '1',
              label: '已启用',
              version: 9,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_misfire_policy: [
            {
              value: '1',
              label: '立即执行',
              version: 5,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '执行一次',
              version: 5,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '放弃执行',
              version: 5,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_data_scope: [
            { value: '1', label: '全部', version: 17, tenantId: null, projectId: null },
            {
              value: '2',
              label: '所在机构及以下数据',
              version: 17,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '所在机构数据',
              version: 17,
              tenantId: null,
              projectId: null,
            },
            {
              value: '4',
              label: '仅本人数据',
              version: 15,
              tenantId: null,
              projectId: null,
            },
            {
              value: '5',
              label: '按明细设置',
              version: 15,
              tenantId: null,
              projectId: null,
            },
            {
              value: '6',
              label: '个性化订制',
              version: 7,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_secret_key_format: [
            {
              value: '2',
              label: 'PKCS1(非JAVA适用)',
              version: 2,
              tenantId: null,
              projectId: null,
            },
            {
              value: '1',
              label: 'PKCS8(JAVA适用)',
              version: 2,
              tenantId: null,
              projectId: null,
            },
          ],
          biz: [
            {
              value: 'biz_channel_status',
              label: '业务应用-应用系统状态',
              version: 19,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'dic_channel_verify_status',
              label: '业务应用-应用系统授权状态',
              version: 16,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_channel_type',
              label: '业务应用-系统类型',
              version: 13,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_network_type',
              label: '业务应用-网络类型',
              version: 19,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_security_level',
              label: '业务应用-安全等级',
              version: 25,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_channel_access_button',
              label: '业务应用-授权按钮标题',
              version: 18,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_channel_voice_recognized',
              label: '业务应用-长语音是否识别',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_email_server',
              label: '业务系统-邮件服务器种类',
              version: 26,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_modal',
              label: '开放服务-引擎模态',
              version: 30,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_channel_scene_rule_type',
              label: '业务应用-场景规则分类',
              version: 14,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_channel_sms_type',
              label: '业务应用-短信分类',
              version: 19,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_islogin',
              label: '业务系统-场景是否登录',
              version: 17,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_scene_status',
              label: '业务系统-场景状态',
              version: 38,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_modal_types',
              label: '客户样本-样本分类',
              version: 35,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_modal_sample_channel_status',
              label: '客户样本-样本和系统的绑定状态',
              version: 10,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_modal_status',
              label: '客户样本-样本状态',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_modal_collect_types',
              label: '客户样本-采集类型',
              version: 14,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_example_user_conf',
              label: '接口示例-普通用户配置',
              version: 38,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_example_demo_url',
              label: '接口示例-人脸示例',
              version: 4,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_example_admin_conf',
              label: '接口示例-管理员配置',
              version: 153,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_engine_feature_status',
              label: '智能服务-引擎特征值提取状态',
              version: 10,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_misfire_policy',
              label: '定时任务-计划执行错误策略',
              version: 11,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_concurrent',
              label: '定时任务-是否并发执行',
              version: 12,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_id_type',
              label: '引擎管理-厂商证件类型',
              version: 23,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_eigenvalue_status',
              label: '未知-特征值提取',
              version: 14,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_service_type',
              label: '人脸识别-服务类型',
              version: 14,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_enable',
              label: '系统-启用禁用',
              version: 25,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_species',
              label: '日报月报-识别种类',
              version: 26,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_card_type',
              label: '系统-法人证件类型',
              version: 7,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_eigenvalue_status: [
            {
              value: '0',
              label: '待提取',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '1',
              label: '进行中',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '已完成',
              version: 7,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_quartz_run_status: [
            {
              value: '0',
              label: '已暂停',
              version: 2,
              tenantId: null,
              projectId: null,
            },
            {
              value: '1',
              label: '已启动',
              version: 3,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_modal: [
            { value: '1', label: '人脸', version: 12, tenantId: null, projectId: null },
            { value: '2', label: '指纹', version: 9, tenantId: null, projectId: null },
            {
              value: '3',
              label: '指静脉',
              version: 9,
              tenantId: null,
              projectId: null,
            },
            { value: '4', label: '虹膜', version: 9, tenantId: null, projectId: null },
            { value: '5', label: '语音', version: 9, tenantId: null, projectId: null },
            { value: '6', label: '语义', version: 9, tenantId: null, projectId: null },
            { value: '7', label: 'OCR', version: 9, tenantId: null, projectId: null },
            { value: '0', label: '其他', version: 10, tenantId: null, projectId: null },
          ],
          biz_modal_sample_channel_status: [
            {
              value: '0',
              label: '已解约',
              version: 4,
              tenantId: null,
              projectId: null,
            },
            {
              value: '1',
              label: '使用中',
              version: 4,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_monitor: [
            {
              value: 'http://192.168.126.14:31005/',
              label: '注册中心Consul',
              version: 4,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'http://192.168.126.14:31001/',
              label: '服务中心Monitor',
              version: 2,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'http://192.168.126.14:31009/swagger-ui.html',
              label: '接口文档Swagger',
              version: 2,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'http://192.168.125.247:8080',
              label: '监控中心Skywalking',
              version: 3,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'http://192.168.126.14:31008/nacos',
              label: '配置中心Nacos',
              version: 2,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_user_card_type_errors: [
            {
              value: '1',
              label: '请输入正确的身份证！',
              version: 9,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '请输入正确的驾驶证！',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '请输入正确的军官证！',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: '4',
              label: '请输入正确的护照！',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: '5',
              label: '请输入正确的港澳通行证！',
              version: 10,
              tenantId: null,
              projectId: null,
            },
            {
              value: '6',
              label: '请输入正确的台湾通行证！',
              version: 8,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_oss_type: [
            {
              value: 'MINIO',
              label: '私有云存储',
              version: 6,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'ALIYUN',
              label: '阿里云存储',
              version: 6,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'QCLOUD',
              label: '腾讯云存储',
              version: 6,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'QINIU',
              label: '七牛云存储',
              version: 7,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_status: [
            { value: '1', label: '启用', version: 5, tenantId: null, projectId: null },
            { value: '0', label: '禁用', version: 5, tenantId: null, projectId: null },
          ],
          sys_quartz_run_concurrent_status: [
            { value: '0', label: '禁止', version: 2, tenantId: null, projectId: null },
            { value: '1', label: '允许', version: 2, tenantId: null, projectId: null },
          ],
          biz_network_type: [
            { value: '1', label: '内网', version: 15, tenantId: null, projectId: null },
            { value: '2', label: '外网', version: 9, tenantId: null, projectId: null },
          ],
          sys_sh_flag: [
            { value: '1', label: '启用', version: 29, tenantId: null, projectId: null },
            { value: '0', label: '禁用', version: 29, tenantId: null, projectId: null },
          ],
          biz_enable: [
            { value: '0', label: '禁用', version: 16, tenantId: null, projectId: null },
            { value: '1', label: '启用', version: 15, tenantId: null, projectId: null },
          ],
          biz_service_type: [
            { value: '1', label: '注册', version: 8, tenantId: null, projectId: null },
            { value: '2', label: '解约', version: 7, tenantId: null, projectId: null },
            { value: '3', label: '认证', version: 7, tenantId: null, projectId: null },
          ],
          biz_security_level: [
            {
              value: '1',
              label: '用户自主保护级',
              version: 10,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '系统审计保护级',
              version: 9,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '安全标记保护级',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: '4',
              label: '结构化保护级',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: '5',
              label: '访问验证保护级',
              version: 8,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_log_operate_type: [
            {
              value: 'OTHER',
              label: '其它',
              version: 13,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'EDIT',
              label: '编辑',
              version: 14,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'LOCK',
              label: '锁定/解锁',
              version: 13,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'DELETE',
              label: '删除',
              version: 10,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'RESETPASSWORD',
              label: '重置用户密码',
              version: 3,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'LOGIN_SUCCESS',
              label: '登录成功',
              version: 15,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'LOGIN_FAIL',
              label: '登录失败11',
              version: 9,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'LOGIN_OUT',
              label: '退出登录',
              version: 2,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_example_demo_url: [
            {
              value: '/assets/demo/voice.wav',
              label: '语音测试用例',
              version: 1,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_species: [
            {
              value: '1',
              label: '人脸识别',
              version: 9,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '指纹识别',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '指静脉识别',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '4',
              label: '虹膜识别',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '5',
              label: '语音识别',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '6',
              label: '语义识别',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '7',
              label: 'OCR识别',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            { value: '0', label: '其他', version: 7, tenantId: null, projectId: null },
          ],
          sys_quartz_set_status: [
            { value: '0', label: '暂停', version: 2, tenantId: null, projectId: null },
            { value: '1', label: '启动', version: 2, tenantId: null, projectId: null },
          ],
          biz_channel_scene_rule_type: [
            { value: '1', label: '阈值', version: 11, tenantId: null, projectId: null },
          ],
          biz_dept_type: [
            {
              value: '1',
              label: '内部机构',
              version: 2,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '外部机构',
              version: 2,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_modal_collect_types: [
            { value: '1', label: '注册', version: 6, tenantId: null, projectId: null },
            { value: '2', label: '比对', version: 3, tenantId: null, projectId: null },
            { value: '3', label: '解约', version: 2, tenantId: null, projectId: null },
            {
              value: '4',
              label: 'OCR识别',
              version: 1,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_modal_status: [
            {
              value: '1',
              label: '使用中',
              version: 2,
              tenantId: null,
              projectId: null,
            },
            {
              value: '0',
              label: '已失效',
              version: 2,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_flag: [
            { value: '1', label: '正常', version: 21, tenantId: null, projectId: null },
            { value: '0', label: '锁定', version: 22, tenantId: null, projectId: null },
          ],
          sys_user_dept: [
            {
              value: '1',
              label: '用户管理-机构-显示隐藏',
              version: 7,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_auth_grant_types: [
            {
              value: 'password',
              label: '帐号密码模式',
              version: 27,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'refresh_token',
              label: '隐式授权模式',
              version: 27,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'client_credentials',
              label: '客户端凭证模式',
              version: 25,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'authorization_code',
              label: '授权码模式',
              version: 26,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_job_mode_grow: [
            { value: '1', label: '每天', version: 14, tenantId: null, projectId: null },
            { value: '2', label: '每周', version: 12, tenantId: null, projectId: null },
            { value: '3', label: '每月', version: 8, tenantId: null, projectId: null },
          ],
          sys_user_personnel_type: [
            {
              value: '1',
              label: '内部人员',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '外部人员',
              version: 7,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_concurrent: [
            { value: '1', label: '允许', version: 7, tenantId: null, projectId: null },
            { value: '0', label: '禁止', version: 7, tenantId: null, projectId: null },
          ],
          biz_modal_types: [
            { value: '1', label: '人脸', version: 7, tenantId: null, projectId: null },
            { value: '2', label: '指纹', version: 5, tenantId: null, projectId: null },
            { value: '7', label: 'OCR', version: 9, tenantId: null, projectId: null },
            { value: '0', label: '其他', version: 6, tenantId: null, projectId: null },
          ],
          global: [
            {
              value: '10',
              label: '默认分页行数',
              version: 2,
              tenantId: null,
              projectId: null,
            },
            {
              value: '1',
              label: '机构删除策略',
              version: 6,
              tenantId: null,
              projectId: null,
            },
            {
              value:
                '^((\\+86|0086)?\\s*)((134[0-8]\\d{7})|(((13([0-3]|[5-9]))|(14[5-9])|15([0-3]|[5-9])|(16(2|[5-7]))|17([0-3]|[5-8])|18[0-9]|19(1|[8-9]))\\d{8})|(14(0|1|4)0\\d{7})|(1740([0-5]|[6-9]|[10-12])\\d{7}))$',
              label: '手机号正则校验',
              version: 4,
              tenantId: null,
              projectId: null,
            },
            {
              value:
                "^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$",
              label: '邮箱正则校验',
              version: 4,
              tenantId: null,
              projectId: null,
            },
            {
              value:
                '^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9])).{12,32}$',
              label: '正确密码正则校验',
              version: 8,
              tenantId: null,
              projectId: null,
            },
          ],
          sys: [
            {
              value: 'sys_yn_flag',
              label: '系统-是/否',
              version: 30,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_gender',
              label: '系统-性别',
              version: 23,
              tenantId: null,
              projectId: null,
            },
            {
              value: '192.168.125.245,192.168.125.246|root|aliOS1688|22|3',
              label: '系统服务资源',
              version: 3,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_flag',
              label: '系统-已激活/已禁用',
              version: 30,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_status',
              label: '系统-通用状态-启用禁用',
              version: 10,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_service_center',
              label: '系统-开放服务能力中心',
              version: 10,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_log_operate_method',
              label: '系统-审计日志请求方式',
              version: 18,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_sh_flag',
              label: '系统-数据字典的启用/禁用',
              version: 43,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_roles',
              label: '系统-系统默认用户',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_monitor',
              label: '系统监控-监控中心',
              version: 20,
              tenantId: null,
              projectId: null,
            },
            {
              value: '0',
              label: '用户管理-机构',
              version: 5,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_user_personnel_type',
              label: '用户管理-人员类型',
              version: 12,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_menu_type',
              label: '系统-资源类型',
              version: 21,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_user_card_type_errors',
              label: '用户管理-证件类型错误信息',
              version: 30,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_dept_type',
              label: '机构管理-机构类型',
              version: 6,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_dept_erp_type',
              label: '机构管理-ERP机构类型',
              version: 6,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz_dept_status',
              label: '机构管理-机构状态',
              version: 9,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_secret_key_status',
              label: '密钥管理-启用禁用',
              version: 19,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_secret_key_format',
              label: '密钥管理-加密格式',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_data_scope',
              label: '系统-数据权限范围',
              version: 31,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_auth_grant_types',
              label: '系统-授权模式',
              version: 55,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_log_operate_type',
              label: '审计日志-业务操作类别',
              version: 82,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_log_operator_type',
              label: '审计日志-业务操作人类别',
              version: 36,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_job_group',
              label: '计划任务-分组',
              version: 15,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_misfire_policy',
              label: '计划任务-执行策略',
              version: 11,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_quartz_run_status',
              label: '计划任务-任务运行状态',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_quartz_run_concurrent_status',
              label: '计划任务-任务调度-并行状态',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_quartz_set_status',
              label: '计划任务-计划任务-设置任务状态',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_quartz_log_result_status',
              label: '计划任务-任务日志-执行状态',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_oss_type',
              label: 'OSS-分布式存储类型',
              version: 15,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_content_type',
              label: 'OSS-常见文件类型',
              version: 24,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys_monitor_service_name',
              label: '系统监控-服务名',
              version: 5,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_example_admin_conf: [
            {
              value: '20210129804736940225069056',
              label: 'clientId',
              version: 56,
              tenantId: null,
              projectId: null,
            },
            {
              value: '21b467763556b1d84f564ed87a732be8',
              label: 'channelId',
              version: 33,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'c203f3a195fecca85f2e2904d6386d2c',
              label: 'sceneId',
              version: 59,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_log_operator_type: [
            {
              value: 'OTHER',
              label: '其它',
              version: 14,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'MANAGE',
              label: '后台管理用户',
              version: 15,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'MOBILE',
              label: '渠道端用户',
              version: 15,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_user_card_type: [
            {
              value: '1',
              label: '身份证',
              version: 15,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '驾驶证',
              version: 15,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '军官证',
              version: 15,
              tenantId: null,
              projectId: null,
            },
            { value: '4', label: '护照', version: 13, tenantId: null, projectId: null },
            {
              value: '7',
              label: '外国人永久居住证',
              version: 8,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_dept_erp_type: [
            { value: 'B', label: '寄递', version: 2, tenantId: null, projectId: null },
            { value: 'A', label: '邮务', version: 2, tenantId: null, projectId: null },
          ],
          sys_quartz_log_result_status: [
            { value: '0', label: '失败', version: 2, tenantId: null, projectId: null },
            { value: '1', label: '成功', version: 2, tenantId: null, projectId: null },
          ],
          sys_yn_flag: [
            { value: '1', label: '是', version: 12, tenantId: null, projectId: null },
            { value: '0', label: '否', version: 13, tenantId: null, projectId: null },
          ],
          dic_service_info: [
            {
              value: '***************',
              label: 'dic_cpms-sys_ip',
              version: 1,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_dept_status: [
            { value: '0', label: '无效', version: 5, tenantId: null, projectId: null },
            { value: '1', label: '有效', version: 4, tenantId: null, projectId: null },
          ],
          biz_channel_sms_type: [
            {
              value: '1',
              label: '验证码',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '通知短信',
              version: 6,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '营销短信',
              version: 4,
              tenantId: null,
              projectId: null,
            },
            {
              value: '4',
              label: '综合短信',
              version: 4,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_log_operate_method: [
            { value: 'GET', label: 'GET', version: 5, tenantId: null, projectId: null },
            {
              value: 'POST',
              label: 'POST',
              version: 5,
              tenantId: null,
              projectId: null,
            },
            { value: 'PUT', label: 'PUT', version: 4, tenantId: null, projectId: null },
            {
              value: 'DELETE',
              label: 'DELETE',
              version: 5,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_job_group: [
            { value: '0', label: '默认', version: 6, tenantId: null, projectId: null },
            { value: '1', label: '系统', version: 6, tenantId: null, projectId: null },
          ],
          sys_content_type: [
            {
              value: 'application/x-zip-compressed',
              label: '.zip',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            {
              value:
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              label: '.xlsx',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'application/msword',
              label: '.doc',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value:
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
              label: '.pptx',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value:
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              label: '.docx',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'application/vnd.ms-excel',
              label: '.xls',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'application/vnd.ms-powerpoint',
              label: '.ppt',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value:
                'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
              label: '.dotx',
              version: 7,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_roles: [
            {
              value: '1',
              label: '系统管理员',
              version: 4,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '默认用户',
              version: 3,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_example_user_conf: [
            {
              value: '2abd93dff1cece3ab3966b7fa14da5b4',
              label: 'sceneId',
              version: 11,
              tenantId: null,
              projectId: null,
            },
            {
              value: '20210124802862557596483584',
              label: 'clientId',
              version: 14,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'f129bd09600d9240c07e7194534d384a',
              label: 'channelId',
              version: 10,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_scene_status: [
            {
              value: '1',
              label: '已启用',
              version: 20,
              tenantId: null,
              projectId: null,
            },
            {
              value: '0',
              label: '已禁止',
              version: 18,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_channel_access_button: [
            {
              value: '1',
              label: '收回授权',
              version: 5,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '重新授权',
              version: 6,
              tenantId: null,
              projectId: null,
            },
            { value: '0', label: '授权', version: 8, tenantId: null, projectId: null },
          ],
          biz_id_type: [
            {
              value: '1',
              label: '身份证',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '户口簿',
              version: 8,
              tenantId: null,
              projectId: null,
            },
            { value: '3', label: '护照', version: 8, tenantId: null, projectId: null },
            {
              value: '4',
              label: '驾驶证',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '5',
              label: '统一社会信用代码',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            {
              value: '6',
              label: '组织机构代码',
              version: 7,
              tenantId: null,
              projectId: null,
            },
            { value: '0', label: '其他', version: 8, tenantId: null, projectId: null },
          ],
          biz_channel_type: [
            {
              value: '1',
              label: '内部使用',
              version: 9,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '外部使用',
              version: 7,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_engine_feature_status: [
            {
              value: '0',
              label: '待提取',
              version: 1,
              tenantId: null,
              projectId: null,
            },
            {
              value: '1',
              label: '提取中',
              version: 1,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '已完成',
              version: 1,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '提取失败',
              version: 1,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_card_type: [
            {
              value: '5',
              label: '统一社会信用代码',
              version: 5,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_menu_type: [
            { value: '0', label: '菜单', version: 16, tenantId: null, projectId: null },
            { value: '1', label: '操作', version: 17, tenantId: null, projectId: null },
          ],
          biz_islogin: [
            { value: '0', label: '否', version: 8, tenantId: null, projectId: null },
            { value: '1', label: '是', version: 12, tenantId: null, projectId: null },
          ],
          sys_monitor_service_name: [
            {
              value: 'cpms-sys',
              label: 'cpms-sys',
              version: 2,
              tenantId: null,
              projectId: null,
            },
          ],
          biz_channel_voice_recognized: [
            {
              value: '0',
              label: '识别失败',
              version: 2,
              tenantId: null,
              projectId: null,
            },
            {
              value: '1',
              label: '识别完成',
              version: 1,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '正在识别',
              version: 1,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_misfire_policy: [
            {
              value: '1',
              label: '立即执行',
              version: 5,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '执行一次',
              version: 5,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '放弃执行',
              version: 5,
              tenantId: null,
              projectId: null,
            },
          ],
          sys_service_center: [
            {
              value: 'sys_service_center_1',
              label: '服务能力中心1',
              version: 4,
              tenantId: null,
              projectId: null,
            },
            {
              value: '2',
              label: '服务能力中心2',
              version: 3,
              tenantId: null,
              projectId: null,
            },
            {
              value: '3',
              label: '服务能力中心3',
              version: 3,
              tenantId: null,
              projectId: null,
            },
          ],
          dic: [
            {
              value: 'global',
              label: '系统全局参数',
              version: 28,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'sys',
              label: '系统数据字典',
              version: 241,
              tenantId: null,
              projectId: null,
            },
            {
              value: 'biz',
              label: '业务数据字典',
              version: 261,
              tenantId: null,
              projectId: null,
            },
          ],
        },
      };
    },
  },
] as MockMethod[];
