import { defineStore } from 'pinia';
import { store } from '/@/stores';
import { getSettingsStorage, setSettingsStorage } from '/@/settings/settingsStorage';
import { deepMerge } from '/@/utils';
import { getAllMenuStorage } from '/@/router/tools/menuStorage';
import { Menu } from '/@/stores/types';

const allMenuList = getAllMenuStorage();
const finKeepAliveList = (list: Menu[] = [], pathList: string[] = ['/blankss']) => {
  list.map((item: Menu) => {
    if (window.__POWERED_BY_WUJIE__) {
      if (
        item?.meta?.microName === import.meta.env.VITE_APP_SHORT_NAME &&
        (item?.meta?.metaConfig || []).find((item) => item.key == 'KeepAlive')
      ) {
        pathList.push(item.path);
      }
    } else if (
      item.type === '0' &&
      (item?.meta?.metaConfig || []).find((item) => item.key == 'KeepAlive')
    ) {
      pathList.push(item.path);
    }
    if (item.children && item.children.length > 0) {
      finKeepAliveList(item.children, pathList);
    }
  });
  return pathList;
};

interface AppState {
  // Page loading status
  pageLoading: boolean;
  // project config
  projectConfig: Nullable<ProjectConfigs>;
  keepAliveList: string[];
  excludeList: string[];
  includeList: string[];
  pageKey: number;
}

export const useAppStore = defineStore({
  id: 'app',
  state: (): AppState => ({
    pageLoading: false,
    projectConfig: getSettingsStorage(),
    keepAliveList: ['/blankss'],
    // 页面缓存使用exclude时，需要排除的path数组
    excludeList: [],
    // 页面缓存使用include时，需要缓存的path数组
    includeList: ['/blankss'],
    pageKey: 0,
  }),
  getters: {
    getProjectConfig(): ProjectConfigs {
      return this.projectConfig || ({} as ProjectConfigs);
    },
    getMenuSetting(): MenuSetting {
      return this.getProjectConfig.menuSetting;
    },
    getKeepAliveList(): string[] {
      return this.keepAliveList;
    },
    getExcludeList(): string[] {
      return this.excludeList;
    },
    getIncludeList(): string[] {
      return this.includeList;
    },
    getPageKey(): number {
      return this.pageKey;
    },
  },
  actions: {
    setProjectConfig(settings: DeepPartial<ProjectConfigs>) {
      this.projectConfig = deepMerge(this.projectConfig || {}, settings);
      setSettingsStorage(this.projectConfig);
    },
    setKeepAliveList(list: string[]) {
      this.keepAliveList = list;
    },
    setExcludeList(list: string[]) {
      this.excludeList = list;
    },
    // 获取菜单成功后调用
    setKeepAliveListMenu() {
      this.keepAliveList = finKeepAliveList(allMenuList);
      this.includeList = finKeepAliveList(allMenuList);
    },
    setPageKey() {
      this.pageKey++;
    },
  },
});

export function useAppStoreWithOut() {
  return useAppStore(store);
}
