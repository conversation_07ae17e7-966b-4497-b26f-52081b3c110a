<template>
  <div class="role-container">
    <div class="role-header">
      <h3>角色管理</h3>
      <p>这是角色管理页面，用于管理系统角色和权限。</p>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchParams" class="demo-form-inline">
        <el-form-item label="角色名称">
          <el-input
            v-model="searchParams.roleName"
            placeholder="请输入角色名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="权限字符">
          <el-input
            v-model="searchParams.roleKey"
            placeholder="请输入权限字符"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus"> 新增角色 </el-button>
      <el-button type="info" @click="handleRefresh" :icon="Refresh"> 刷新 </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        height="400"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="roleName" label="角色名称" width="150" />
        <el-table-column prop="roleKey" label="权限字符" width="150" />
        <el-table-column prop="roleSort" label="显示顺序" width="100" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.status === '0'" type="success">正常</el-tag>
            <el-tag v-else type="danger">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column prop="remark" label="备注" width="200" />
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)"> 编辑 </el-button>
            <el-button type="primary" link @click="handleAssignMenus(row)">
              分配权限
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 角色编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="formData.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="权限字符" prop="roleKey">
          <el-input v-model="formData.roleKey" placeholder="请输入权限字符" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="roleSort">
          <el-input-number v-model="formData.roleSort" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态">
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus, Refresh } from '@element-plus/icons-vue';
  import {
    getRolePageList,
    saveRole,
    updateRole,
    deleteRoles,
    type RoleInfo,
    type RoleQueryParams
  } from '/@/api/sys/roles';

  // 响应式数据
  const tableRef = ref();
  const formRef = ref();

  const tableData = ref<RoleInfo[]>([]);
  const loading = ref(false);
  const submitLoading = ref(false);

  // 分页数据
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 对话框状态
  const dialogVisible = ref(false);
  const dialogTitle = ref('');
  const isEdit = ref(false);

  // 表单数据
  const formData = ref<RoleInfo>({
    roleName: '',
    roleKey: '',
    roleSort: 0,
    status: '0',
    remark: '',
  });

  // 表单验证规则
  const formRules = {
    roleName: [
      { required: true, message: '请输入角色名称', trigger: 'blur' }
    ],
    roleKey: [
      { required: true, message: '请输入权限字符', trigger: 'blur' }
    ],
    status: [
      { required: true, message: '请选择状态', trigger: 'change' }
    ]
  };

  // 搜索参数
  const searchParams = ref<RoleQueryParams>({});

  // 生命周期
  onMounted(() => {
    loadRoleList();
  });

  // 加载角色列表
  const loadRoleList = async () => {
    try {
      loading.value = true;
      const params = {
        ...searchParams.value,
        currentPage: pagination.current,
        pageSize: pagination.pageSize,
      };

      const result = await getRolePageList(params);
      if (result && result.records) {
        tableData.value = result.records;
        pagination.total = result.total;
      }
    } catch (error) {
      ElMessage.error('加载角色列表失败');
      console.error('Load role list error:', error);
    } finally {
      loading.value = false;
    }
  };

  // 搜索处理
  const handleSearch = () => {
    pagination.current = 1;
    loadRoleList();
  };

  // 重置搜索
  const handleReset = () => {
    searchParams.value = {};
    pagination.current = 1;
    loadRoleList();
  };

  // 刷新
  const handleRefresh = () => {
    loadRoleList();
  };

  // 分页处理
  const handlePageChange = (page: number) => {
    pagination.current = page;
    loadRoleList();
  };

  const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.current = 1;
    loadRoleList();
  };

  // 新增角色
  const handleAdd = () => {
    dialogTitle.value = '新增角色';
    isEdit.value = false;
    formData.value = {
      roleName: '',
      roleKey: '',
      roleSort: 0,
      status: '0',
      remark: '',
    };
    dialogVisible.value = true;
  };

  // 编辑角色
  const handleEdit = (record: RoleInfo) => {
    dialogTitle.value = '编辑角色';
    isEdit.value = true;
    formData.value = { ...record };
    dialogVisible.value = true;
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const valid = await formRef.value?.validate();
      if (!valid) return;

      submitLoading.value = true;

      if (isEdit.value) {
        await updateRole(formData.value);
        ElMessage.success('更新角色成功');
      } else {
        await saveRole(formData.value);
        ElMessage.success('新增角色成功');
      }

      dialogVisible.value = false;
      loadRoleList();
    } catch (error) {
      ElMessage.error(isEdit.value ? '更新角色失败' : '新增角色失败');
      console.error('Submit role error:', error);
    } finally {
      submitLoading.value = false;
    }
  };

  // 删除角色
  const handleDelete = async (record: RoleInfo) => {
    try {
      await ElMessageBox.confirm('确定要删除该角色吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      await deleteRoles(record.id!);
      ElMessage.success('删除角色成功');
      loadRoleList();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除角色失败');
        console.error('Delete role error:', error);
      }
    }
  };

  // 分配权限 - 暂时只显示消息
  const handleAssignMenus = (record: RoleInfo) => {
    ElMessage.info(`分配权限功能开发中，角色：${record.roleName}`);
  };
</script>

<style scoped lang="scss">
  .role-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    margin: 8px;

    .role-header {
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e6e8ee;

      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }

    .search-section {
      margin-bottom: 20px;
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
    }

    .action-section {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;
    }

    .table-section {
      .pagination-section {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }
</style>
