/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 统一的Axios API服务
 * 将所有chatagent相关的fetch API调用转换为axios格式
 */

import { defHttp } from '/@/utils/axios'
import type { RequestOptions } from '/#/axios'

// ==================== 接口定义 ====================

// DirectApiService 相关接口
export interface DirectStreamingRequest {
  chatId: string
  userId: string
  message: string
  toolContext?: Record<string, any>
  modelId?: string
}

export interface DirectStreamEvent {
  type: string
  planId?: string
  entityId?: string
  payload: string | any
  compressed?: boolean
}

// StreamingApiService 相关接口
export interface StreamingRequest {
  params: Record<string, any>
  chatId: string
  userId: string
  message: string
  toolContext: Record<string, any>
}

export interface StreamEvent {
  type: string
  planId?: string
  entityId?: string
  payload: string | any
  compressed?: boolean
}

// Model 相关接口
export interface Model {
  id: string
  name: string
  model: string
  configName?: string
  provider?: string
  description?: string
  status?: string
  type?: string
  maxTokens?: number
  supportStreaming?: boolean
}

// DataSource 相关接口
export interface DataSource {
  id: number
  name: string
  type: string
  host?: string
  port?: number
  database?: string
  schema?: string
  description?: string
  status?: string
}

export interface Database {
  name: string
  schemas?: Schema[]
}

export interface Schema {
  name: string
}
export interface TableScheme {
  columnList: any[] | null;
  comment: string | null;
  ddl: string | null;
  indexList: any[] | null;
  name: string;
  pinned: boolean;
}
// Table 相关接口
export interface Table {
  name: string
  comment?: string
  type?: string
  data?: TableScheme[]
}

export interface TableColumn {
  columnName: string
  dataType: string
  comment?: string
  isNullable?: boolean
  defaultValue?: string
  isPrimaryKey?: boolean
}

// 数据标注相关接口
export interface DataAnnotation {
  id?: string
  dataSourceId: number
  databaseName: string
  schemaName: string
  tableName: string
  columnName: string
  chineseName?: string
  showInDataset: boolean
  showInNumber: boolean
  foreignKeyTableName?: string
  foreignKeyColumnName?: string
  sortOrder?: number
  version?: number
}

// Agent 相关接口
export interface Agent {
  id: string
  name?: string
  description?: string
  availableTools?: string[]
  nextStepPrompt?: string
  userRequest?: string
  title?: string
  updateTime?: string
  planTemplateId?: string  // 添加 planTemplateId 字段
}

export interface Tool {
  key: string
  name: string
  description: string
  enabled: boolean
  serviceGroup?: string
}

// MCP 相关接口
export interface McpServer {
  id: number
  mcpServerName: string
  connectionType: 'STUDIO' | 'SSE'
  connectionConfig: string
}

export interface McpServerRequest {
  connectionType: 'STUDIO' | 'SSE'
  configJson: string
}

// Config 相关接口
export interface ConfigOption {
  value: string
  label: string
}

export interface ConfigItem {
  id: number
  configGroup: string
  configKey: string
  configValue: string
  inputType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'SELECT' | 'TEXTAREA' | 'CHECKBOX' | 'TEXT'
  configSubGroup?: string
  description?: string
  options?: (string | ConfigOption)[]
  min?: number
  max?: number
  _modified?: boolean
}

// ToolDetail 相关接口
export interface ToolDetailResponse {
  id: number
  parentExecutionId: number
  status: string
  toolName: string
  thinkStartTime: string
  thinkEndTime?: string
  actStartTime?: string
  actEndTime?: string
  thinkDetails: {
    thinkInput: string
    thinkOutput: string
  }
  actionDetails: {
    toolParameters: any
    actionResult: any
  }
  errorInfo?: string
}

export interface ToolParametersResponse {
  thinkActId: number
  toolName: string
  toolParameters: any
  actStartTime?: string
}

export interface ActionResultResponse {
  thinkActId: number
  toolName: string
  actionResult: any
  actEndTime?: string
  status: string
}

// 通用响应接口
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

// ==================== API服务类 ====================

/**
 * 统一的Axios API服务类
 */
export class AxiosApiService {
  
  // ==================== DirectApiService 转换 ====================
  
  /**
   * 直接发送任务（直接执行模式）
   */
  static async sendMessage(query: string, options?: RequestOptions): Promise<any> {
    return defHttp.post({
      url: '/api/executor/execute',
      data: { query },
    }, options)
  }

  /**
   * 发送消息并获取流式响应（使用新的/direct端点）
   * 注意：流式响应需要特殊处理，这里提供基础的POST请求
   */
  static async sendMessageWithStreamingDirect(
    planTemplateId: string,
    request: DirectStreamingRequest,
    options?: RequestOptions
  ): Promise<any> {
    const url = `/api/streaming-events/template/${encodeURIComponent(planTemplateId)}/direct`
    return defHttp.post({
      url,
      data: request,
      headers: {
        'Accept': 'text/event-stream'
      }
    }, options)
  }

  // ==================== CommonApiService 转换 ====================
  
  /**
   * 获取详细的执行记录
   */
  static async getDetails(planId: string, options?: RequestOptions): Promise<any | null> {
    try {
      return await defHttp.get({
        url: `/api/executor/details/${planId}`,
      }, options)
    } catch (error: any) {
      // 404 返回 null
      if (error?.response?.status === 404) {
        return null
      }
      return null
    }
  }

  /**
   * 提交用户表单输入
   */
  static async submitFormInput(planId: string, formData: any, options?: RequestOptions): Promise<any> {
    return defHttp.post({
      url: `/api/executor/submit-input/${planId}`,
      data: formData,
    }, options)
  }

  // ==================== StreamingApiService 转换 ====================
  
  /**
   * 发送消息并获取流式响应
   * 注意：流式响应需要特殊处理，这里提供基础的POST请求
   */
  static async sendMessageWithStreaming(
    planTemplateId: string,
    request: StreamingRequest,
    options?: RequestOptions
  ): Promise<any> {
    const url = `/api/streaming-events/template/${encodeURIComponent(planTemplateId)}`
    return defHttp.post({
      url,
      data: request,
      headers: {
        'Accept': 'text/event-stream'
      }
    }, options)
  }

  /**
   * 获取聊天历史
   */
  static async getChatHistory(chatId: string, options?: RequestOptions): Promise<any> {
    return defHttp.get({
      url: `/api/streaming-events/chat/history/${encodeURIComponent(chatId)}`,
    }, options)
  }

  /**
   * 获取聊天会话列表
   */
  static async getChatSessions(userId: string, options?: RequestOptions): Promise<any> {
    return defHttp.get({
      url: `/api/streaming-events/chat/sessions/${encodeURIComponent(userId)}`,
    }, options)
  }

  /**
   * 删除聊天会话
   */
  static async deleteChatSession(chatId: string, options?: RequestOptions): Promise<any> {
    return defHttp.delete({
      url: `/api/streaming-events/chat/session/${encodeURIComponent(chatId)}`,
    }, options)
  }

  /**
   * 获取聊天会话列表（分页）
   */
  static async getChatSessionsPaginated(
    // userId: string,
    planTemplateId: string,
    keyword?: string,
    options?: RequestOptions
  ): Promise<any> {
    return defHttp.get({
      url: `/api/streaming-events/chat/sessions?page=0&size=100&planTemplateId=${encodeURIComponent(planTemplateId)}&keyword=${keyword}`,
}, options)
  }

  /**
   * 获取聊天历史（通过聊天ID）
   */
  static async getChatHistoryById(chatId: string, options?: RequestOptions): Promise<any> {
    return defHttp.get({
      url: `/api/streaming-events/chat/${encodeURIComponent(chatId)}/history`,
    }, options)
  }

  // ==================== ModelApiService 转换 ====================

  /**
   * 获取大模型简要列表
   */
  static async getBriefModelList(options?: RequestOptions): Promise<Model[]> {
    const result = await defHttp.get({
      url: '/cpit/ai/setting/model/brief/list',
    }, options)

    // 处理响应数据格式
    if (result && typeof result === 'object' && result.data) {
      return result.data
    }
    if (Array.isArray(result)) {
      return result
    }
    return []
  }

  /**
   * 获取所有大模型列表（详细信息）
   */
  static async getAllModels(options?: RequestOptions): Promise<Model[]> {
    const result = await defHttp.get({
      url: '/cpit/ai/setting/model/list',
    }, options)

    // 处理响应数据格式
    if (result && typeof result === 'object' && result.data) {
      return result.data
    }
    if (Array.isArray(result)) {
      return result
    }
    return []
  }

  /**
   * 根据模型ID获取详细信息
   */
  static async getModelById(id: string, options?: RequestOptions): Promise<Model | null> {
    try {
      const result = await defHttp.get({
        url: `/cpit/ai/setting/model/${encodeURIComponent(id)}`,
      }, options)

      // 处理响应数据格式
      if (result && typeof result === 'object' && result.data) {
        return result.data
      }
      if (result && typeof result === 'object' && result.id) {
        return result
      }
      return null
    } catch (error) {
      console.error('获取大模型详情失败:', error)
      return null
    }
  }

  /**
   * 测试模型可用性
   */
  static async testModel(id: string, options?: RequestOptions): Promise<boolean> {
    try {
      const result = await defHttp.post({
        url: `/cpit/ai/setting/model/${encodeURIComponent(id)}/test`,
      }, options)

      return result.success === true
    } catch (error) {
      console.error('测试大模型失败:', error)
      return false
    }
  }

  /**
   * 获取默认模型ID
   */
  static async getDefaultModelId(options?: RequestOptions): Promise<string | null> {
    try {
      const result = await defHttp.get({
        url: '/cpit/ai/setting/model/default',
      }, options)

      if (result && result.id) {
        return result.id
      }
      return null
    } catch (error) {
      console.error('获取默认模型失败:', error)
      return null
    }
  }

  // ==================== DataSourceApiService 转换 ====================

  /**
   * 获取所有数据源列表
   */
  static async getDataSourceList(options?: RequestOptions): Promise<DataSource[]> {
    const result = await defHttp.get({
      url: `/api/connection/datasource/list`,
      params: {
        pageNo: 1,
        pageSize: 1000,
        refresh: true
      }
    }, options)

    // 根据API结构提取数据
    if (result && result.data) {
      return result.data.map((item: any) => ({
        id: item.id,
        name: item.alias || `数据源${item.id}`,
        type: item.type,
        host: item.host,
        port: item.port,
        description: item.url,
        supportDatabase: item.supportDatabase,
        supportSchema: item.supportSchema
      }))
    }
    return []
  }

  /**
   * 根据数据源ID获取数据库列表
   */
  static async getDatabaseList(dataSourceId: number, options?: RequestOptions): Promise<Database[]> {
    const result = await defHttp.get({
      url: '/api/rdb/database/list?refresh=true',
      params: { dataSourceId }
    }, options)

    // 根据API结构提取数据
    if (result) {
      return result.map((item: any) => ({
        name: item.name,
        description: item.description,
        count: item.count
      }))
    }
    return []
  }

  /**
   * 根据数据源ID和数据库名获取模式列表
   */
  static async getSchemaList(dataSourceId: number, databaseName: string, options?: RequestOptions): Promise<Schema[]> {
    const result = await defHttp.get({
      url: '/api/rdb/schema/list',
      params: {
        dataSourceId,
        databaseName: encodeURIComponent(databaseName),
        refresh: true
      }
    }, options)

    // 根据API结构提取数据
    if (result) {
      return result.map((item: any) => ({
        name: item.name
      }))
    }
    return []
  }
  // 根据数据源等信息获取数据表列表
  static async getTableList(dataSourceId: number, databaseName: string, schemaName: string, options?: RequestOptions): Promise<Table[]> {
    const result = await defHttp.get({
      url: '/api/rdb/table/list',
      params: { dataSourceId, databaseName, schemaName,refresh: true,pageNo: 1, pageSize: 1000 }
    }, options)
    return result
  }

  /**
   * 根据数据源等信息获取数据表字段列表
   */
  static async getTableColumns(dataSourceId: number, dataSourceName : string, databaseType: string, databaseName: string, schemaName: string, tableName: string, pageNo: number, pageSize: number,refresh?:boolean, options?: RequestOptions): Promise<TableColumn[]> {
    const result = await defHttp.get({
      url: '/api/rdb/table/query',
      params: { dataSourceId,dataSourceName, databaseType, databaseName, schemaName, tableName,pageNo, pageSize,refresh }
    }, options)

    // 根据API结构提取数据
    if (result && Array.isArray(result.columnList)) {
      return result.columnList.map((item: any) => ({
        columnName: item.name,
        primaryKey: item.primaryKey ,
        comment: item.comment || item.description,
        primaryKeyName: item.primaryKeyName,
        schemaName: item.schemaName,
        tableName: item.tableName
      }))
    }
    return []
  }

  // ==================== DataAnnotation 相关接口 ====================

  /**
   * 获取数据标注列表
   */
  static async getDataAnnotations(dataSourceId: number, databaseName: string, schemaName: string, tableName: string, options?: RequestOptions): Promise<DataAnnotation[]> {
    const result = await defHttp.get({
      url: '/cpit/ai/mark/list',
      params: { dataSourceId, databaseName, schemaName, tableName }
    }, options)

    if (result && Array.isArray(result?.columnAiMarkS)) {
      return result
    }
    return []
  }
  // 同步数据标注列表 接口地址/cpit/ai/mark/sync/vector post方法 参数 dataSourceId, databaseName, schemaName, tableName
  static async syncDataAnnotations(params: any, options?: RequestOptions): Promise<any> {
    return defHttp.post({
      url: '/cpit/ai/mark/sync/vector',
      data: params
    }, options)
  }

  /**
   * 保存数据标注
   */
  /**
   * 保存数据标注（新版，支持表级结构）
   */
  static async saveDataAnnotations(params: any, options?: RequestOptions): Promise<any> {
    return defHttp.post({
      url: '/cpit/ai/mark',
      data: params
    }, options)
  }

  /**
   * 删除数据标注
   */
  static async deleteDataAnnotation(id: string, options?: RequestOptions): Promise<any> {
    return defHttp.delete({
      url: `/api/data-annotation/${id}`
    }, options)
  }

  /**
   * 根据数据源ID获取详细信息
   */
  static async getDataSourceById(id: number, options?: RequestOptions): Promise<DataSource | null> {
    try {
      const result = await defHttp.get({
        url: `/api/connection/datasource/${id}`,
      }, options)

      // 处理响应数据格式
      if (result && typeof result === 'object' && result.data) {
        return result.data
      }
      if (result && typeof result === 'object' && result.id) {
        return result
      }
      return null
    } catch (error) {
      console.error('获取数据源详情失败:', error)
      return null
    }
  }

  // ==================== AgentApiService 转换 ====================

  /**
   * 获取所有 Agent 列表
   */
  static async getAllAgents(options?: RequestOptions): Promise<Agent[]> {
    return defHttp.get({
      url: '/api/plan-template/list/published',
    }, options)
  }

  /**
   * 根据 ID 获取 Agent 详情
   */
  static async getAgentById(id: string, options?: RequestOptions): Promise<Agent> {
    return defHttp.get({
      url: `/api/agents/${id}`,
    }, options)
  }

  /**
   * 创建新的 Agent
   */
  static async createAgent(agentConfig: Omit<Agent, 'id'>, options?: RequestOptions): Promise<Agent> {
    return defHttp.post({
      url: '/api/agents',
      data: agentConfig,
    }, options)
  }

  /**
   * 更新 Agent 配置
   */
  static async updateAgent(id: string, agentConfig: Agent, options?: RequestOptions): Promise<Agent> {
    return defHttp.put({
      url: `/api/agents/${id}`,
      data: agentConfig,
    }, options)
  }

  /**
   * 删除 Agent
   */
  static async deleteAgent(id: string, options?: RequestOptions): Promise<void> {
    return defHttp.delete({
      url: `/api/agents/${id}`,
    }, options)
  }

  /**
   * 获取所有可用工具列表
   */
  static async getAvailableTools(options?: RequestOptions): Promise<Tool[]> {
    return defHttp.get({
      url: '/api/agents/tools',
    }, options)
  }

  // ==================== McpApiService 转换 ====================

  /**
   * 获取所有 MCP 服务器配置
   */
  static async getAllMcpServers(options?: RequestOptions): Promise<McpServer[]> {
    return defHttp.get({
      url: '/api/mcp/list',
    }, options)
  }

  /**
   * 添加新的 MCP 服务器配置
   */
  static async addMcpServer(mcpConfig: McpServerRequest, options?: RequestOptions): Promise<ApiResponse> {
    try {
      await defHttp.post({
        url: '/api/mcp/add',
        data: mcpConfig,
      }, options)

      return { success: true, message: '添加 MCP 服务器成功' }
    } catch (error) {
      console.error('添加 MCP 服务器失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '添加失败，请重试'
      }
    }
  }

  /**
   * 删除 MCP 服务器配置
   */
  static async removeMcpServer(id: number, options?: RequestOptions): Promise<ApiResponse> {
    try {
      await defHttp.get({
        url: '/api/mcp/remove',
        params: { id }
      }, options)

      return { success: true, message: '删除 MCP 服务器成功' }
    } catch (error) {
      console.error(`删除 MCP 服务器[${id}]失败:`, error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '删除失败，请重试'
      }
    }
  }

  // ==================== AdminApiService 转换 ====================

  /**
   * 根据组名获取配置项
   */
  static async getConfigsByGroup(groupName: string, options?: RequestOptions): Promise<ConfigItem[]> {
    return defHttp.get({
      url: `/api/config/group/${groupName}`,
    }, options)
  }

  /**
   * 批量更新配置项
   */
  static async batchUpdateConfigs(configs: ConfigItem[], options?: RequestOptions): Promise<ApiResponse> {
    if (!configs || configs.length === 0) {
      return { success: true, message: '没有需要更新的配置' }
    }

    try {
      await defHttp.post({
        url: '/api/config/batch-update',
        data: configs,
      }, options)

      return { success: true, message: '配置保存成功' }
    } catch (error) {
      console.error('批量更新配置失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '更新失败，请重试'
      }
    }
  }

  /**
   * 获取单个配置项
   */
  static async getConfigById(id: number, options?: RequestOptions): Promise<ConfigItem> {
    return defHttp.get({
      url: `/api/config/${id}`,
    }, options)
  }

  /**
   * 更新单个配置项
   */
  static async updateConfig(config: ConfigItem, options?: RequestOptions): Promise<ApiResponse> {
    try {
      await defHttp.put({
        url: `/api/config/${config.id}`,
        data: config,
      }, options)

      return { success: true, message: '配置更新成功' }
    } catch (error) {
      console.error('更新配置项失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '更新失败，请重试'
      }
    }
  }

  // ==================== ToolDetailApiService 转换 ====================

  /**
   * 获取工具的完整详细信息
   */
  static async getToolDetail(thinkActId: string | number, options?: RequestOptions): Promise<ToolDetailResponse | null> {
    try {
      return await defHttp.get({
        url: `/api/streaming-events/think-act/${thinkActId}/detail`,
      }, options)
    } catch (error: any) {
      if (error?.response?.status === 404) {
        console.warn('⚠️ 工具详情不存在:', thinkActId)
        return null
      }
      console.error('❌ 获取工具详情失败:', error)
      throw error
    }
  }

  /**
   * 获取工具参数
   */
  static async getToolParameters(thinkActId: string | number, options?: RequestOptions): Promise<ToolParametersResponse | null> {
    try {
      return await defHttp.get({
        url: `/api/streaming-events/think-act/${thinkActId}/tool-parameters`,
      }, options)
    } catch (error: any) {
      if (error?.response?.status === 404) {
        console.warn('⚠️ 工具参数不存在:', thinkActId)
        return null
      }
      console.error('❌ 获取工具参数失败:', error)
      throw error
    }
  }

  /**
   * 获取执行结果
   */
  static async getActionResult(thinkActId: string | number, options?: RequestOptions): Promise<ActionResultResponse | null> {
    try {
      return await defHttp.get({
        url: `/api/streaming-events/think-act/${thinkActId}/action-result`,
      }, options)
    } catch (error: any) {
      if (error?.response?.status === 404) {
        console.warn('⚠️ 执行结果不存在:', thinkActId)
        return null
      }
      console.error('❌ 获取执行结果失败:', error)
      throw error
    }
  }

  /**
   * 批量获取多个工具的详细信息
   */
  static async getBatchToolDetails(thinkActIds: (string | number)[], options?: RequestOptions): Promise<(ToolDetailResponse | null)[]> {
    try {
      console.log('🔍 批量获取工具详情:', thinkActIds)

      const promises = thinkActIds.map(id => this.getToolDetail(id, options))
      const results = await Promise.allSettled(promises)

      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value
        } else {
          console.error(`❌ 获取工具详情失败 [${thinkActIds[index]}]:`, result.reason)
          return null
        }
      })
    } catch (error: any) {
      console.error('❌ 批量获取工具详情失败:', error)
      throw error
    }
  }

  // ==================== PlanActApiService 转换 ====================

  /**
   * 生成计划
   */
  static async generatePlan(query: string, existingJson?: string, options?: RequestOptions): Promise<any> {
    const requestBody: Record<string, any> = { query }
    if (existingJson) requestBody.existingJson = existingJson

    const responseData = await defHttp.post({
      url: '/api/plan-template/generate',
      data: requestBody,
    }, options)

    if (responseData.planJson) {
      try {
        responseData.plan = JSON.parse(responseData.planJson)
      } catch (e) {
        responseData.plan = { error: '无法解析计划数据' }
      }
    }
    return responseData
  }

  /**
   * 执行已生成的计划
   */
  static async executePlan(planTemplateId: string, rawParam?: string, options?: RequestOptions): Promise<any> {
    const requestBody: Record<string, any> = { planTemplateId }
    if (rawParam) requestBody.rawParam = rawParam

    return defHttp.post({
      url: '/api/plan-template/executePlanByTemplateId',
      data: requestBody,
    }, options)
  }

  /**
   * 保存计划到服务器
   */
  static async savePlanTemplate(planId: string, planJson: string, options?: RequestOptions): Promise<any> {
    return defHttp.post({
      url: '/api/plan-template/save',
      data: { planId, planJson },
    }, options)
  }

  /**
   * 获取计划的所有版本
   */
  static async getPlanVersions(planId: string, options?: RequestOptions): Promise<any> {
    return defHttp.post({
      url: '/api/plan-template/versions',
      data: { planId },
    }, options)
  }

  /**
   * 获取特定版本的计划
   */
  static async getVersionPlan(planId: string, versionIndex: number, options?: RequestOptions): Promise<any> {
    return defHttp.post({
      url: '/api/plan-template/get-version',
      data: { planId, versionIndex: versionIndex.toString() },
    }, options)
  }

  /**
   * 获取所有计划模板列表
   */
  static async getAllPlanTemplates(options?: RequestOptions): Promise<any> {
    return defHttp.get({
      url: '/api/plan-template/list',
    }, options)
  }

  /**
   * 更新现有计划模板
   */
  static async updatePlanTemplate(planId: string, query: string, existingJson?: string, options?: RequestOptions): Promise<any> {
    const requestBody: Record<string, any> = { planId, query }
    if (existingJson) requestBody.existingJson = existingJson

    const responseData = await defHttp.post({
      url: '/api/plan-template/update',
      data: requestBody,
    }, options)

    if (responseData.planJson) {
      try {
        responseData.plan = JSON.parse(responseData.planJson)
      } catch (e) {
        responseData.plan = { error: '无法解析计划数据' }
      }
    }
    return responseData
  }

  /**
   * 删除计划模板
   */
  static async deletePlanTemplate(planId: string, options?: RequestOptions): Promise<any> {
    return defHttp.post({
      url: '/api/plan-template/delete',
      data: { planId },
    }, options)
  }

  // ==================== 工具方法 ====================

  /**
   * 构建工具上下文对象
   */
  static buildToolContext(dataSourceId: number, databaseName?: string, schemaName?: string): Record<string, any> {
    const context: Record<string, any> = {
      dataSourceId
    }

    if (databaseName) {
      context.databaseName = databaseName
    }

    if (schemaName) {
      context.schemaName = schemaName
    }

    return context
  }

  /**
   * 格式化模型显示名称
   */
  static formatModelDisplayName(model: Model): string {
    if (model.provider) {
      return `${model.name} (${model.provider})`
    }
    return model.name
  }

  /**
   * 检查模型是否支持流式输出
   */
  static isStreamingSupported(model: Model): boolean {
    return model.supportStreaming === true
  }

  /**
   * 根据类型过滤模型
   */
  static filterModelsByType(models: Model[], type: string): Model[] {
    return models.filter(model => model.type === type)
  }

  /**
   * 根据提供商过滤模型
   */
  static filterModelsByProvider(models: Model[], provider: string): Model[] {
    return models.filter(model => model.provider === provider)
  }

  /**
   * 格式化工具参数为可读的JSON字符串
   */
  static formatParameters(parameters: any): string {
    if (!parameters) return '暂无参数'

    try {
      if (typeof parameters === 'string') {
        // 如果已经是字符串，尝试解析再格式化
        const parsed = JSON.parse(parameters)
        return JSON.stringify(parsed, null, 2)
      } else {
        // 如果是对象，直接格式化
        return JSON.stringify(parameters, null, 2)
      }
    } catch (e) {
      // 如果解析失败，返回原始字符串
      return String(parameters)
    }
  }

  /**
   * 格式化执行结果为可读的JSON字符串
   */
  static formatResult(result: any): string {
    if (!result) return '暂无结果'

    try {
      if (typeof result === 'string') {
        // 如果已经是字符串，尝试解析再格式化
        const parsed = JSON.parse(result)
        return JSON.stringify(parsed, null, 2)
      } else {
        // 如果是对象，直接格式化
        return JSON.stringify(result, null, 2)
      }
    } catch (e) {
      // 如果解析失败，返回原始字符串
      return String(result)
    }
  }
}
