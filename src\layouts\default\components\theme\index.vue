<template>
  <div class="theme-area">
    <el-divider>颜色设置</el-divider>
    <ul class="setting">
      <li>
        <span>主题颜色</span>

        <el-icon
          v-for="(item, index) in themeColors"
          :key="index"
          :style="getThemeColorStyle(item.color)"
          @click="setEpThemeColor(item.themeColor, index, 'main')"
          style="margin: 0.1em 0.1em 0 0"
          :size="17"
          :color="getThemeColor(item.themeColor)"
        >
          <Check class="check-icon" v-show="colorIndexObject.main == index" />
        </el-icon>
      </li>
      <li>
        <span>菜单颜色</span>
        <el-icon
          v-for="(item, index) in themeColors"
          :key="index"
          :style="getThemeColorStyle(item.color)"
          @click="setEpThemeColor(item.themeColor, index, 'menu')"
          style="margin: 0.1em 0.1em 0 0"
          :size="17"
          :color="getThemeColor(item.themeColor)"
        >
          <Check class="check-icon" v-show="colorIndexObject.menu == index" />
        </el-icon>
      </li>
      <li>
        <span>导航颜色</span>
        <el-icon
          v-for="(item, index) in themeColors"
          :key="index"
          :style="getThemeColorStyle(item.color)"
          @click="setEpThemeColor(item.themeColor, index, 'nav')"
          style="margin: 0.1em 0.1em 0 0"
          :size="17"
          :color="getThemeColor(item.themeColor)"
        >
          <Check class="check-icon" v-show="colorIndexObject.nav == index" />
        </el-icon>
      </li>
      <!-- <li>
        <span>背景颜色</span>
        <el-icon
          v-for="(item, index) in themeColors"
          :key="index"
          :style="getThemeColorStyle(item.color)"
          @click="setEpThemeColor(item.themeColor, index, 'content')"
          style="margin: 0.1em 0.1em 0 0"
          :size="17"
          :color="getThemeColor(item.themeColor)"
        >
          <Check class="check-icon" v-show="colorIndexObject.content == index" />
        </el-icon>
      </li> -->
    </ul>
    <el-divider>暗黑模式</el-divider>
    <!-- :active-icon="dayIcon"
      :inactive-icon="darkIcon" -->
    <el-switch
      inline-prompt
      v-model="dataTheme"
      class="cpvf-datatheme"
      active-text="开"
      inactive-text="关"
      @change="dataThemeChange"
    />
    <el-divider>界面显示</el-divider>
    <ul class="setting">
      <li v-show="!dataTheme">
        <span>灰色模式</span>
        <el-switch
          v-model="greyVal"
          inline-prompt
          inactive-color="#a6a6a6"
          active-text="开"
          inactive-text="关"
          @change="greyChange"
        />
      </li>
      <li v-show="!dataTheme">
        <span>色弱模式</span>
        <el-switch
          v-model="weakVal"
          inline-prompt
          inactive-color="#a6a6a6"
          active-text="开"
          inactive-text="关"
          @change="weekChange"
        />
      </li>
    </ul>
    <ProjectSettings />
  </div>
</template>
<script setup lang="ts" name="theme-setting">
  import ProjectSettings from './ProjectSettings.vue';
  import { computed, ref, getCurrentInstance } from 'vue';
  import { TinyColor } from '@ctrl/tinycolor';
  import { createNewStyle, writeNewStyle, themeColorList } from './element-plus';
  import { find } from 'lodash-unified';
  import { useSettingStoreWithOut } from '/@/stores/modules/settings';
  import WujieVue from 'wujie-vue3';
  // 保存选中的主题
  const settingStore = useSettingStoreWithOut();
  const body = document.documentElement as HTMLElement;
  const instanceConfig =
    getCurrentInstance().appContext.app.config.globalProperties.$config;
  let themeColors = ref<Array>(themeColorList);

  const shadeBgColor = (color: string): string => {
    return new TinyColor(color).shade(10).toString();
  };
  const colorObject = ref<ThemeColorObject>(settingStore.getTheme);
  // settingStore.getTheme
  const colorIndexObject = ref({
    main: -1,
    nav: -1,
    menu: -1,
    content: -1,
  });
  const { bus } = WujieVue;
  const setEpThemeColor = (color: string, index: string, type: string) => {
    if (color === 'default' || color === 'light') {
      color = '#1a5efe';
    } else {
      const colors = find(themeColors.value, { themeColor: color });
      color = colors.color;
    }
    colorObject.value[type] = color;
    colorIndexObject.value[type] = index;
    if (type != 'main') {
      writeNewStyle(createNewStyle(colorObject.value));
    } else {
      writeNewStyle(createNewStyle(colorObject.value));
      body.style.setProperty('--el-color-primary-active', shadeBgColor(color));
    }
    settingStore.setEpThemeColor(colorObject.value);
    bus.$emit('sendMicroMessage', {
      message: { colorObject: colorObject.value },
      type: 'changeTheme',
    });
  };
  const getThemeColorStyle = computed(() => {
    return (color) => {
      return { background: color };
    };
  });
  let layoutTheme =
    // ref(storageLocal.getItem('responsive-layout')) ||
    ref({
      layout: instanceConfig?.Layout ?? 'vertical',
      theme: instanceConfig?.Theme ?? 'default',
    });
  // 主题色 激活选择项
  const getThemeColor = computed(() => {
    return (current) => {
      if (current === layoutTheme.value.theme && layoutTheme.value.theme !== 'light') {
        return '#fff';
      } else if (
        current === layoutTheme.value.theme &&
        layoutTheme.value.theme === 'light'
      ) {
        return '#1d2b45';
      } else {
        return 'transparent';
      }
    };
  });
  const greyVal = ref(false);
  // 灰色模式设置 value
  const greyChange = (): void => {
    toggleClass(greyVal.value, 'html-grey', document.querySelector('html'));
    // storageConfigureChange("grey", value);
  };
  const weakVal = ref(false);
  // 色弱模式设置 value
  const weekChange = (): void => {
    toggleClass(weakVal.value, 'html-weakness', document.querySelector('html'));
    // storageConfigureChange('weak', value);
  };
  function toggleClass(flag: boolean, clsName: string, target?: HTMLElement) {
    const targetEl = target || document.body;
    let { className } = targetEl;
    className = className.replace(clsName, '').trim();
    targetEl.className = flag ? `${className} ${clsName} ` : className;
  }
  let dataTheme = ref<boolean>(false);

  // 日间、夜间主题切换
  function dataThemeChange() {
    if (dataTheme.value) {
      body.setAttribute('data-theme', 'dark');
      // setLayoutThemeColor("light");
    } else {
      body.setAttribute('data-theme', '');
    }
  }
</script>
<style scoped>
  .theme-color {
    width: 100%;
    height: 40px;
    margin-top: 20px;
    display: flex;
    justify-content: center;

    li {
      float: left;
      width: 20px;
      height: 20px;
      margin-top: 8px;
      margin-right: 8px;
      font-weight: 700;
      text-align: center;
      border-radius: 2px;
      cursor: pointer;

      &:nth-child(2) {
        border: 1px solid #ddd;
      }
    }
  }

  .theme-area {
    padding: 0 10px;
  }

  .setting {
    width: 100%;

    li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 25px;
    }
  }

  .check-icon {
    color: #fff;
  }

  .el-icon {
    border: 1px #1c1b1b solid;
  }
</style>
