<template>
  <div class="tools-management">
    <!-- 工具栏 -->
    <!-- <div class="toolbar">
      <div class="toolbar-left">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索工具名称..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <el-select
          v-model="statusFilter"
          placeholder="所有状态"
          clearable
          @change="handleFilter"
        >
          <el-option label="所有状态" value="" />
          <el-option label="已连接" value="connected" />
          <el-option label="未连接" value="disconnected" />
        </el-select>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          添加工具
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div> -->
    <div class="agent-set-top">
      <el-input
        v-model="searchKeyword"
        style="width: 240px; margin-right: 12px"
        placeholder="搜索工具名称"
        :prefix-icon="Search"
        clearable
        @clear="handleSearch"
      />
      <el-select
        style="width: 240px; margin-right: 12px"
        v-model="statusFilter"
        placeholder="所有状态"
        clearable
        @change="handleFilter"
        @clear="handleSearch"
      >
        <el-option label="所有状态" value="" />
        <el-option label="已连接" value="connected" />
        <el-option label="未连接" value="disconnected" />
      </el-select>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button type="primary" @click="showAddDialog" :icon="Plus">新建</el-button>
    </div>
    <!-- 工具列表 -->
    <!-- <div v-loading="loading" class="tools-grid">
      <div
        v-for="tool in filteredTools"
        :key="tool.id"
        class="tool-card"
        @click="handleToolClick(tool)"
      >
        <div class="card-header">
          <div class="tool-info">
            <h3 class="tool-name">{{ tool.name }}</h3>
            <span class="tool-type">{{ tool.type || 'MCP' }}</span>
          </div>
          <div class="tool-status">
            <el-tag
              :type="tool.status === 'connected' ? 'success' : 'danger'"
              size="small"
            >
              {{ tool.status === 'connected' ? '已连接' : '未连接' }}
            </el-tag>
          </div>
        </div>

        <div class="card-content">
          <p class="tool-description">{{ tool.description || '暂无描述' }}</p>

          <div class="tool-details">
            <div class="detail-item">
              <span class="label">命令:</span>
              <span class="value">
                <pre>{{ JSON.stringify(JSON.parse(tool.configJson), null, 2) }}</pre>
              </span>
  
            </div>
          </div>
        </div>

        <div class="card-actions">
          <el-button size="small" @click.stop="editTool(tool)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button size="small" type="danger" @click.stop="deleteTool(tool)">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div> -->

    <div v-loading="loading" class="card-content">
      <div>
        <el-row :gutter="20">
          <el-col
            v-for="(item, index) in filteredItems"
            :key="index"
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <div class="card">
              <p class="card-top">
                <span class="card-top-name">{{ item.name }} </span>
                <span class="card-top-id">{{ item.id }}</span>
              </p>
              <div class="card-tools">
                <span class="tool-tag">
                  {{ item.connectionType }}
                </span>
                <el-tag
                  :type="item.status === 'connected' ? 'success' : 'danger'"
                  size="small"
                  style="margin-left: 16px"
                >
                  {{ item.status === 'connected' ? '已连接' : '未连接' }}
                </el-tag>
              </div>
              <div class="card-description">
                <!-- {{ item.description }} -->
                <pre style="font-size: 10px">{{
                  JSON.stringify(JSON.parse(item.configJson), null, 2)
                }}</pre>
              </div>

              <div class="card-bottom">
                <div class="card-bottom-actions">
                  <!-- <el-button @click="handleEdit(item)" :icon="Edit">编辑</el-button> -->
                  <el-popconfirm
                    :icon="InfoFilled"
                    title="确定要删除吗?"
                    @confirm="handleDelete(item.id)"
                  >
                    <template #reference>
                      <el-button :icon="DeleteFilled">删除</el-button>
                    </template>
                  </el-popconfirm>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- 空状态 -->
      <div
        v-if="!loading && filteredItems.length === 0"
        style="background: #ffffff; height: calc(100% - 10px)"
      >
        <el-empty description="暂无工具数据" style="height: 100%">
          <el-button type="primary" @click="showAddDialog">添加第一个工具</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 添加/编辑MCP服务器对话框 -->
    <el-drawer v-model="dialogVisible" size="800px" @close="resetForm">
      <template #header>
        <p class="drawer-title">新建MCP服务</p>
      </template>
      <div class="mcp-dialog-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <div class="section-header">
            <el-icon><InfoFilled /></el-icon>
            <span>基本信息</span>
          </div>

          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="100px"
          >
            <!-- <el-form-item label="服务器名称" prop="name">
              <el-input v-model="formData.name" placeholder="输入MCP服务器名称" />
              <div class="form-tip">请一个好记的MCP服务器名称</div>
            </el-form-item> -->

            <el-form-item label="连接类型" prop="connectionType">
              <el-radio-group
                v-model="formData.connectionType"
                class="connection-radio-group"
                @change="handleChange"
              >
                <el-radio value="STUDIO" class="connection-radio">
                  <span class="radio-content">
                    <span class="radio-title">STUDIO</span>
                    <span class="radio-desc"
                      >本地mcp server，目前市面上主流的是这个</span
                    >
                  </span>
                </el-radio>
                <el-radio value="SSE" class="connection-radio">
                  <span class="radio-content">
                    <span class="radio-title">SSE</span>
                    <span class="radio-desc">一种远程mcp server 链接协议</span>
                  </span>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>

        <!-- 配置信息 -->
        <div class="info-section">
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>配置信息</span>
          </div>

          <div class="config-section">
            <div class="config-label">连接配置</div>
            <el-input
              v-model="formData.configJson"
              type="textarea"
              :rows="12"
              placeholder="请输入MCP服务器配置JSON"
              class="config-textarea"
            />
            <div class="form-tip">请输入完整的MCP服务器配置JSON，参考下方预设示例</div>
          </div>
        </div>

        <!-- 预设示例 -->
        <div class="info-section">
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>预设示例</span>
          </div>

          <div class="preset-examples">
            <div class="example-item">
              <div class="example-title"
                >{{
                  formData.connectionType === 'STUDIO' ? '顺序思维服务' : '实时数据搜索'
                }}
              </div>
              <div class="example-config">
                <pre>{{
                  formData.connectionType === 'STUDIO' ? studioExample : sseExample
                }}</pre>
              </div>
              <el-button size="small" @click="useExample(formData.connectionType)"
                >使用此配置</el-button
              >
            </div>

            <!-- <div class="example-item">
              <div class="example-title">文件系统服务</div>
              <div class="example-config">
                <pre>{{ filesystemExample }}</pre>
              </div>
              <el-button size="small" @click="useExample('filesystem')"
                >使用此配置</el-button
              >
            </div> -->
          </div>
        </div>

        <!-- 使用说明 -->
        <div class="info-section">
          <div class="section-header">
            <el-icon><QuestionFilled /></el-icon>
            <span>使用说明</span>
          </div>

          <div class="instructions">
            <ol>
              <li>
                找到你要用的mcp server的配置json：
                <ul>
                  <li
                    ><strong>本地(STDIO)</strong>：可以在
                    <a href="https://mcp.so" target="_blank">mcp.so</a>
                    上找到，需要你有Node.js环境并理解你要配置的json里面的每一个项，做对应调整比如配置ak</li
                  >
                  <li
                    ><strong>远程服务(SSE)</strong>：<a
                      href="https://mcp.higress.ai/"
                      target="_blank"
                      >mcp.higress.ai/</a
                    >
                    上可以找到，有SSE和STREAMING两种，目前SSE协议更完备一些</li
                  >
                </ul>
              </li>
              <li
                >将json配置复制到上面的输入框，本地选STUDIO，远程选STREAMING或SSE，提交</li
              >
              <li
                >这样mcp
                tools就注册成功了。默认会放在DEFAULT_AGENT下面，如果tools过多，对上下文有压力，多出的tools会被忽略</li
              >
              <li
                >推荐在Agent配置里面，新建一个agent，然后增加指定的tools，这样可以极大减少冲突，增强tools被agent选择的准确性</li
              >
            </ol>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { ElMessage } from 'element-plus';
  import {
    Plus,
    Search,
    InfoFilled,
    Setting,
    Document,
    QuestionFilled,
    DeleteFilled,
  } from '@element-plus/icons-vue';
  import {
    getMcpServersApi,
    addMcpServerApi,
    removeMcpServerApi,
    // getMcpToolsApi,
  } from '../../api/agentSet';
  import { cloneDeep } from 'lodash-es';
  // import JsonEditorVue from 'json-editor-vue';
  // 响应式数据
  const loading = ref(false);
  const submitting = ref(false);
  const dialogVisible = ref(false);
  const isEdit = ref(false);
  const searchKeyword = ref('');
  const statusFilter = ref('');
  const tools = ref<any[]>([]);
  const filteredItems = ref<any[]>([]);
  const formRef = ref();

  // 表单数据
  const formData = reactive({
    id: '',
    // name: '',
    connectionType: 'STUDIO',
    configJson: '',
  });

  // 预设示例
  const studioExample = ref(`{
  "mcpServers": {
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}`);

  const sseExample = ref(`{
  "mcpServers": {
    "tavily-mcp": {
      "command": "npx.cmd",
      "args": [
        "-y",
        "tavily-mcp"
      ],
      "env": {
        "TAVILY_API_KEY": ""
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}`);

  // 表单验证规则
  const formRules = {
    // name: [{ required: true, message: '请输入服务器名称', trigger: 'blur' }],
    connectionType: [{ required: true, message: '请选择连接类型', trigger: 'change' }],
    configJson: [
      { required: true, message: '请输入配置JSON', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value) {
            JSON.parse(value);
            callback();
          } else {
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
  };

  // 计算属性 - 过滤后的工具列表
  // const filteredTools = computed(() => {
  //   let result = tools.value;

  //   // 搜索过滤
  //   if (searchKeyword.value) {
  //     const keyword = searchKeyword.value.toLowerCase();
  //     result = result.filter(
  //       (tool) =>
  //         tool.name?.toLowerCase().includes(keyword) ||
  //         tool.description?.toLowerCase().includes(keyword),
  //     );
  //   }

  //   // 状态过滤
  //   if (statusFilter.value) {
  //     result = result.filter((tool) => tool.status === statusFilter.value);
  //   }

  //   return result;
  // });

  // 页面加载时获取数据
  onMounted(() => {
    loadToolsData();
  });

  // 加载工具数据
  const loadToolsData = async () => {
    loading.value = true;
    try {
      // 获取MCP服务器列表
      const mcpServers = await getMcpServersApi();

      // 处理服务器数据，转换为工具卡片格式
      tools.value = (mcpServers || []).map((server) => ({
        id: server.id || Math.random().toString(36).substr(2, 9),
        name: server.mcpServerName || server.name || '未命名服务器',
        description: formatConfigDescription(server.connectionConfig),
        connectionType: server.connectionType || 'STUDIO',
        configJson: server.connectionConfig || '',
        type: 'MCP',
        status: 'connected', // 默认状态，可以根据实际情况调整
        // 为了兼容现有的显示逻辑，保留这些字段
      }));
      // filteredItems
      filteredItems.value = cloneDeep(tools.value);
      console.log('MCP服务器数据加载完成:', tools.value);
    } catch (error) {
      console.error('加载MCP服务器数据失败:', error);
      ElMessage.error('加载数据失败: ' + (error.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  };

  // 格式化配置描述
  const formatConfigDescription = (configJson) => {
    try {
      if (!configJson) return '暂无配置';
      const config = JSON.parse(configJson);
      const servers = config.mcpServers || {};
      const serverNames = Object.keys(servers);
      if (serverNames.length > 0) {
        return `包含 ${serverNames.length} 个服务: ${serverNames.join(', ')}`;
      }
      return '配置已设置';
    } catch {
      return '配置格式错误';
    }
  };

  // 刷新数据
  // const refreshData = () => {
  //   loadToolsData();
  // };

  // 搜索处理
  // const handleSearch = () => {
  //   // 搜索逻辑在计算属性中处理
  // };
  const handleSearch = () => {
    // if (!searchKeyword.value.trim()) {
    //   filteredItems.value = [...tools.value];
    //   return;
    // }
    filteredItems.value = [...tools.value];
    const searchTerm = searchKeyword.value.toLowerCase();
    filteredItems.value = tools.value.filter((item) =>
      item.name.toLowerCase().includes(searchTerm),
    );

    if (statusFilter.value) {
      filteredItems.value = filteredItems.value.filter(
        (tool) => tool.status === statusFilter.value,
      );
    }
  };
  // 筛选处理
  const handleFilter = () => {
    // 筛选逻辑在计算属性中处理
  };

  // 显示添加对话框
  const showAddDialog = () => {
    isEdit.value = false;
    dialogVisible.value = true;
    resetForm();
  };

  // const handleEdit = (tool) => {
  //   isEdit.value = true;
  //   dialogVisible.value = true;
  //   Object.assign(formData, {
  //     id: tool.id,
  //     name: tool.name,
  //     connectionType: tool.connectionType || 'STUDIO',
  //     configJson: JSON.stringify(JSON.parse(tool.configJson), null, 2) || '',
  //   });
  // };

  const handleDelete = (id) => {
    console.log(id);

    removeMcpServerApi(id).then(() => {
      loadToolsData();
      ElMessage({
        type: 'success',
        message: '删除成功！',
      });
    });
  };
  // 使用预设示例
  const useExample = (type) => {
    if (type === 'STUDIO') {
      formData.configJson = studioExample.value;
    } else if (type === 'filesystem') {
      formData.configJson = sseExample.value;
    }
  };

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      id: '',
      name: '',
      connectionType: 'STUDIO',
      configJson: '',
    });

    if (formRef.value) {
      formRef.value.clearValidate();
    }
  };

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return;

    try {
      await formRef.value.validate();
      submitting.value = true;

      // 构建提交数据，按照静态页面的格式
      const submitData = {
        mcpServerName: formData.name,
        connectionType: formData.connectionType,
        configJson: formData.configJson,
      };

      if (isEdit.value) {
        // 编辑逻辑 - 先删除再添加（如果API不支持直接更新）
        await removeMcpServerApi(formData.id);
        await addMcpServerApi(submitData);
        ElMessage.success('更新成功');
      } else {
        await addMcpServerApi(submitData);
        ElMessage.success('创建成功');
      }

      dialogVisible.value = false;
      loadToolsData();
    } catch (error) {
      console.error('提交失败:', error);
      ElMessage.error('操作失败: ' + (error.message || '未知错误'));
    } finally {
      submitting.value = false;
    }
  };

  const handleChange = () => {
    formData.configJson = '';
  };
</script>

<style scoped lang="scss">
  .tools-management {
    padding: 20px;
    background: #ffffff;
    min-height: calc(100vh - 64px);
  }
  /* MCP对话框样式 */
  .mcp-dialog-content {
    // max-height: 70vh;
    overflow-y: auto;
    padding: 0;
  }

  .info-section {
    margin-bottom: 20px;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;

    &:last-child {
      margin-bottom: 0;
    }

    .section-header {
      background: #f6f8fa;
      padding: 12px 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #24292e;
      border-bottom: 1px solid #e1e4e8;
      font-size: 14px;
    }

    .el-form {
      padding: 20px;
      margin: 0;
    }
  }

  .form-tip {
    font-size: 12px;
    color: #6a737d;
    margin-top: 6px;
    line-height: 1.4;
  }

  /* 表单项样式优化 */
  :deep(.el-form-item) {
    margin-bottom: 20px;

    .el-form-item__label {
      font-weight: 500;
      color: #24292e;
      line-height: 1.5;
    }

    .el-form-item__content {
      line-height: 1.5;
    }

    .el-form-item__error {
      padding-top: 4px;
    }
  }

  :deep(.el-input__wrapper) {
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      border-color: #c6cbd1;
    }

    &.is-focus {
      border-color: #0366d6;
      box-shadow: 0 0 0 3px rgba(3, 102, 214, 0.1);
    }
  }

  :deep(.el-textarea__inner) {
    border-radius: 6px;
    font-family: 'SFMono-Regular', 'Monaco', 'Inconsolata', 'Liberation Mono',
      'Courier New', monospace;
    font-size: 13px;
    line-height: 1.45;
  }

  .connection-radio-group {
    width: 100%;

    .connection-radio {
      // display: block;
      width: 100%;
      margin-bottom: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      padding: 32px;
      transition: all 0.2s;
      margin-right: 0;

      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }

      &.is-checked {
        border-color: #409eff;
        background: #f0f9ff;
      }

      .radio-content {
        display: flex;
        flex-direction: column;
        margin-left: 8px;

        .radio-title {
          font-weight: 600;
          color: #333;
          font-size: 14px;
          line-height: 1.4;
        }

        .radio-desc {
          font-size: 12px;
          color: #666;
          line-height: 1.4;
          margin-top: 2px;
        }
      }
    }
  }

  .config-section {
    padding: 20px;

    .config-label {
      font-weight: 500;
      color: #24292e;
      margin-bottom: 12px;
      font-size: 14px;
    }

    .config-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'SFMono-Regular', 'Monaco', 'Inconsolata', 'Liberation Mono',
          'Courier New', monospace;
        font-size: 13px;
        line-height: 1.45;
        border-radius: 6px;
        border: 1px solid #d1d5da;

        &:focus {
          border-color: #0366d6;
          box-shadow: 0 0 0 3px rgba(3, 102, 214, 0.1);
        }
      }
    }
  }

  .preset-examples {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .example-item {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      padding: 12px;

      .example-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .example-config {
        background: #f8f9fa;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 8px;
        max-height: 210px;
        overflow-y: auto;

        pre {
          margin: 0;
          font-size: 12px;
          line-height: 1.4;
          color: #333;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }

  .instructions {
    padding: 16px;
    font-size: 14px;
    line-height: 1.6;

    ol {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;

        ul {
          margin-top: 4px;
          padding-left: 20px;

          li {
            margin-bottom: 4px;
          }
        }
      }
    }

    a {
      color: #409eff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    strong {
      font-weight: 600;
      color: #333;
    }
  }
  :deep(.el-radio__label) {
    display: inline-block;
  }

  .card-content {
    overflow-y: auto;
    overflow-x: hidden;
    height: calc(100vh - 180px);
    .el-row {
      margin-top: 8px;
    }
    .card {
      // width: 400px;
      height: 344px;
      background: #f9fafb;
      border-radius: 16px;
      border: 1px solid #e5e7eb;
      padding: 24px;
      box-shadow: var(--shadow-sm);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      cursor: pointer;
      overflow: hidden;
      margin-bottom: 12px;
      .card-top {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        .card-top-name {
          font-weight: 700;
          font-size: 1.25rem;
          color: #111827;
          letter-spacing: -0.025em;
          margin: 0;
        }
        .card-top-id {
          // display: inline-block;
          // padding: 4px 6px;
          border-radius: 6px;
          font-size: 0.75rem;
          color: #6b7280;
          // #D9D9D9
          background: #f3f4f6;
          padding: 4px 8px;
          border-radius: 6px;
          font-weight: 500;
        }
      }
      .card-description {
        //   font-size: 0.875rem;
        color: #4b5563;
        line-height: 1.5;
        margin-bottom: 16px;
        // display: -webkit-box;
        // -webkit-line-clamp: 3;
        // -webkit-box-orient: vertical;
        // overflow: hidden;
        height: 180px;
        overflow: auto;
      }
      .card-tools {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-bottom: 16px;
        .tool-tag {
          background: #e0e7ff;
          color: #409eff;
          padding: 4px 8px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;
        }
      }
      .card-bottom {
        position: absolute;
        bottom: 18px;
        right: 25px;
        width: 100%;

        .card-bottom-actions {
          display: flex;
          gap: 4px;
          justify-content: flex-end;
        }
      }
    }
    .card:hover {
      border-color: #409eff;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
      transform: translateY(-4px);
    }
    .card:hover::before {
      opacity: 1;
    }
    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: #409eff;
      opacity: 0;
      transition: opacity 0.2s ease;
    }
  }

  .agent-set-top {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-bottom: 24px;
    margin-top: 4px;
  }
  .drawer-title {
    font-size: 18px;
  }
</style>
