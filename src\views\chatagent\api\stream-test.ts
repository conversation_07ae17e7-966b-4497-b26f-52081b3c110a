/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 流式响应测试工具
 * 用于测试和调试流式响应的处理逻辑
 */

import { DirectApiService, DirectStreamingRequest, DirectStreamEvent } from './direct-api-service'

export class StreamTestUtils {
  /**
   * 测试流式响应处理
   */
  static async testStreamingResponse(
    planTemplateId: string,
    request: DirectStreamingRequest,
    timeout?: number
  ): Promise<{
    events: DirectStreamEvent[]
    completed: boolean
    error?: Error
    duration: number
  }> {
    const startTime = Date.now()
    const events: DirectStreamEvent[] = []
    let completed = false
    let error: Error | undefined

    return new Promise((resolve) => {
      DirectApiService.sendMessageWithStreaming(
        planTemplateId,
        request,
        (event: DirectStreamEvent) => {
          console.log('📨 测试收到事件:', event)
          events.push(event)
        },
        (err: Error) => {
          console.error('❌ 测试收到错误:', err)
          error = err
          resolve({
            events,
            completed,
            error,
            duration: Date.now() - startTime
          })
        },
        () => {
          console.log('✅ 测试流式响应完成')
          completed = true
          resolve({
            events,
            completed,
            error,
            duration: Date.now() - startTime
          })
        },
        timeout
      )
    })
  }

  /**
   * 模拟服务端响应测试
   */
  static createMockSSEResponse(events: any[], includeEndSignal: boolean = true): Response {
    const encoder = new TextEncoder()
    let eventIndex = 0

    const stream = new ReadableStream({
      start(controller) {
        const sendNextEvent = () => {
          if (eventIndex < events.length) {
            const event = events[eventIndex]
            const sseData = `data: ${JSON.stringify(event)}\n\n`
            controller.enqueue(encoder.encode(sseData))
            eventIndex++
            
            // 模拟延迟
            setTimeout(sendNextEvent, 100)
          } else {
            // 发送结束信号
            if (includeEndSignal) {
              controller.enqueue(encoder.encode('data: [DONE]\n\n'))
            }
            controller.close()
          }
        }

        sendNextEvent()
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache'
      }
    })
  }

  /**
   * 检查流式响应是否正常结束
   */
  static validateStreamCompletion(
    events: DirectStreamEvent[],
    completed: boolean,
    error?: Error
  ): {
    isValid: boolean
    issues: string[]
  } {
    const issues: string[] = []

    // 检查是否正常完成
    if (!completed && !error) {
      issues.push('流式响应未完成且无错误信息')
    }

    // 检查是否有事件
    if (events.length === 0) {
      issues.push('未收到任何事件')
    }

    // 检查错误类型
    if (error) {
      if (error.message.includes('超时')) {
        issues.push('流式响应超时')
      } else if (error.message.includes('无响应')) {
        issues.push('流式连接无响应')
      } else {
        issues.push(`未知错误: ${error.message}`)
      }
    }

    return {
      isValid: issues.length === 0,
      issues
    }
  }

  /**
   * 生成测试报告
   */
  static generateTestReport(
    testName: string,
    result: {
      events: DirectStreamEvent[]
      completed: boolean
      error?: Error
      duration: number
    }
  ): string {
    const validation = this.validateStreamCompletion(
      result.events,
      result.completed,
      result.error
    )

    return `
=== 流式响应测试报告 ===
测试名称: ${testName}
持续时间: ${result.duration}ms
事件数量: ${result.events.length}
完成状态: ${result.completed ? '✅ 已完成' : '❌ 未完成'}
错误信息: ${result.error ? `❌ ${result.error.message}` : '✅ 无错误'}

验证结果: ${validation.isValid ? '✅ 通过' : '❌ 失败'}
${validation.issues.length > 0 ? `问题列表:\n${validation.issues.map(issue => `- ${issue}`).join('\n')}` : ''}

事件详情:
${result.events.map((event, index) => `${index + 1}. ${event.type}: ${JSON.stringify(event.payload)}`).join('\n')}
========================
    `.trim()
  }
}
