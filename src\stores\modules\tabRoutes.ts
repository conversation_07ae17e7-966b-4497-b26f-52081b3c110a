import { defineStore } from 'pinia';
import { store } from '/@/stores';
import { Menu } from '/@/stores/types';
import { PageEnum } from '/@/enums/pageEnum';
import { useUserStore } from '/@/stores/modules/user';
import { getTabPages, setTabPages } from '/@/utils/storage/tabPages';
// import { addKpAliveList } from '/@/hooks/web/useKeepAlive';
// import { setKpAliveList } from '/@/hooks/web/useKeepAlive';

interface TabRoutesState {
  // Page loading status
  tabs: Menu[];
}

export const useTabRoutesStore = defineStore({
  id: 'tabRoutes',
  state: (): TabRoutesState => ({
    tabs: [],
  }),
  getters: {
    getTabs(): Menu[] {
      if (getTabPages() && getTabPages().length > 0) {
        this.tabs = getTabPages();
      }
      return this.tabs;
    },
  },
  actions: {
    setTabs(menus: Menu[]) {
      this.tabs = menus;
      this.setTabsStorage(this.tabs);
    },
    addTabs(menu: Menu, realParams?: any) {
      if (menu) {
        const params = realParams?.params || {};
        const query = realParams?.query || {};
        const menuIndex = this.getTabs.findIndex((item) => item.path === menu.path);
        if (menuIndex < 0) {
          menu.params = params;
          menu.query = query;
          this.tabs = [...this.tabs, menu];
          this.setTabsStorage(this.tabs);
        } else {
          if (realParams) {
            this.getTabs[menuIndex].params = params;
            this.getTabs[menuIndex].query = query;
            this.setTabsStorage(this.getTabs);
          }
        }
        // addKpAliveList(menu);
      }
    },
    // 缓存 this.tabs
    setTabsStorage(menu: Menu[]) {
      setTabPages(menu);
    },
    // 关闭当前
    delTabs(menu: Menu) {
      this.tabs = this.getTabs.filter((item) => item.path !== menu.path);
      // setKpAliveList(menu);
      this.setTabsStorage(this.tabs);
    },
    // 删除左侧Tab
    delLeftTabs(menu: Menu) {
      const userStore = useUserStore();
      const menuIndex = this.getTabs.findIndex((item) => item.path === menu.path);
      this.tabs = this.getTabs.filter((item, index) => {
        if (index < menuIndex) {
          // setKpAliveList(item);
        }
        if (
          index >= menuIndex ||
          [PageEnum.BASE_HOME, userStore.userInfo?.homePath].includes(
            item.path as PageEnum,
          )
        ) {
          return item;
        }
      });
      this.setTabsStorage(this.tabs);
    },
    // 删除右侧Tab
    delRightTabs(menu: Menu) {
      const userStore = useUserStore();
      const menuIndex = this.getTabs.findIndex((item) => item.path === menu.path);
      this.tabs = this.getTabs.filter((item, index) => {
        if (index > menuIndex) {
          // setKpAliveList(item);
        }
        if (
          index <= menuIndex ||
          [PageEnum.BASE_HOME, userStore.userInfo?.homePath].includes(
            item.path as PageEnum,
          )
        ) {
          return item;
        }
      });
      this.setTabsStorage(this.tabs);
    },
    // 删除所有Tab
    delAllTabs() {
      const userStore = useUserStore();
      this.tabs = this.getTabs.filter((item) => {
        // setKpAliveList(item);
        if (
          [PageEnum.BASE_HOME, userStore.userInfo?.homePath].includes(
            item.path as PageEnum,
          )
        ) {
          return item;
        }
      });
      this.setTabsStorage(this.tabs);
    },
  },
});

export function useTabRoutesStoreWithOut() {
  return useTabRoutesStore(store);
}
