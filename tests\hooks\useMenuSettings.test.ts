import { describe, test, beforeEach, expect } from 'vitest';
import { useMenuSettings } from '/@/hooks/settings/useMenuSettings';
import { setActivePinia, createPinia } from 'pinia';

describe('Menu菜单配置的Hooks工具类', () => {
  beforeEach(() => {
    setActivePinia(createPinia()); // 激活pinia
  });

  test('设置菜单不折叠', () => {
    const menuSettings = useMenuSettings();
    // 打开 菜单折叠
    menuSettings.toggleOpen();
    expect(menuSettings.getMenuCollapsed.value).toEqual(false);
  });
});
