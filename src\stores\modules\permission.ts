/*
 * @Author: wangneng <EMAIL>
 * @Date: 2022-08-01 14:30:52
 * @LastEditors: wuxiaofan
 * @LastEditTime: 2022-09-16 10:19:16
 * @FilePath: \cpvf-pools\src\stores\modules\permission.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia';
import { store } from '/@/stores';
import { Menu, PagesRoutes } from '/@/stores/types';
import router, { addedRoute, allRoutes, BUS_WHITE_LIST } from '/@/router';
import {
  backMenuAddProp,
  backMenuPath,
  fiterRoutesWithMenuPath,
  frontRouteToMenu,
  getShowMenu,
  // getShowMenu,
  // uniqeMicroMenu,
} from '/@/router/tools/menuTools';
import { cloneDeep } from 'lodash-es';
import { getMenus } from '/@/api/sys';
import {
  getMenuStorage,
  setMenuStorage,
  setMicroData,
  setAllMenuStorage,
} from '/@/router/tools/menuStorage';
import { useUserStore } from '/@/stores/modules/user';
// import { menuParams } from '/@/api/model/User';
import { useTabRoutesStore } from '/@/stores/modules/tabRoutes';
import { useTenantStore } from '/@/stores/modules/tenant';
import { getHeaderInfo } from '/@/utils/storage/tenant';
import { PageEnum } from '/@/enums/pageEnum';
import { unref } from 'vue';
import { RouteLocationNormalized } from 'vue-router';
// import { useAppStoreWithOut } from '/@/stores/modules/app';

interface PermissionState {
  // 权限列表
  permCodeList: string[] | number[];
  // 后端菜单
  menuList: Menu[];
  // 后端摊平菜单
  flatMenuList: any;
  // 业务白名单
  busMenuList: Menu[];
  // 当前菜单
  currentMenu: Menu;
  // 是否已经加载路由
  dynamicAddedRoute: boolean;
  // 是否已经加载白名单路由
  dynamicAddedBusMenu: boolean;
}

export const usePermissionStore = defineStore({
  id: 'app-permission',
  state: (): PermissionState => ({
    permCodeList: [],
    menuList: [],
    flatMenuList: {},
    busMenuList: [],
    dynamicAddedRoute: false,
    dynamicAddedBusMenu: false,
    currentMenu: { id: '', name: '', path: '' },
  }),
  getters: {
    getPermCodeList(): string[] | number[] {
      const userStore = useUserStore();
      let permissionList;
      const id = useTenantStore().getCurTenant.id;
      const tenantId = getHeaderInfo()?.tenantId;
      const projectId = getHeaderInfo()?.projectId;
      const systemList = userStore.getUserInfo.permissionMap?.system;
      const tenantList = userStore.getUserInfo.permissionMap?.[tenantId || id];
      const projectList = userStore.getUserInfo.permissionMap?.[projectId || id];
      if (systemList?.length && tenantList?.length) {
        permissionList = systemList.concat(tenantList);
      } else if (systemList?.length) {
        permissionList = systemList;
      } else if (tenantList?.length) {
        permissionList = tenantList;
      } else {
        permissionList = [];
      }
      //处理项目账号没有租户菜单场景
      if (projectList?.length) {
        permissionList = permissionList.concat(projectList);
      }
      // eslint-disable-next-line prefer-const
      return this.permCodeList && this.permCodeList.length > 0
        ? this.permCodeList
        : permissionList
        ? permissionList
        : [];
    },
    getMenuList(): Menu[] {
      return this.menuList && this.menuList.length > 0
        ? this.menuList
        : getMenuStorage();
    },
    getBusMenuList(): Menu[] {
      return this.busMenuList;
    },
    getDynamicAddedRoute(): boolean {
      return this.dynamicAddedRoute;
    },
    getDynamicAddedBusMenu(): boolean {
      return this.dynamicAddedBusMenu;
    },
    getFlatMenuList(): any {
      return this.flatMenuList;
    },
  },
  actions: {
    /**
     * 根据 path 获取 当前 Menu
     * @param path
     */
    getCurrentMenu(path: string): Menu {
      return this.getFlatMenuData(path);
    },
    getFlatMenuData(path: string): Menu {
      return this.flatMenuList[path];
    },
    setCurrentMenu(menu: RouteLocationNormalized) {
      const menuValue = unref(menu);
      const tabRoutesStore = useTabRoutesStore();
      const realParams: any = {};
      realParams.params = menuValue?.params || {};
      realParams.query = menuValue?.query || {};
      tabRoutesStore.addTabs(this.getFlatMenuData(menuValue.path), realParams);
    },
    setPermCodeList(codeList: string[] | number[]) {
      this.permCodeList = codeList;
    },
    setFlatMenuList(flatMenus: Menu[]) {
      // 将菜单摊平后，方便获取路由信息
      this.flatMenuList = flatMenus;
    },
    setMenuList(list: Menu[]) {
      this.menuList = list;
      setMenuStorage(list);
    },
    setDynamicAddedRoute(added: boolean) {
      this.dynamicAddedRoute = added;
    },
    setDynamicAddedBusMenu(added: boolean) {
      this.dynamicAddedBusMenu = added;
    },
    setBusWhiteMenus(list: Menu[]) {
      this.busMenuList = list;
    },
    async genFrontWhiteMenuList() {
      // 获取纯白名单的菜单
      // 根据前端路由生成菜单 routes的第一层是layout，要根据children提取menus
      const cloneRoutePagesList = cloneDeep(allRoutes);
      const menuRouteList = cloneRoutePagesList.filter(
        (item) => BUS_WHITE_LIST.indexOf('/' + item.path.split('/')[1]) !== -1,
      );
      // const menu = await frontRouteToMenu(menuRouteList as PagesRoutes[]);
      const menu = await frontRouteToMenu(menuRouteList as PagesRoutes[]);
      this.setBusWhiteMenus(menu);
      // return menu;
      this.setDynamicAddedBusMenu(true);
    },
    async genFrontMenuList(whiteMenuList: string[]) {
      // 纯前端路由 将所有生成的routes添加到 菜单中
      allRoutes.forEach((route) => {
        if (!whiteMenuList.includes(route.path)) {
          const removeRoute = router.addRoute(route);
          addedRoute.push(removeRoute);
        }
      });
      // 根据前端路由生成菜单 routes的第一层是layout，要根据children提取menus
      const cloneRoutePagesList = cloneDeep(allRoutes);
      const menuRouteList = cloneRoutePagesList.filter(
        (item) => whiteMenuList.indexOf(item.path) === -1,
      );
      // const menu = await frontRouteToMenu(menuRouteList as PagesRoutes[]);
      const menu = await frontRouteToMenu(menuRouteList as PagesRoutes[]);
      this.setMenuList(menu);
      // return menu;
      this.setDynamicAddedRoute(true);
    },
    async genBackMenuList() {
      const userStore = useUserStore();
      // 后端路由，保留route中在后端路由中的菜单
      let baseHomeMenu: Nullable<Menu> = null;
      const backMenus = (await getMenus()) as any;
      // console.log(backMenus);

      // const backMenus = [
      //   {
      //     id: '1565627326099849218',
      //     parentId: '0',
      //     children: [],
      //     label: '工作台',
      //     status: null,
      //     name: '工作台',
      //     path: '/dashboard',
      //     component: null,
      //     type: '0',
      //     sort: 0,
      //     meta: { icon: '', title: '工作台' },
      //   },

      //   {
      //     id: '1565627326099849218',
      //     parentId: '0',
      //     children: [],
      //     label: 'setting',
      //     status: null,
      //     name: 'setting',
      //     path: '/agent/setting',
      //     component: null,
      //     type: '0',
      //     sort: 0,
      //     meta: { icon: '', title: 'setting' },
      //   },
      //   {
      //     id: '1565627326099849218',
      //     parentId: '0',
      //     children: [],
      //     label: 'arrange',
      //     status: null,
      //     name: 'arrange',
      //     path: '/agent/arrange',
      //     component: null,
      //     type: '0',
      //     sort: 0,
      //     meta: { icon: '', title: 'arrange' },
      //   },
      //   {
      //     id: '3',
      //     parentId: '0',
      //     children: [
      //       {
      //         id: '5',
      //         parentId: '3',
      //         children: [],
      //         label: '用户管理',
      //         status: null,
      //         name: '用户管理',
      //         path: '/system/user',
      //         component: null,
      //         type: '0',
      //         sort: 2,
      //         meta: { icon: '', title: '用户管理' },
      //       },

      //       {
      //         id: '6',
      //         parentId: '3',
      //         children: [],
      //         label: '角色管理',
      //         status: null,
      //         name: '角色管理',
      //         path: '/system/role',
      //         component: null,
      //         type: '0',
      //         sort: 3,
      //         meta: { icon: '', title: '角色管理' },
      //       },
      //     ],
      //     label: '系统管理',
      //     status: null,
      //     name: '系统管理',
      //     path: '/system',
      //     component: null,
      //     type: '0',
      //     sort: 3,
      //     meta: { icon: '', title: '系统管理' },
      //   },

      //           {
      //     id: '1565627326099849218',
      //     parentId: '0',
      //     children: [],
      //     label: '工作台',
      //     status: null,
      //     name: '工作台',
      //     path: '/dashboard',
      //     component: null,
      //     type: '0',
      //     sort: 0,
      //     meta: { icon: '', title: '工作台' },
      //   },
      // ];
      const menusData: Menu[] = backMenus;
      // 提取所有后端的menu path，同时在backMenuPath中判断当前是否子应用
      const menuPath: string[] = [];
      const microList: Menu[] = [];
      const flatMenu: Menu[] = [];
      backMenuPath(menusData, menuPath, microList, flatMenu);
      // 设置后端摊平菜单，方便查询
      this.setFlatMenuList(flatMenu);
      menuPath.push('/micro/vite/:id');
      // 保存微前端菜单数据
      setMicroData(microList);
      // 筛选出 被包含在菜单中的路由
      const realRoutes = fiterRoutesWithMenuPath(allRoutes, menuPath);
      // 将前端路由中的属性 添加到后端菜单上  realRoutes menusData
      backMenuAddProp(menusData, realRoutes);
      realRoutes.forEach((route) => {
        const removeRoute = router.addRoute(route);
        addedRoute.push(removeRoute);
      });
      // microList.forEach((item) => {addedRoute.push(item)});
      // console.log('realRoutes', realRoutes);
      // 将微应用的菜单添加上数字用以区分唯一菜单 /microapp/vite/1
      // uniqeMicroMenu(menusData);
      // 将菜单中隐藏的菜单进行删除
      setAllMenuStorage(menusData);
      // 从菜单配置的metaconfig中获取开启的页面缓存
      // const appStore = useAppStoreWithOut();
      // appStore.setKeepAliveListMenu();
      getShowMenu(menusData);
      this.setMenuList(menusData);
      this.setDynamicAddedRoute(true);
      // 将首页添加到tabPages中
      const useTabRoutes = useTabRoutesStore();
      const tabRoutes = useTabRoutes.getTabs;
      const baseHomeIndex = tabRoutes.findIndex((item: Menu) =>
        [PageEnum.BASE_HOME, userStore.userInfo?.homePath].includes(item.path),
      );
      if (baseHomeIndex === -1) {
        menusData.forEach((item: Menu) => {
          if ([PageEnum.BASE_HOME, userStore.userInfo?.homePath].includes(item.path)) {
            baseHomeMenu = item;
            baseHomeMenu.params = {};
            baseHomeMenu.query = {};
            useTabRoutes.addTabs(baseHomeMenu);
          }
        });
      }
      // 请求后端菜单后，需要查看tabRoutes 是否包含home页面
      // return menusData;
    },
  },
});

export function usePermissionStoreWithOut() {
  return usePermissionStore(store);
}
