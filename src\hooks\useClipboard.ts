import { Ref, ref } from 'vue';

export type useClipboardResult = {
  copyText: (text: string) => void;
  text: Ref<string>;
};

const createElement = () => {
  const el = document.createElement('div');
  el.style.whiteSpace = 'pre';
  return el;
};

const useClipboard = (): useClipboardResult => {
  const el = createElement();
  const text = ref('');
  const copyText = (copy?: string) => {
    if (!copy) return;
    const selection = window.getSelection();
    if (!selection) return;
    const range = document.createRange();
    el.textContent = copy;
    text.value = copy;
    document.body.appendChild(el);
    range.selectNode(el);
    selection.removeAllRanges();
    selection.addRange(range);
    try {
      document.execCommand('Copy');
    } catch (error) {
      console.error('copy failed!');
    }
    selection.removeAllRanges();
    el.textContent = '';
    document.body.removeChild(el);
  };
  return { copyText, text };
};

export default useClipboard;
