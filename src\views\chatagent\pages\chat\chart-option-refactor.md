# 图表配置重构 - baseOption 合并复用

## 重构背景
在代码中发现了两个函数都定义了相似的 `baseOption` 配置：
1. `generateSummaryChartOption` - 用于summary区域的图表
2. `generateChartOption` - 用于工具详情面板的图表

这两个函数中有大量重复的图表配置代码，需要提取公共部分进行复用。

## 重构前的问题

### 代码重复
两个函数都包含相似的配置：
```javascript
// generateSummaryChartOption 中的 baseOption
const baseOption = {
  title: { text: title || '图表', left: 'center' },
  grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  tooltip: { trigger: 'axis' },
  xAxis: { type: 'category', data: xData, ... },
  yAxis: { type: 'value' },
  series: [{ name: yFields || 'Value', type: chartType, ... }]
}

// generateChartOption 中的 baseOption  
const baseOption = {
  title: { text: selectedTool.value?.parameters?.chartTitle || '', left: 'center' },
  grid: { left: '3%', right: '3%', bottom: '3%', top: '10%', containLabel: true }
}
```

### 维护困难
- 修改图表样式需要在两个地方同时修改
- 容易出现不一致的配置
- 代码冗余，增加维护成本

## 重构方案

### 1. 提取公共基础配置函数
```javascript
// 生成基础图表配置（公共部分）
const createBaseChartOption = (title: string, customGrid?: any) => {
  return {
    title: {
      text: title || '图表',
      left: 'center'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
      ...customGrid  // 允许自定义grid配置
    },
    tooltip: {
      trigger: 'axis'
    }
  }
}
```

### 2. 提取轴图表配置函数
```javascript
// 生成折线图/柱状图的轴配置
const createAxisChartConfig = (baseOption: any, xData: any[], yData: any[], chartType: string, seriesName: string) => {
  return {
    ...baseOption,
    xAxis: {
      type: 'category',
      data: xData,
      axisLabel: {
        rotate: xData.some(label => String(label).length > 6) ? 45 : 0
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: seriesName || 'Value',
      type: chartType === 'line' ? 'line' : 'bar',
      data: yData,
      smooth: chartType === 'line',
      itemStyle: {
        color: '#3b82f6',
        borderRadius: chartType === 'bar' ? [4, 4, 0, 0] : 0
      }
    }]
  }
}
```

## 重构后的实现

### generateSummaryChartOption 重构
```javascript
// 折线图和柱状图配置 - 使用公共函数
const xData = data.map(item => item[xFields] || '')
const yData = data.map(item => {
  const value = item[yFields]
  return typeof value === 'string' ? parseFloat(value) || 0 : value || 0
})

const baseOption = createBaseChartOption(title)
return createAxisChartConfig(baseOption, xData, yData, chartType, yFields || 'Value')
```

### generateChartOption 重构
```javascript
// 折线图和柱状图配置 - 使用公共函数
if (chartType === 'line' || chartType === 'bar') {
  const xData = chartData.map(item => item[result.XFields]);
  const yData = chartData.map(item => item[result.YFields]);

  const customGrid = {
    top: '10%'  // 工具详情面板需要更多顶部空间
  }
  const baseOption = createBaseChartOption(title, customGrid)
  
  // 为工具详情面板定制tooltip
  const actualChartType = chartType === 'auto' ? 'line' : chartType
  const customOption = createAxisChartConfig(baseOption, xData, yData, actualChartType, result.YFields || 'Value')
  customOption.tooltip = {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  }
  
  return customOption;
}
```

## 重构优势

### 1. 代码复用
- 提取了公共的图表配置逻辑
- 减少了重复代码
- 统一了图表样式

### 2. 灵活性
- `createBaseChartOption` 支持自定义grid配置
- `createAxisChartConfig` 支持不同的数据和样式
- 保持了原有功能的完整性

### 3. 维护性
- 修改基础样式只需要在一个地方修改
- 新增图表类型更容易
- 代码结构更清晰

### 4. 一致性
- 确保两个场景下的图表样式保持一致
- 统一的配置标准
- 减少了配置错误的可能性

## 支持的功能

### 饼图配置
- 两个函数都保留了独立的饼图配置
- 饼图配置相对独特，不适合过度抽象

### 自定义配置
- summary区域：标准配置
- 工具详情面板：增加了 `top: '10%'` 和自定义tooltip

### 数据处理
- 保持了原有的数据转换逻辑
- 支持字符串数字的自动转换
- 处理了空值和异常情况

## 测试建议

1. 测试summary区域的图表显示是否正常
2. 测试工具详情面板的图表显示是否正常
3. 验证两个区域的图表样式是否一致
4. 测试不同图表类型（line、bar、pie）的显示效果
5. 确认自定义配置（如grid.top）是否生效

通过这次重构，代码变得更加简洁、可维护，同时保持了原有的所有功能。
