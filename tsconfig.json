{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "sourceMap": true,
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "importHelpers": true,
    "experimentalDecorators": true,
    "noLib": false,
    "forceConsistentCasingInFileNames": true,
    "strictFunctionTypes": false,
    "allowJs": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitAny": false,
    "removeComments": true,
    "lib": ["esnext", "dom"],
    "types": [
      "vite/client",
      "vitest/globals",
      "vite-plugin-pages/client",
      "vite-plugin-vue-layouts/client",
    ],
    "typeRoots": ["./node_modules/@types", "./types/"],
    "baseUrl": ".",
    "paths": {
      "@build/*": ["build/*"],
      "/@/*": ["src/*"],
      "/#/*": ["types/*"],
    }
  },
  "include": [
    "types/**/*.d.ts",
    "types/**/*.ts",
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "build/**/*.ts",
    "vite.config.ts"
  ],
  "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js"]
}
