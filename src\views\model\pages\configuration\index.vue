<!-- eslint-disable prettier/prettier -->
<template>
  <div class="agent-container">
    <!-- <div class="agent-header"> 模型配置 </div> -->
    <div class="main">
      <div class="sider">
        <div class="sidebar-title"> AI来源 </div>
        <div class="divider"></div>
        <div class="menu-list">
          <p
            v-for="(item, index) in modalList"
            :key="item.id"
            @click="selectItem(index)"
            :class="['menu-item', { active: selectedIndex === index }]"
            ><span class="menu-label">{{ item.name }}</span>

            <el-popconfirm
              :icon="InfoFilled"
              title="确定要删除吗?"
              @confirm="handleDelete(item.id)"
              @cancel="cancelEvent"
            >
              <template #reference>
                <el-icon v-if="selectedIndex === index"><Close /></el-icon>
                <span v-else style="display: none"></span>
              </template>
            </el-popconfirm>
          </p>
        </div>
        <div class="sider-bottom">
          <el-button :icon="Plus" @click="handleOpenNameModal">添加</el-button>
        </div>
      </div>
      <div class="content">
        <!-- {{ modelForm }} -->
        <el-form label-position="top" label-width="auto">
          <el-form-item label="API地址">
            <el-input
              v-model="modelForm.apiHost"
              @blur="(e) => handleFieldBlur(e, 'apiHost')"
              @focus="handleFocus"
              autocomplete="off"
              name="no-autofill-username"
            />
          </el-form-item>
          <el-form-item label="密钥">
            <el-input
              v-model="modelForm.apiKey"
              type="password"
              :show-password="true"
              @focus="handleFocus"
              autocomplete="new-password"
              name="no-autofill-password"
              @blur="(e) => handleFieldBlur(e, 'apiKey')"
            />
          </el-form-item>
          <el-form-item label="类型">
            <!-- <el-input v-model="modelForm.type" /> -->
            <el-select v-model="modelForm.type" disabled>
              <el-option
                v-for="item in modelTypeOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
              <!-- <el-option label="Zone two" value="beijing" /> -->
            </el-select>
          </el-form-item>
        </el-form>
        <p class="model-p">
          <span style="margin-right: 10px">模型</span>
          <el-Button icon="Plus" @click="handleAddModel">添加</el-Button>
        </p>
        <el-collapse expand-icon-position="left" v-if="modelSetList.length > 0">
          <el-collapse-item v-for="item in modelSetList" :name="item.id" :key="item.id">
            <template #title>
              <div class="collapse-header">
                <div class="collapse-header-left">
                  <span>{{ item.model }}</span>

                  <span
                    v-if="item.modelTypes && item.modelTypes.includes('VISUAL')"
                    class="spaceCollapseSpan visual-bj visual-color"
                  >
                    视觉
                  </span>
                  <span
                    v-if="item.modelTypes && item.modelTypes.includes('NETWORK')"
                    class="spaceCollapseSpan net-bj net-color"
                  >
                    联网
                  </span>
                  <span
                    v-if="item.modelTypes && item.modelTypes.includes('EMBED')"
                    class="spaceCollapseSpan enb-bj enb-color"
                  >
                    嵌入
                  </span>
                  <span
                    v-if="item.modelTypes && item.modelTypes.includes('REASONING')"
                    class="spaceCollapseSpan era-bj era-color"
                  >
                    推理
                  </span>
                  <span
                    v-if="item.modelTypes && item.modelTypes.includes('TOOL')"
                    class="spaceCollapseSpan tool-bj tool-color"
                  >
                    工具
                  </span>
                </div>
                <div class="collapse-header-right" style="margin-right: 12px">
                  <el-switch
                    v-model="item.modelEnable"
                    @click.stop
                    @change="(val) => handleSwitchChange(val, item)"
                    inline-prompt
                    active-text="启用"
                    inactive-text="禁用"
                    style="
                      --el-switch-on-color: #13ce66;
                      --el-switch-off-color: #dcdfe6;
                    "
                  />
                  <el-icon @click.stop="handlEditModelSet(item)"><EditPen /></el-icon>
                  <el-popconfirm
                    :icon="InfoFilled"
                    title="确定要删除吗?"
                    @confirm="handleModelSetDelete(item)"
                    @cancel="cancelEvent"
                  >
                    <template #reference>
                      <el-icon @click.stop><Close /></el-icon>
                    </template>
                  </el-popconfirm>
                </div>
              </div>
            </template>
            <div style="padding: 12px">
              <!-- {{ item }} -->
              <el-descriptions :column="1">
                <el-descriptions-item label="最大token数：">{{
                  item.maxTokens
                }}</el-descriptions-item>
                <el-descriptions-item label="温度：">{{
                  item.temperature
                }}</el-descriptions-item>
                <!-- <el-descriptions-item label="是否思考："
                  ><el-tag size="small">{{
                    item.enableThinking ? '是' : '否'
                  }}</el-tag></el-descriptions-item
                > -->
                <el-descriptions-item label="更多配置：">
                  <JsonEditorVue
                    class="editor"
                    v-model="item.moreConfig"
                    mode="text"
                    :readOnly="true"
                    :mainMenuBar="false"
                    :expanded-on-start="true"
                  />
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-empty v-else description="暂无数据！" />
      </div>
      <!-- <div class="menusTitle">AI来源</div>  modelTypeOptions
      <div v-for="item in modalList" class="menuItem" :key="item.id">
        <span>{{ item.name }}</span>
      </div> -->
    </div>

    <el-dialog
      v-model="isModalNameVisible"
      title="添加AI来源"
      width="500"
      :destroy-on-close="true"
    >
      <basic-form
        :formList="modalNameFormSchema"
        :isCreate="false"
        :formData="sourceFormData"
        :showSubmit="false"
        :check-strictly="true"
        label-position="top"
        ref="sourceFormDataRef"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="isModalNameVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="modalNameloading"
            @click="handleAddModelName"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="isModalVisible"
      :title="modelOperationalState === 'add' ? '添加' : '编辑'"
      width="500"
      :destroy-on-close="true"
    >
      <basic-form
        :formList="modalFormSchema"
        :isCreate="false"
        :formData="modalFormData"
        :showSubmit="false"
        :check-strictly="true"
        label-position="top"
        ref="modalFormDataRef"
      >
        <template #moreConfig>
          <JsonEditorVue
            class="editor"
            style="width: 100%"
            v-model="modalFormData.moreConfig"
            mode="text"
            :readOnly="false"
            :mainMenuBar="false"
            :expanded-on-start="true"
          />
        </template>
      </basic-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="isModalVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="modalLoading"
            @click="handleAddOrEditModel"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { InfoFilled, Plus } from '@element-plus/icons-vue';
  import JsonEditorVue from 'json-editor-vue';
  import {
    getConfigListApi,
    deleteAiSourceApi,
    addAiSourceApi,
    getByIdAiSourceApi,
    putAiSourceApi,
    getModelTypeApi,
    addModelSetApi,
    putModelSetApi,
    deleteModelSetApi,
  } from '../../api/model';
  import { modalNameFormSchema, modelTypeOptions, modalFormSchema } from './data';
  import { cloneDeep } from 'lodash-es';

  const modalList = ref<any[]>([]);
  const selectedIndex = ref(0);
  const isModalNameVisible = ref(false);
  const sourceFormData = ref({
    name: '',
    type: '',
  });
  const sourceFormDataRef = ref();
  const modalNameloading = ref(false);

  const modelForm = ref({
    apiHost: '',
    apiKey: '',
    type: '',
    id: '',
  });

  const isModalVisible = ref(false);

  const modalFormDataRef = ref();

  const modalFormData = ref({
    aiConfigId: '',
    maxTokens: 1,
    model: '',
    modelTypes: [],
    moreConfig: { options: {} },
    temperature: 0.7,
    // enableThinking: true,
  });
  const modelOperationalState = ref('add');
  const modalLoading = ref(false);

  const modelSetList = ref<any[]>([]);

  const hasReceivedFocus = ref(false);

  const getList = async () => {
    const res = await getConfigListApi();
    modalList.value = res;

    // if (res[selectedIndex.value].apiHost===null)
    res[selectedIndex.value].apiHost = res[selectedIndex.value].apiHost
      ? res[selectedIndex.value].apiHost
      : '';
    res[selectedIndex.value].apiKey = res[selectedIndex.value].apiKey
      ? res[selectedIndex.value].apiKey
      : '';
    modelForm.value = res[selectedIndex.value];
    console.log('modelForm');
    console.log(modelForm.value);
    getAiModal(res[selectedIndex.value].id);
  };
  // getByIdAiSourceApi
  const getAiModal = async (id) => {
    const res = await getByIdAiSourceApi(id);
    console.log(res);
    modelSetList.value = res;
  };

  getList();
  const selectItem = (index) => {
    selectedIndex.value = index; // 更新选中的索引
    modelForm.value = modalList.value[selectedIndex.value];
    getAiModal(modalList.value[selectedIndex.value].id);
  };
  const handleDelete = (id) => {
    console.log(id);

    deleteAiSourceApi(id).then(() => {
      selectedIndex.value = 0;
      getList();
      ElMessage({
        type: 'success',
        showClose: true,
        message: '删除成功！',
      });
    });
  };
  const cancelEvent = () => {
    console.log('cancel!');
  };
  const handleOpenNameModal = () => {
    isModalNameVisible.value = true;
    sourceFormData.value = {
      name: '',
      type: '',
    };
    // sourceForm.resetFields()
  };
  const handleAddModelName = () => {
    const getData = sourceFormDataRef.value.submitForm;
    const ruleFormRef = sourceFormDataRef.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        modalNameloading.value = true;
        addAiSourceApi(data)
          .then(() => {
            ElMessage({
              type: 'success',
              message: `添加成功`,
            });
            modalNameloading.value = false;
            selectedIndex.value = 0;
            getList();
            isModalNameVisible.value = false;
          })
          .catch(() => {
            modalNameloading.value = false;
          });
      }
    });
  };

  const handleFieldBlur = (e, fieldName) => {
    console.log('handleFieldBlur');
    console.log(hasReceivedFocus.value);
    // console.log(modalList.value[selectedIndex.value]);
    if (!!hasReceivedFocus.value) {
      if (
        modalList.value[selectedIndex.value] &&
        modalList.value[selectedIndex.value].id
      ) {
        const params = {
          id: modalList.value[selectedIndex.value].id,
          version: modalList.value[selectedIndex.value].version,
        };
        params[fieldName] = e.target.value;
        hasReceivedFocus.value = false;
        putAiSourceApi(params).then(() => {
          getList();
        });
      }
    }

    // console.log(res);
    // getAiList();
    // setMenusList(res);
    // 在这里处理特定字段的失焦逻辑
  };
  const handleFocus = () => {
    hasReceivedFocus.value = true;
  };
  const getModelType = async () => {
    console.log('getModelType11');
    const res = await getModelTypeApi();
    const data = res.map((item) => {
      return { label: item.label, value: item.name };
    });
    console.log('getModelType');

    if (!!modalFormSchema && modalFormSchema[3]) {
      modalFormSchema[3].componentProps.options = data;
    }
    console.log(modalFormSchema);
  };
  getModelType();

  const handleAddModel = () => {
    isModalVisible.value = true;
    modelOperationalState.value = 'add';
    modalFormData.value = {
      aiConfigId: '',
      maxTokens: 1,
      model: '',
      modelTypes: [],
      moreConfig: { options: {} },
      temperature: 0.7,
      // enableThinking: true,
    };
  };

  const handlEditModelSet = (item) => {
    isModalVisible.value = true;
    modelOperationalState.value = 'edit';
    const deepItem = cloneDeep(item);
    console.log(deepItem);
    if (deepItem.modelTypes === '') {
      deepItem.modelTypes = [];
    } else {
      deepItem.modelTypes = deepItem.modelTypes && deepItem.modelTypes.split(',');
    }

    modalFormData.value = {
      ...deepItem,
    };
  };

  const handleAddOrEditModel = () => {
    const getData = modalFormDataRef.value.submitForm;
    const ruleFormRef = modalFormDataRef.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        modalLoading.value = true;
        console.log(data);
        console.log(modalList.value);
        console.log(modalList.value[selectedIndex.value].id);
        const deepData = cloneDeep(data);
        // deepData.modelTypes = deepData.modelTypes && deepData.modelTypes.join(',')===''?undefined:deepData.modelTypes && deepData.modelTypes.join(',');
        deepData.modelTypes = deepData.modelTypes && deepData.modelTypes.join(',');

        if (modelOperationalState.value === 'add') {
          const params = {
            ...deepData,
          };
          params.aiConfigId = modalList.value[selectedIndex.value].id;
          console.log(params);
          params.moreConfig =
            typeof params.moreConfig === 'string'
              ? JSON.parse(params.moreConfig)
              : params.moreConfig;
          console.log('add');
          console.log(params);
          addModelSetApi(params)
            .then(() => {
              // getAiModal(menusList[currentMenu].id);
              // setIsModalVisible(false);
              // message.success('添加成功');
              getAiModal(modalList.value[selectedIndex.value].id);
              ElMessage({
                type: 'success',
                message: `添加成功`,
              });
              modalLoading.value = false;
              isModalVisible.value = false;
            })
            .catch(() => {
              modalLoading.value = false;
            });
        } else {
          const editParams = {
            ...deepData,
            // id: modalList[modalListIndex].id,
            // moreConfig: jsonData,
            // version: modalList[modalListIndex].version,
          };
          editParams.moreConfig =
            typeof deepData.moreConfig === 'string'
              ? JSON.parse(deepData.moreConfig)
              : deepData.moreConfig;
          putModelSetApi(editParams)
            .then(() => {
              getAiModal(modalList.value[selectedIndex.value].id);
              isModalVisible.value = false;
              ElMessage({
                type: 'success',
                message: `编辑成功`,
              });
              modalLoading.value = false;
            })
            .catch(() => {
              modalLoading.value = false;
            });
        }
        // modalLoading
        // modalNameloading.value = true;
        // addAiSourceApi(data)
        //   .then(() => {
        //     ElMessage({
        //       type: 'success',
        //       message: `添加成功`,
        //     });
        //     modalNameloading.value = false;
        //     selectedIndex.value = 0;
        //     getList();
        //     isModalNameVisible.value = false;
        //   })
        //   .catch(() => {
        //     modalNameloading.value = false;
        //   });
      }
    });
  };

  const handleSwitchChange = (value, item) => {
    const editParams = {
      aiConfigId: modalList.value[selectedIndex.value].id,
      id: item.id,
      modelEnable: value,
      version: item.version,
    };

    putModelSetApi(editParams)
      .then(() => {
        getAiModal(modalList.value[selectedIndex.value].id);
        ElMessage({
          type: 'success',
          message: `编辑成功`,
        });
      })
      .catch(() => {
        item.modelEnable = !value;
      });
  };

  const handleModelSetDelete = (item) => {
    deleteModelSetApi(item.id).then(() => {
      ElMessage({
        type: 'success',
        message: `删除成功`,
      });
      getAiModal(modalList.value[selectedIndex.value].id);
    });
    // deleteAiModel({ id: item.id });  deleteModelSetApi
    //         message.success(i18n('common.text.successfullyDelete'));
    //         getAiModal(menusList[currentMenu].id)
  };
</script>

<style scoped lang="scss">
  .agent-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .agent-header {
      border-bottom: 1px solid #e6e8ee;
      background: #fff;
      background-color: #ffffff;
      justify-content: space-between;
      align-items: center;
      height: 64px;
      padding: 16px 20px;
      display: flex;
      position: relative;
    }
    .main {
      display: flex;
      flex: 1;
      .sider {
        flex: 0 0 320px;
        background: #ffffff;
        border-right: 1px solid #e6e8ee;
        .menu-item {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          // margin: 0 12px;
          // border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s;
          font-size: 14px;
          color: #606266;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            color: #409eff;
            background-color: #ecf5ff;
            border-right: 3px solid #409eff;
          }

          .menu-label {
            flex: 1;
          }
        }
        .menu-list {
          overflow-y: auto;
          height: calc(100vh - 180px);
        }

        .sidebar-title {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
          padding: 0 20px;
          margin: 15px 0 15px 0;
        }

        .divider {
          height: 1px;
          background-color: #ebeef5;
          margin-bottom: 15px;
        }

        .sider-bottom {
          height: 30px;
          padding: 0 12px;
          .el-button {
            width: 100%;
          }
        }
      }
      .content {
        flex: 1;
        overflow: auto;
        padding: 24px;
        height: calc(100vh - 80px);

        .model-p {
          margin-bottom: 12px;
        }
        :deep(.el-collapse) {
          border-bottom: none;
          border-top: none;
        }
        :deep(.el-collapse-item) {
          border: 1px solid rgba(211, 211, 212, 0.4);
          border-radius: 8px;
          .el-icon {
            margin-left: 12px;
          }
        }
        :deep(.el-collapse-item__header) {
          background-color: rgba(35, 36, 41, 0.02);
          border-bottom: 1px solid rgba(211, 211, 212, 0.4);
          border-radius: 8px 8px 0 0;
          height: 40px;
        }
        :deep(.el-collapse-item__wrap) {
          border-bottom: none;
        }
        .collapse-header {
          display: flex;
          justify-content: space-between;
        }

        .tool-bj {
          background: rgba(255, 77, 79, 0.25);
        }
        .tool-color {
          color: rgba(255, 77, 79, 0.45);
        }
        .visual-bj {
          background: #e4f5e9;
        }
        .visual-color {
          color: #3bbb71;
        }
        .net-bj {
          background: #e2ebff;
        }
        .net-color {
          color: #2d6efe;
        }
        .enb-bj {
          background: #fff1e0;
        }
        .enb-color {
          color: #fdb150;
        }
        .era-bj {
          background: #e8e9f3;
        }
        .era-color {
          color: #878ec8;
        }
        // visual-bj visual-color   era-bj era-color  enb-bj enb-color
        .spaceCollapseSpan {
          width: 42px;
          height: 24px;
          border-radius: 12px;
          margin-left: 12px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          vertical-align: middle;
          font-size: 12px;
          .spaceCollapseSpan-icon {
            font-size: inherit; /* 继承父元素字体大小 */
            line-height: 1;
            margin-left: 0;
          }
        }
      }
    }
  }
</style>
