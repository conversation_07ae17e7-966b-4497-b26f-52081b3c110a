import { ref } from 'vue';
// import device from 'current-device';
export default function useZtree() {
  const ztree = ref<any>();
  const value = ref('');
  const nodeId = ref('');
  const nodeStatus = ref(false);
  const handleClick = (evt, id, node) => {
    if (nodeId.value === node.id && !!nodeId.value) {
      if (!nodeStatus.value) {
        ztree.value.cancelSelectedNode(node);
        nodeId.value = '';
      } else {
        nodeStatus.value = !nodeStatus.value;
      }
    } else {
      nodeId.value = node.id;
    }
  };
  const setting = ref<any>({
    data: {
      simpleData: {
        enable: true,
        pIdKey: 'parentId',
      },
      key: {
        name: 'instName',
      },
    },
    view: {
      showIcon: false,
      dblClickExpand: false, // 双击不允许展开节点
      fontCss: function (treeId, treeNode) {
        // console.log(treeNode);
        return !!treeNode.highlight
          ? { color: '#A60000', 'font-weight': 'bold' }
          : { color: '#000000', 'font-weight': 'normal' };
      },
    },
    edit: {
      drag: {
        isMove: true,
        prev: true,
        next: true,
        inner: false,
      },
      enable: true,
      showRenameBtn: false, //隐藏自带的修改按钮
      showRemoveBtn: false, //隐藏自带的修改按钮
    },
    callback: {
      onClick: handleClick,
    },
  });

  const handleCreated = (ztreeObj) => {
    ztree.value = ztreeObj;
    let node: any = {};
    if (!!nodeId.value) {
      node = ztreeObj.getNodesByParam('id', nodeId.value, null)[0];
      nodeStatus.value = true;
      ztreeObj.selectNode(node);
      ztreeObj.setting.callback.onClick('', ztreeObj.setting.treeId, node);
    }
    if (!nodeId.value) {
      if (ztreeObj.getNodes()[0]) {
        ztreeObj.expandNode(ztreeObj.getNodes()[0], true);
      }
    }
  };

  return {
    setting,
    handleCreated,
    ztree,
    value,
    nodeId,
    nodeStatus,
  };
}
