/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 统一的API服务导出
 * 提供所有封装后的API服务
 */

// 导出类型定义
export type * from './types'

// 导出核心组件
export { FetchWrapper } from './fetch-wrapper'
export { StreamResponseHandler } from './stream-handler'
export { ApiClient } from './api-client'

// 导出具体的API服务
export { DataSourceApi, dataSourceApi } from './datasource-api'
export { ModelApi, modelApi } from './model-api'
export { DirectApi, directApi } from './direct-api'
export { ToolDetailApi, toolDetailApi } from './tool-detail-api'

// 导出API服务实例（推荐使用）
export const apiServices = {
  dataSource: dataSourceApi,
  model: modelApi,
  direct: directApi,
  toolDetail: toolDetailApi
} as const

// 导出工具函数
export const apiUtils = {
  /**
   * 创建自定义API客户端
   */
  createApiClient: (baseURL: string, config?: any) => {
    return new (class extends ApiClient {
      constructor() {
        super(baseURL, config)
      }
    })()
  },

  /**
   * 创建fetch包装器
   */
  createFetchWrapper: (config?: any) => {
    return new FetchWrapper(config)
  },

  /**
   * 格式化API错误
   */
  formatApiError: (error: any): string => {
    if (error?.message) {
      return error.message
    }
    if (error?.status && error?.statusText) {
      return `HTTP ${error.status}: ${error.statusText}`
    }
    if (typeof error === 'string') {
      return error
    }
    return '未知错误'
  },

  /**
   * 检查是否为网络错误
   */
  isNetworkError: (error: any): boolean => {
    return error?.name === 'TypeError' && error?.message?.includes('fetch')
  },

  /**
   * 检查是否为超时错误
   */
  isTimeoutError: (error: any): boolean => {
    return error?.name === 'AbortError' || error?.message?.includes('timeout')
  },

  /**
   * 检查是否为认证错误
   */
  isAuthError: (error: any): boolean => {
    return error?.status === 401 || error?.status === 403
  },

  /**
   * 检查是否为服务器错误
   */
  isServerError: (error: any): boolean => {
    return error?.status >= 500
  },

  /**
   * 重试函数
   */
  retry: async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    let lastError: any
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        
        if (i < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
        }
      }
    }
    
    throw lastError
  },

  /**
   * 并发请求限制
   */
  concurrentLimit: async <T>(
    tasks: (() => Promise<T>)[],
    limit: number = 5
  ): Promise<T[]> => {
    const results: T[] = []
    const executing: Promise<any>[] = []

    for (const task of tasks) {
      const promise = task().then(result => {
        results.push(result)
        executing.splice(executing.indexOf(promise), 1)
        return result
      })

      executing.push(promise)

      if (executing.length >= limit) {
        await Promise.race(executing)
      }
    }

    await Promise.all(executing)
    return results
  }
} as const

// 默认导出
export default apiServices
