<template>
  <div class="global-config-content">
    <h2>全局配置</h2>
    <basic-table
      :columns="columns"
      :data="configList"
      :downSetting="false"
      :setting="false"
      :pagination="false"
      :border="true"
    >
      <template #configValue="{ record }">
        <el-tag
          v-if="record.configKey === 'headless'"
          :type="record.configValue === 'true' ? 'success' : 'danger'"
          >{{ record.configValue === 'true' ? '开启' : '关闭' }}</el-tag
        >
        <el-tag
          v-else-if="record.configKey === 'consoleQuery'"
          :type="record.configValue === 'true' ? 'success' : 'danger'"
          >{{ record.configValue === 'true' ? '开启' : '关闭' }}</el-tag
        >
        <el-tag v-else-if="record.configKey === 'maxSteps'">{{
          record.configValue
        }}</el-tag>
        <el-tag v-else-if="record.configKey === 'userInputTimeout'">{{
          record.configValue
        }}</el-tag>
        <el-tag v-else-if="record.configKey === 'requestTimeout'">{{
          record.configValue
        }}</el-tag>
        <el-tag
          v-else-if="record.configKey === 'resetAgents'"
          :type="record.configValue === 'true' ? 'success' : 'danger'"
          >{{ record.configValue === 'true' ? '已重置' : '未重置' }}</el-tag
        >

        <el-tag
          v-else-if="record.configKey === 'openBrowser'"
          :type="record.configValue === 'true' ? 'success' : 'danger'"
          >{{ record.configValue === 'true' ? '是' : '否' }}</el-tag
        >

        <el-tag
          v-else-if="record.configKey === 'debug'"
          :type="record.configValue ? 'success' : 'danger'"
          >{{ record.configValue ? '开启' : '关闭' }}</el-tag
        >
        <span v-else>{{ record.configValue }}</span>
      </template>
      <template #action="{ record }">
        <el-button
          type="primary"
          size="default"
          :round="false"
          :link="true"
          @click="handleEdit(record)"
          >配置</el-button
        >
      </template>
    </basic-table>

    <el-drawer v-model="drawer" direction="rtl" size="600px" :destroy-on-close="true">
      <template #header>
        <p class="drawer-title">配置</p>
      </template>
      <template #default>
        <basic-form
          :formList="formSchema"
          :isCreate="false"
          :formData="formData"
          :showSubmit="false"
          :check-strictly="true"
          ref="formRef"
        >
          <template #configValue>
            <div v-if="formData.configKey === 'headless'">
              <el-switch
                v-model="formData.configValue"
                active-text="开启"
                inactive-text="关闭"
              />
            </div>
            <div v-else-if="formData.configKey === 'consoleQuery'">
              <el-switch
                v-model="formData.configValue"
                active-text="开启"
                inactive-text="关闭"
              />
            </div>
            <div
              v-else-if="
                formData.configKey === 'maxSteps' ||
                formData.configKey === 'userInputTimeout' ||
                formData.configKey === 'requestTimeout'
              "
            >
              <el-input-number v-model="formData.configValue" :min="1" />
            </div>
            <div v-else-if="formData.configKey === 'resetAgents'">
              <el-switch
                v-model="formData.configValue"
                active-text="是"
                inactive-text="否"
              />
            </div>
            <div v-else-if="formData.configKey === 'debug'">
              <el-switch
                v-model="formData.configValue"
                active-text="开启"
                inactive-text="关闭"
            /></div>
            <div v-else> <el-input v-model="formData.configValue" /></div>
          </template>
        </basic-form>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawer = false">取消</el-button>
          <el-button type="primary" @click="confirmClick" :loading="loading"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { getManusListApi, putManusListApi } from '../../api/agentSet';
  import { cloneDeep } from 'lodash-es';
  import { TableOptions } from '/@/components/sys/BasicTable/types';

  const columns: TableOptions[] = [
    {
      label: '配置key',
      prop: 'configKey',
      ellipsis: true,
      align: 'center',
      width: 300,
    },
    {
      label: '描述',
      prop: 'description',
      ellipsis: true,
      align: 'center',
    },
    {
      label: '值',
      prop: 'configValue',
      slot: 'configValue',
      ellipsis: true,
      width: 160,
      align: 'center',
    },
    {
      label: '操作',
      prop: 'action',
      action: true,
      width: 80,
      align: 'center',
      fixed: 'right',
    },
  ];

  const formSchema: any[] = [
    {
      field: 'configKey',
      label: '配置key',
      component: 'Text',
    },
    {
      field: 'description',
      label: '描述',
      component: 'Text',
    },
    {
      field: 'configValue',
      label: '值',
      slot: 'configValue',
      // component: 'Select',

      // componentProps: {
      //   placeholder: '请输入岗位职级',
      //   options: [],
      // },
      // required: true,
    },
  ];

  const configList = ref<any[]>([]);

  const loading = ref(false);
  const drawer = ref(false);
  const formData = ref<any>({
    configKey: '',
    description: '',
    defaultValue: null,
    id: '',
    configGroup: '',
    configSubGroup: '',
    configPath: '',
    configValue: null,
    inputType: '',
    optionsJson: '',
    updateTime: '',
    createTime: '',
  });
  const formRef = ref();
  const getList = async () => {
    const res = await getManusListApi();
    console.log(res);
    configList.value = res;
  };
  getList();

  const handleEdit = (item) => {
    formData.value = cloneDeep(item);
    if (
      formData.value.configKey === 'maxSteps' ||
      formData.value.configKey === 'userInputTimeout' ||
      formData.value.configKey === 'requestTimeout'
    ) {
      formData.value.configValue = Number(formData.value.configValue);
    } else if (formData.value.configKey === 'baseDir') {
      formData.value.configValue = formData.value.configValue;
    } else {
      formData.value.configValue = formData.value.configValue === 'true'; // true
    }
    drawer.value = true;
  };
  const confirmClick = () => {
    const getData = formRef.value.submitForm;
    const ruleFormRef = formRef.value.ruleFormRef;
    getData(ruleFormRef, (status) => {
      if (status == 'success') {
        loading.value = true;
        formData.value.configValue = String(formData.value.configValue);
        putManusListApi([formData.value])
          .then(() => {
            ElMessage({
              type: 'success',
              message: '修改成功',
            });
            getList();
            drawer.value = false;
            loading.value = false;
          })
          .catch(() => {
            loading.value = false;
          });
      }
    });
  };
</script>

<style scoped lang="scss">
  .global-config-content {
    background: #ffffff;
    width: 100%;
    height: 100%;
    padding: 20px;
  }

  .global-config-content h2 {
    font-size: 20px;
    font-weight: bold;
    color: #303133;
    margin: 0 0 20px 0;
  }
  .drawer-title {
    font-size: 18px;
  }
</style>
