/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 工具详情API服务
 * 基于统一的fetch封装实现
 */

import { ApiClient } from './api-client'
import type { 
  ToolDetailResponse, 
  ToolParametersResponse, 
  ActionResultResponse, 
  RequestOptions 
} from './types'

export class ToolDetailApi extends ApiClient {
  constructor() {
    super('/api/streaming-events', {
      enableLogging: true,
      enableCache: true,
      defaultCacheTime: 30 * 1000, // 30秒缓存
      enableRetry: true,
      defaultRetryCount: 2
    })
  }

  /**
   * 获取工具的完整详细信息
   */
  async getToolDetail(thinkActId: string | number, options?: RequestOptions): Promise<ToolDetailResponse | null> {
    try {
      const response = await this.get<ToolDetailResponse>(
        `/think-act/${thinkActId}/detail`,
        undefined,
        {
          cache: true,
          cacheTime: 30 * 1000, // 30秒缓存
          ...options
        }
      )

      return response
    } catch (error: any) {
      if (error?.status === 404) {
        console.warn('⚠️ 工具详情不存在:', thinkActId)
        return null
      }
      
      console.error('❌ 获取工具详情失败:', error)
      throw error
    }
  }

  /**
   * 获取工具参数
   */
  async getToolParameters(thinkActId: string | number, options?: RequestOptions): Promise<ToolParametersResponse | null> {
    try {
      const response = await this.get<ToolParametersResponse>(
        `/think-act/${thinkActId}/tool-parameters`,
        undefined,
        {
          cache: true,
          cacheTime: 30 * 1000, // 30秒缓存
          ...options
        }
      )

      return response
    } catch (error: any) {
      if (error?.status === 404) {
        console.warn('⚠️ 工具参数不存在:', thinkActId)
        return null
      }
      
      console.error('❌ 获取工具参数失败:', error)
      throw error
    }
  }

  /**
   * 获取执行结果
   */
  async getActionResult(thinkActId: string | number, options?: RequestOptions): Promise<ActionResultResponse | null> {
    try {
      const response = await this.get<ActionResultResponse>(
        `/think-act/${thinkActId}/action-result`,
        undefined,
        {
          cache: true,
          cacheTime: 30 * 1000, // 30秒缓存
          ...options
        }
      )

      return response
    } catch (error: any) {
      if (error?.status === 404) {
        console.warn('⚠️ 执行结果不存在:', thinkActId)
        return null
      }
      
      console.error('❌ 获取执行结果失败:', error)
      throw error
    }
  }

  /**
   * 批量获取多个工具的详细信息
   */
  async getBatchToolDetails(thinkActIds: (string | number)[], options?: RequestOptions): Promise<(ToolDetailResponse | null)[]> {
    try {
      console.log('🔍 批量获取工具详情:', thinkActIds)

      const requests = thinkActIds.map(id => ({
        url: `/think-act/${id}/detail`,
        method: 'GET' as const
      }))

      const results = await this.batchRequest<ToolDetailResponse>(requests, options)

      return results.map((result, index) => {
        if (result) {
          return result
        } else {
          console.error(`❌ 获取工具详情失败 [${thinkActIds[index]}]`)
          return null
        }
      })
    } catch (error: any) {
      console.error('❌ 批量获取工具详情失败:', error)
      throw error
    }
  }

  /**
   * 批量获取工具参数
   */
  async getBatchToolParameters(thinkActIds: (string | number)[], options?: RequestOptions): Promise<(ToolParametersResponse | null)[]> {
    try {
      console.log('🔍 批量获取工具参数:', thinkActIds)

      const requests = thinkActIds.map(id => ({
        url: `/think-act/${id}/tool-parameters`,
        method: 'GET' as const
      }))

      const results = await this.batchRequest<ToolParametersResponse>(requests, options)

      return results.map((result, index) => {
        if (result) {
          return result
        } else {
          console.error(`❌ 获取工具参数失败 [${thinkActIds[index]}]`)
          return null
        }
      })
    } catch (error: any) {
      console.error('❌ 批量获取工具参数失败:', error)
      throw error
    }
  }

  /**
   * 批量获取执行结果
   */
  async getBatchActionResults(thinkActIds: (string | number)[], options?: RequestOptions): Promise<(ActionResultResponse | null)[]> {
    try {
      console.log('🔍 批量获取执行结果:', thinkActIds)

      const requests = thinkActIds.map(id => ({
        url: `/think-act/${id}/action-result`,
        method: 'GET' as const
      }))

      const results = await this.batchRequest<ActionResultResponse>(requests, options)

      return results.map((result, index) => {
        if (result) {
          return result
        } else {
          console.error(`❌ 获取执行结果失败 [${thinkActIds[index]}]`)
          return null
        }
      })
    } catch (error: any) {
      console.error('❌ 批量获取执行结果失败:', error)
      throw error
    }
  }

  /**
   * 检查工具详情是否可用
   */
  async isToolDetailAvailable(thinkActId: string | number, options?: RequestOptions): Promise<boolean> {
    try {
      await this.head(`/think-act/${thinkActId}/detail`, options)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 获取工具执行时间线
   */
  async getToolTimeline(thinkActId: string | number, options?: RequestOptions): Promise<any> {
    try {
      const detail = await this.getToolDetail(thinkActId, options)
      if (!detail) return null

      const timeline = []

      // 思考阶段
      if (detail.thinkStartTime) {
        timeline.push({
          phase: 'think',
          action: 'start',
          timestamp: detail.thinkStartTime,
          description: '开始思考'
        })
      }

      if (detail.thinkEndTime) {
        timeline.push({
          phase: 'think',
          action: 'end',
          timestamp: detail.thinkEndTime,
          description: '思考完成'
        })
      }

      // 执行阶段
      if (detail.actStartTime) {
        timeline.push({
          phase: 'action',
          action: 'start',
          timestamp: detail.actStartTime,
          description: '开始执行'
        })
      }

      if (detail.actEndTime) {
        timeline.push({
          phase: 'action',
          action: 'end',
          timestamp: detail.actEndTime,
          description: '执行完成'
        })
      }

      return timeline.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
    } catch (error) {
      console.error('获取工具时间线失败:', error)
      return null
    }
  }

  /**
   * 计算工具执行耗时
   */
  async getToolDuration(thinkActId: string | number, options?: RequestOptions): Promise<{
    thinkDuration?: number
    actionDuration?: number
    totalDuration?: number
  } | null> {
    try {
      const detail = await this.getToolDetail(thinkActId, options)
      if (!detail) return null

      const result: any = {}

      // 计算思考耗时
      if (detail.thinkStartTime && detail.thinkEndTime) {
        const thinkStart = new Date(detail.thinkStartTime).getTime()
        const thinkEnd = new Date(detail.thinkEndTime).getTime()
        result.thinkDuration = thinkEnd - thinkStart
      }

      // 计算执行耗时
      if (detail.actStartTime && detail.actEndTime) {
        const actStart = new Date(detail.actStartTime).getTime()
        const actEnd = new Date(detail.actEndTime).getTime()
        result.actionDuration = actEnd - actStart
      }

      // 计算总耗时
      if (detail.thinkStartTime && detail.actEndTime) {
        const totalStart = new Date(detail.thinkStartTime).getTime()
        const totalEnd = new Date(detail.actEndTime).getTime()
        result.totalDuration = totalEnd - totalStart
      }

      return result
    } catch (error) {
      console.error('计算工具执行耗时失败:', error)
      return null
    }
  }

  /**
   * 格式化工具参数为可读的JSON字符串
   */
  formatParameters(parameters: any): string {
    if (!parameters) return '暂无参数'

    try {
      if (typeof parameters === 'string') {
        // 如果已经是字符串，尝试解析再格式化
        const parsed = JSON.parse(parameters)
        return JSON.stringify(parsed, null, 2)
      } else {
        // 如果是对象，直接格式化
        return JSON.stringify(parameters, null, 2)
      }
    } catch (e) {
      // 如果解析失败，返回原始字符串
      return String(parameters)
    }
  }

  /**
   * 格式化执行结果为可读的JSON字符串
   */
  formatResult(result: any): string {
    if (!result) return '暂无结果'

    try {
      if (typeof result === 'string') {
        // 如果已经是字符串，尝试解析再格式化
        const parsed = JSON.parse(result)
        return JSON.stringify(parsed, null, 2)
      } else {
        // 如果是对象，直接格式化
        return JSON.stringify(result, null, 2)
      }
    } catch (e) {
      // 如果解析失败，返回原始字符串
      return String(result)
    }
  }

  /**
   * 获取工具状态描述
   */
  getStatusDescription(status: string): string {
    const statusMap: Record<string, string> = {
      'PENDING': '等待中',
      'THINKING': '思考中',
      'ACTING': '执行中',
      'COMPLETED': '已完成',
      'FAILED': '执行失败',
      'CANCELLED': '已取消',
      'TIMEOUT': '执行超时'
    }

    return statusMap[status] || status
  }

  /**
   * 检查工具是否执行成功
   */
  isToolSuccessful(detail: ToolDetailResponse): boolean {
    return detail.status === 'COMPLETED' && !detail.errorInfo
  }

  /**
   * 获取工具错误信息
   */
  getToolError(detail: ToolDetailResponse): string | null {
    return detail.errorInfo || null
  }
}

// 导出单例实例
export const toolDetailApi = new ToolDetailApi()
