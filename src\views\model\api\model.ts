/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
import { defHttp } from '/@/utils/axios';

enum Api {
  getConfigList = '/cpit/ai/setting/config/list',
  config = '/cpit/ai/setting/config',
  model = '/cpit/ai/setting/model/list',
  modelType = '/cpit/ai/setting/model/modelType',
  modelSet = '/cpit/ai/setting/model',
}
export const getConfigListApi = () => {
  return defHttp.get({ url: Api.getConfigList });
};
// const deleteAiSource = createRequest<{ id: string }, boolean>('/cpit/ai/setting/config/:id', {
//   method: 'delete',
// });
export const deleteAiSourceApi = (id: string) =>
  defHttp.delete({ url: `${Api.config}/${id}` });

export const addAiSourceApi = (params) => defHttp.post({ url: Api.config, params });
export const getByIdAiSourceApi = (id: string) =>
  defHttp.get({ url: `${Api.model}/${id}` });

export const putAiSourceApi = (params) => defHttp.put({ url: Api.config, params });
export const getModelTypeApi = () => defHttp.get({ url: `${Api.modelType}` });
export const addModelSetApi = (params) => defHttp.post({ url: Api.modelSet, params });
export const putModelSetApi = (params) => defHttp.put({ url: Api.modelSet, params });
export const deleteModelSetApi = (id: string) =>
  defHttp.delete({ url: `${Api.modelSet}/${id}` });
// const deleteAiModel = createRequest<{ id: string }, boolean>('/cpit/ai/setting/model/:id', {
//   method: 'delete',
// });
