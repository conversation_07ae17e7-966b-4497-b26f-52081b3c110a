import { nextTick } from 'vue';
import { useTenantStore } from '/@/stores/modules/tenant';
import { TenantInfo } from '/@/api/model/Tenant';
import { useUserStore } from '/@/stores/modules/user';
import { cloneDeep } from 'lodash-es';

export function useTenantChange() {
  const useTenant = useTenantStore();
  const userStore = useUserStore();
  async function requestTenantProject(needFreshMenu = true) {
    await useTenant.requestData(userStore.getUserInfo);
    if (!needFreshMenu) return;
    // 刷新用户的权限数据
    // await userStore.reFreshUser();
    // 刷新租户数据
    await userStore.resetData();
    // await userStore.refreshUserData();
  }
  async function setCurTenant(tenant: TenantInfo) {
    // 获取新的应用列表
    nextTick(async () => {
      const userInfo = cloneDeep(userStore.getUserInfo);
      useTenant.keepTenantHeaderInfo();
      if (userInfo.currentDept) {
        userInfo.currentDept.id = '';
      }
      userInfo.projectId = '';
      userInfo.tenantId = tenant.id;
      // 刷新用户信息
      userStore.setUserInfo(userInfo);
      useTenant.setCurTenant(tenant);

      // // 刷新用户的权限数据
      await userStore.reFreshUser();
      // // 刷新租户数据
      await userStore.refreshUserData();
      await useTenant.requestProjectInfos(userInfo);
      await useTenant.requestDeptInfos(userInfo);
    });
  }
  return { requestTenantProject, setCurTenant };
}
