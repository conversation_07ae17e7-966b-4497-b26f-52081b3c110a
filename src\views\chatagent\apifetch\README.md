# ChatAgent API Fetch 封装

## 概述

这是一个基于 fetch 的统一 API 封装层，提供了完整的请求配置、错误处理、缓存、重试、流式响应等功能。相比直接使用 fetch，这个封装层提供了更好的开发体验和更强的功能。

## 特性

- 🚀 **统一的请求配置**：支持全局和局部配置
- 🔄 **自动重试机制**：可配置的重试次数和延迟
- 💾 **智能缓存**：支持请求缓存和缓存过期
- 🔍 **请求去重**：避免重复请求
- 📊 **请求统计**：提供详细的请求统计信息
- 🌊 **流式响应**：支持 SSE 和其他流式响应
- 🛡️ **错误处理**：统一的错误处理和类型安全
- 📝 **完整的 TypeScript 支持**：全面的类型定义
- 🔧 **拦截器支持**：请求和响应拦截器
- 📈 **性能监控**：请求时间和成功率统计

## 目录结构

```
apifetch/
├── types.ts              # 类型定义
├── fetch-wrapper.ts      # 核心 fetch 封装
├── stream-handler.ts     # 流式响应处理
├── api-client.ts         # API 客户端基类
├── datasource-api.ts     # 数据源 API
├── model-api.ts          # 模型 API
├── direct-api.ts         # 直接执行 API
├── tool-detail-api.ts    # 工具详情 API
├── index.ts              # 统一导出
└── README.md             # 使用文档
```

## 快速开始

### 1. 基本使用

```typescript
import { apiServices } from '../apifetch'

// 获取数据源列表
const dataSources = await apiServices.dataSource.getDataSourceList()

// 获取模型列表
const models = await apiServices.model.getBriefModelList()

// 发送直接执行请求
const result = await apiServices.direct.sendMessage('你好')
```

### 2. 流式响应

```typescript
import { apiServices } from '../apifetch'

// 创建流式处理器
const handler = apiServices.direct.createSimpleStreamHandler(
  (event) => {
    console.log('收到事件:', event)
  },
  (error) => {
    console.error('流式错误:', error)
  },
  () => {
    console.log('流式完成')
  }
)

// 发送流式请求
await apiServices.direct.sendMessageWithStreamingDirect(
  'template-id',
  {
    chatId: 'chat-123',
    userId: 'user-456',
    message: '你好'
  },
  handler
)
```

### 3. 自定义配置

```typescript
import { FetchWrapper } from '../apifetch'

const customApi = new FetchWrapper({
  baseURL: '/api/custom',
  timeout: 30000,
  enableCache: true,
  defaultCacheTime: 10 * 60 * 1000, // 10分钟
  enableRetry: true,
  defaultRetryCount: 3
})

// 添加请求拦截器
customApi.addRequestInterceptor({
  onFulfilled: (config) => {
    config.headers = {
      ...config.headers,
      'X-Custom-Header': 'value'
    }
    return config
  }
})
```

## API 服务

### DataSource API

```typescript
import { dataSourceApi } from '../apifetch'

// 获取数据源列表
const dataSources = await dataSourceApi.getDataSourceList()

// 获取数据库列表
const databases = await dataSourceApi.getDatabaseList(dataSourceId)

// 获取模式列表
const schemas = await dataSourceApi.getSchemaList(dataSourceId, databaseName)

// 测试连接
const isConnected = await dataSourceApi.testConnection(dataSourceId)
```

### Model API

```typescript
import { modelApi } from '../apifetch'

// 获取模型列表
const models = await modelApi.getBriefModelList()

// 获取模型详情
const model = await modelApi.getModelById('model-id')

// 测试模型
const isAvailable = await modelApi.testModel('model-id')

// 格式化模型名称
const displayName = modelApi.formatModelDisplayName(model)
```

### Direct API

```typescript
import { directApi } from '../apifetch'

// 直接执行
const result = await directApi.sendMessage('查询用户数据')

// 获取执行详情
const details = await directApi.getExecutionDetails('plan-id')

// 提交表单输入
await directApi.submitFormInput('plan-id', formData)
```

### Tool Detail API

```typescript
import { toolDetailApi } from '../apifetch'

// 获取工具详情
const detail = await toolDetailApi.getToolDetail('think-act-id')

// 获取工具参数
const parameters = await toolDetailApi.getToolParameters('think-act-id')

// 获取执行结果
const result = await toolDetailApi.getActionResult('think-act-id')

// 批量获取
const details = await toolDetailApi.getBatchToolDetails(['id1', 'id2'])
```

## 迁移指南

### 从原始 fetch 迁移

**原来的代码：**
```typescript
const response = await fetch('/api/datasource/list', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
})

if (!response.ok) {
  throw new Error(`HTTP ${response.status}: ${response.statusText}`)
}

const data = await response.json()
```

**新的代码：**
```typescript
import { dataSourceApi } from '../apifetch'

const data = await dataSourceApi.getDataSourceList()
```

### 从原始 API 服务迁移

**原来的代码：**
```typescript
import { DataSourceApiService } from '../api/datasource-api-service'

const service = new DataSourceApiService()
const dataSources = await service.getDataSourceList()
```

**新的代码：**
```typescript
import { dataSourceApi } from '../apifetch'

const dataSources = await dataSourceApi.getDataSourceList()
```

## 配置选项

### FetchWrapper 配置

```typescript
interface FetchWrapperConfig {
  baseURL?: string                    // 基础URL
  timeout?: number                    // 超时时间（毫秒）
  headers?: Record<string, string>    // 默认请求头
  enableLogging?: boolean             // 是否启用日志
  logLevel?: LogLevel                 // 日志级别
  enableCache?: boolean               // 是否启用缓存
  defaultCacheTime?: number           // 默认缓存时间
  enableDedupe?: boolean              // 是否启用请求去重
  dedupeWindow?: number               // 去重时间窗口
  enableRetry?: boolean               // 是否启用重试
  defaultRetryCount?: number          // 默认重试次数
  defaultRetryDelay?: number          // 默认重试延迟
  errorHandlingMode?: 'throw' | 'return' | 'silent'  // 错误处理模式
}
```

### 请求选项

```typescript
interface RequestOptions {
  showLoading?: boolean               // 是否显示加载状态
  showError?: boolean                 // 是否显示错误消息
  errorMessageMode?: 'message' | 'modal' | 'none'  // 错误消息模式
  retry?: boolean                     // 是否重试
  retryCount?: number                 // 重试次数
  retryDelay?: number                 // 重试延迟
  cache?: boolean                     // 是否缓存
  cacheTime?: number                  // 缓存时间
  dedupe?: boolean                    // 是否去重复请求
  requestId?: string                  // 请求标识
}
```

## 错误处理

### 统一错误格式

```typescript
interface ApiError {
  message: string
  status?: number
  statusText?: string
  code?: string
  data?: any
  config?: RequestConfig
}
```

### 错误处理示例

```typescript
import { apiUtils } from '../apifetch'

try {
  const result = await dataSourceApi.getDataSourceList()
} catch (error) {
  if (apiUtils.isNetworkError(error)) {
    console.error('网络错误')
  } else if (apiUtils.isAuthError(error)) {
    console.error('认证错误')
  } else if (apiUtils.isServerError(error)) {
    console.error('服务器错误')
  } else {
    console.error('其他错误:', apiUtils.formatApiError(error))
  }
}
```

## 性能优化

### 缓存策略

```typescript
// 启用缓存
const result = await dataSourceApi.getDataSourceList({
  cache: true,
  cacheTime: 5 * 60 * 1000  // 5分钟缓存
})

// 清除缓存
dataSourceApi.clearCache()
```

### 并发控制

```typescript
import { apiUtils } from '../apifetch'

// 限制并发请求数量
const tasks = ids.map(id => () => modelApi.getModelById(id))
const results = await apiUtils.concurrentLimit(tasks, 3)  // 最多3个并发
```

### 请求去重

```typescript
// 自动去重相同的请求
const [result1, result2] = await Promise.all([
  dataSourceApi.getDataSourceList(),  // 实际发送请求
  dataSourceApi.getDataSourceList()   // 复用第一个请求的结果
])
```

## 监控和调试

### 获取统计信息

```typescript
const stats = dataSourceApi.getStats()
console.log('请求统计:', stats)
// {
//   total: 100,
//   success: 95,
//   error: 5,
//   cached: 20,
//   retried: 3,
//   averageTime: 250,
//   lastRequestTime: 1640995200000
// }
```

### 日志配置

```typescript
const api = new FetchWrapper({
  enableLogging: true,
  logLevel: 'debug'  // 'debug' | 'info' | 'warn' | 'error' | 'none'
})
```

## 最佳实践

1. **使用单例实例**：推荐使用导出的单例实例而不是创建新实例
2. **合理设置缓存**：对于不经常变化的数据启用缓存
3. **错误处理**：始终处理可能的错误情况
4. **类型安全**：充分利用 TypeScript 类型定义
5. **性能监控**：定期检查请求统计信息
6. **日志记录**：在开发环境启用详细日志

## 注意事项

1. **流式响应**：流式响应需要特殊处理，不支持缓存和重试
2. **文件上传**：使用 FormData 时会自动处理 Content-Type
3. **取消请求**：使用 AbortController 来取消请求
4. **内存管理**：定期清理缓存避免内存泄漏
