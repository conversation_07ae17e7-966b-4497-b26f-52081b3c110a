<template>
  <div v-if="isVertical" class="sidebar-container" style="height: 100vh">
    <div class="flex h-18 p-1 text-white text-2xl w-full justify-center items-center">
      <a href="/dashboard">
        <img class="w-8 h-8" src="/mainlogo-upms.svg" style="width: 36px" />
      </a>
      <a href="/dashboard">
        <span class="pl-1 title-dark" v-if="!getMenuCollapsed">{{
          VITE_APP_TITLE
        }}</span>
      </a>
    </div>
    <div>
      <sys-menu
        :mode="mode"
        :menu-list="menuList"
        :collapsed="getMenuCollapsed"
        :deeps="0"
      />
    </div>
  </div>
  <div
    v-if="isHorizontal"
    class="cpvf-horizontal bg-white text-gray-800 flex"
    style="height: 60px"
  >
    <div
      :style="{
        width: appStore.getProjectConfig.menuWidth + 'px',
      }"
      class="flex h-18 p-1 text-white text-2xl w-full justify-center items-center"
    >
      <img class="w-8 h-8" src="/mainlogo-upms.svg" />
      <span class="pl-1 sys-title">{{ VITE_APP_TITLE }}</span>
    </div>
    <!-- <div
      :style="{
        width: `calc(100% - ${appStore.getProjectConfig.menuWidth + 160}px)`,
        marginTop: '10px',
      }"
    > -->
    <div
      :style="{
        width: `calc(100% - ${appStore.getProjectConfig.menuWidth + 130}px)`,
      }"
    >
      <sys-menu :mode="mode" :menu-list="menuList" :deeps="0" />
    </div>
    <!-- 菜单水平时，显示用户 -->
    <LoginOut />
  </div>
</template>

<script lang="ts" setup name="DefaultSide">
  import { PropType } from 'vue';
  import { Menu } from '/@/stores/types';
  import { SysMenu } from '/@/components/sys/Menu';
  import { useMenuSettings } from '/@/hooks/settings/useMenuSettings';
  import { warpperEnv } from '@build/index';
  import { useAppStore } from '/@/stores/modules/app';
  import LoginOut from './Logout.vue';

  const { VITE_APP_TITLE } = warpperEnv();
  const appStore = useAppStore();

  defineProps({
    menuList: {
      type: Array as PropType<Menu[]>,
      default: () => [],
    },
    mode: {
      type: String as PropType<string>,
      default: 'vertical', // 'vertical' | 'horizontal'
    },
  });

  const { getMenuCollapsed, isVertical, isHorizontal } = useMenuSettings();
  defineExpose({ getMenuCollapsed });
</script>
<style scoped>
  .cpvf-horizontal {
    background: var(--el-menu-bg-color-theme);
    border-bottom: solid 1px var(--el-menu-border-color);
  }

  .sidebar-container {
    --tw-bg-opacity: 1;
    background: rgba(31, 41, 55, var(--tw-bg-opacity));
  }

  .title-dark {
    color: #000000;
    font-size: 20px;
  }
</style>
