<template>
  <div class="left-msg">
    <div class="title">最新消息</div>
    <div class="infinite-list-wrapper" style="overflow: auto" v-if="page.total > 0">
      <ul v-infinite-scroll="load" class="list" :infinite-scroll-disabled="disabled">
        <div class="msg-item" v-for="i in listData" :key="i.id">
          <div class="content" @click="handleDetail(i)">{{ i.title }}</div>
          <div class="time">{{ i.publisherTime }}</div>
        </div>
      </ul>
      <p class="load-more" v-if="loading">加载中...</p>
      <p class="load-more" v-if="noMore">没有更多数据了</p>
    </div>
    <el-empty :image-size="100" v-else description="暂无数据" />
  </div>
  <el-dialog
    v-model="viewDialogVisible"
    append-to-body
    :width="600"
    title="查看我的消息"
  >
    <div class="view-content">
      <div class="remind">
        <div class="remind-content">
          <div v-dompurify-html="content"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { myNotices } from '../api/project.ts';
  const count = ref(0);
  const loading = ref(false);
  const noMore = computed(() => count.value >= page.value.total);
  const disabled = computed(() => loading.value || noMore.value);
  const listData = ref([]);
  const viewDialogVisible = ref(false);
  const content = ref('');
  const page = ref({
    total: 10,
    pageSize: 5,
    pageNum: 1,
  });
  const load = () => {
    console.log('loading');
    loading.value = true;
    // getNotice();
    setTimeout(() => {
      getNotice();
    }, 2000);
  };
  const getNotice = () => {
    const { pageNum, pageSize } = page.value;
    myNotices({
      pageNum,
      pageSize,
      read: '',
    }).then((res) => {
      const { list, total } = res;
      loading.value = false;
      count.value += list.length;
      page.value.total = total;
      listData.value = listData.value.concat(list);
      page.value.pageNum++;
    });
  };
  /**
   * 查看我的消息
   */
  const handleDetail = (item) => {
    viewDialogVisible.value = true;
    content.value = item.content;
    // getNoticeId(item.id).then((res) => {
    //   formData.value = res;
    //   init();
    // });
  };
  load();
</script>
<style scoped lang="scss">
  .left-msg {
    width: 100%;
    height: 309px;
    background: #fff;
    // margin-top: 10px;
    padding: 20px 24px;
    border-radius: 8px;
    .title {
      width: 100%;
      height: 40px;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      line-height: 24px;
      font-weight: 600;
    }
    .infinite-list-wrapper .list {
      padding: 0;
      margin: 0;
      list-style: none;
      width: 100%;
      // height: 200px;
      // display: flex;
      // overflow: auto;
      .msg-item {
        width: 100%;
        height: 40px;
        display: flex;
        border-bottom: 1px solid rgba(227, 231, 233, 1);
        padding: 10px 0;
        background: #fff;
        .content {
          width: calc(100% - 140px);
          font-size: 14px;
          color: #333333;
          letter-spacing: 0;
          line-height: 24px;
          font-weight: 500;
          background: #fff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }
        .time {
          width: 120px;
          font-size: 12px;
          color: #666666;
          letter-spacing: 0;
          line-height: 20px;
          font-weight: 400;
        }
      }
    }
    .load-more {
      margin-top: 10px;
      text-align: center;
    }
    .infinite-list-wrapper .list-item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 50px;
      background: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
    }
    .infinite-list-wrapper .list-item + .list-item {
      margin-top: 10px;
    }
  }
  .infinite-list-wrapper {
    height: 220px;
    // text-align: center;
  }
</style>
