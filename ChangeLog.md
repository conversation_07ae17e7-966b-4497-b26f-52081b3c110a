## v2.6.2 (2025/??/??)

Features

- 新增：短信验证码登录
- 优化：个人信息-手机号、邮箱加密
- 优化：修改密码-验证码、userId 加密
- 修复：角色管理-菜单权限出现获取已授权数据失败的问题
- 修复：账号管理下载导入模板
- 修复：消息公告-富文本插入链接文本颜色、生效时间限制范围、公告内容回填等问题
- 修复：忘记密码-验证码加密传输
- 修复：员工管理-所属板块获取对应字典的数据
- 修复：上次登录信息时间格式修改

## v2.6.1 (2025/04/02)

Features

- 新增：验证码功能
- 修复：工作台 vue-grid-layout 响应式布局
- 修复：注册页面、员工相关 bug

## v2.6.0 (2025/02/21)

Features

- 新增：身份验证成功，添加登录信息提示和预警提示
- 新增：批量删除用户二次身份验证
- 新增：操作 loading 效果
- 新增：密码弱口令提示
- 新增：账号名敏感限制
- 新增：忘记密码功能
- 新增：页面按钮权限
- 新增：机构树缓存
- 新增：添加统一认证登录页面地址配置项
- 新增：缓存加密（与低版本微应用代码不兼容）
- 新增：机构相关树修改VTree组件、远程加载机构数据
- 新增：通知公告模块
- 新增：账号注册、头像
- 新增：微应用导入、导出、启用、禁用
- 新增：导入文件修改请求header
- 新增：菜单页面过滤已禁用的微应用
- 新增：个人信息页面添加头像、我的租户、我的项目
- 新增：账号管理授权管理添加待失效状态、默认排序
- 优化：微前端路由传参、路由同步、页面缓存修改成include
- 优化：修改系统图标
- 优化：依赖整体升级（Vue3.5.5 Vite4.5.5，Vite禁止升级到5.0以上，否则局部热加载失效）
- 优化：登录页补充加载项目配置，避免被清除后无法读取配置的问题
- 优化：员工手机号校验添加号段
- 优化：顶部标签页操作优化
- 优化：BasicForm组件el-radio警告
- 优化：加密传输方式
- 优化：字典、菜单限制只有同节点下可以排序
- 优化：vite中配置splitVendorChunkPlugin、cssCodeSplit进行性能优化，

Bugs

- 修复：修复列表组件开启缓存拖拽列顺序后，记录的列变动在打开设置后被重置的问题
- 修复：修复平台账号工作台租户项目相关接口报无权限
- 修复：修复账号只授权租户菜单或者项目菜单，按钮不显示问题
- 修复：修复升级element-plus V2.8.0后账号页面选择员工、角色和岗位，弹窗不关闭问题

## v2.5.1(2024/11/28)

Features

- 新增：工作台自定拖拽布局
- 新增：接口权限、按钮权限、数据权限
- 优化：员工敏感信息加密
- 优化：顶部标签页、登录页、表格样式

Bugs

- 修复：修复编辑账号、角色授权等操作更新缓存权限数据
- 修复：修复菜单被禁用后新增下级菜单
- 修复：修复机构导入后数据不刷新的问题

## v2.5.0.4 (2024/05/28)

Features

- 新增：身份验证成功，添加登录信息提示和预警提示
- 新增：批量删除用户二次身份验证
- 新增：操作 loading 效果
- 新增：密码弱口令提示
- 新增：账号名敏感限制
- 新增：忘记密码功能
- 新增：页面按钮权限
- 新增：机构树缓存

## v2.5.0.3 (2024/3/13)

Features

- 优化：完善 mock 数据，解决 mock 模式初始化报错的问题

- 优化：提取个人信息页面的手机号校验接口到公共，解决删除 upms 目录后报错的问题

- 优化：导出接口添加 isTemplate、targetId 参数

## v2.5.0 (2023/9/28)

Features

- 新增：使用 cpit-cpvf/tree 组件，Splitpanes 组件，添加 src\views\upms\pages\dept-tree 文件，src\hooks\upms\deptTree\useZtree.ts 文件

- 新增：添加表格导入导出功能

- 优化：搜索组件新增隐藏操作按钮功能，新增隐藏 label 功能，新增 placeholder 属性

- 优化：表格组件，表头和数据列样式，新增返回数据索引属性

- 优化：baseTable 组件添加复选事件 @selectionChange="handleCurrentChange"，返回值为对象数组,新增表头数据 selectable 属性，selectable 为函数，返回值为布尔值

- 优化：basicSearch 组件新增类型 input,select 新增 clearable 属性， componentProps.clearable

- 优化：页面，操作优化

- 优化：主应用添加消息，通知子应用同步 userinfo 信息
- 新增：工程配置，projectSettings.settingConfig:
  - SettingsEnum.CACHE 优先从缓存读取项目配置，
  - SettingsEnum.FILE 优先从配置文件读取，该模式下前端以配置文件为准，无法切换 主题、中英文等等，刷新后恢复为从文件读取配置
- 新增：短信验证功能，projectSettings.loginMsg 设置为 true 时前端可以使用短信验证码进行登录
- 优化：登录页登录模式进行组件化优化

## v2.4.4 (2023/7/21)

Features

- 优化：Tab 页上的刷新功能，由刷新整体优化为刷新当前页面
- 新增：添加 dom 操作 utils，添加 dom 监听大小变化组件，添加水印组件

Bugs

- 修复：路由 keepAlive 后，跳转不刷新的 bug
- 修复：微前端多个子应用激活时，事件都被监听的问题（事件添加 microname 标识）
- 修复：子应用 token 过期后退出登录，触发主应用退出登录操作

## v2.4.3 (2023/06/08)

Features

- vite.config.ts 压缩功能添加 50K 以上压缩配置；添加打包分割，将 element-plus/icons-vue 和 mockjs 进行独立打包，提升前端性能
- nginx.conf 添加 gzip 压缩配置，提升前端性能
- 优化：去掉租户、应用、字典等页面的标题、解决样式不统一的问题
- 新增：菜单列表页面
- 新增：租户和应用列表添加检索和查看详情
- 新增：个人信息页面
- 新增：角色检索、角色导出
- 优化：BasicForm 为 单选、多选 添加 onChange 事件
- 新增：2.4.3 低代码模板添加业务代码生成模块
- 优化：多个相同的消息弹窗进行合并

Bugs

- 修复：应用和租户下，新增菜单时报错、菜单的 meta 信息保存异常的问题
- 修复：纯前端菜单异常的问题
- 修复：登录后，字典返回较慢时，租户无法自动选择
- 修复：菜单管理 切换 资源类型 时，权限编码处于 disabled 状态
- 修复：BasicSearch 的 labelWidth 未生效的问题
- 修复：面包屑点击后 404（由于层级没有真实路径，删除跳转逻辑）
- 修复：Tab 页携带 query 参数时，切换 tab 出现参数丢失的问题
- 修复：首次登录，header 中的内容默认选择后，未刷新菜单、租户、用户等信息

## v2.4.2 (2023/04/25)

Features

- 新增：角色批量绑定用户
- 优化：BasicTable 添加多选、序号列功能
- 优化：新增查看、修改用户信息，修改密码迁移到 sys 下

Bugs

- 修复：登录页背景图片循环加载的问题
- 修复：BasicForm 修改 required: true 时的提示
- 修复：多个微应用切换存在跳转首页的问题
- 修复：Tab 页切换报错的问题

## v2.4.2-rc3 (2023/04/18)

Features

- 用户列表、用户管理、机构列表和机构管理页面的新需求开发
- ElementPlus 版本从 2.2.13 升级到 2.3.3(统计组件、Text 组件)

## v2.4.2-rc2 (2023/04/12)

- 修复: 修复路由拦截器中，history 模式和 hash 模式跳转不一致的问题
- 优化：BasicTable 组件添加字段是否初始化展示属性，添加分页的 layout 属性，同时添加对 el-pagination 所有属性支持的能力
- 修复：编译后 tab 页切换发生错误
- 修复：切换租户、应用、机构等相关功能

## v2.4.2-rc1 (2023/03/28)(带微前端版本)

- 修复: BasicSearch 组件 SelectMultiple 不是多选的问题，添加 el-tree-select 的 placeholder 功能，添加 defaultValue 默认值
- 优化: BasicForm 组件添加 span，字段可以实现多列排布，添加 defaultValue 默认值
- 优化：BasicTable 组件添加右上角操作功能，可以进行字段筛选、字段拖拽排序等功能
- 优化: 单元测试组件从 Jest 升级为 Vitest，编写单元测试
- 修复: Tab 页功能修复
- 修复：权限管理套件修复
- 修复：微前端存在刷新后打开失败的问题

## v2.4.2-beta4 (2023/03/13)

- 修复：修复租户切换发生错误的问题
- 新增：添加微前端编译环境配置，.env.mico
- 新增: VITE_MICRO=false 配置，当应用为子应用时，将微应用菜单数据处理成正常菜单
- 修改：修改 nginx.conf 和 Dockerfile，应用上线后不需要处理 nginx 配置
- 修复：微应用页面打开后，退出登录、重新登录后再次打开出现 404

## v2.4.2-beta3 (2023/03/03)

- 修复：切换租户、项目相关信息未写入缓存，造成请求数据异常
- 优化：压缩部分 png、svg 文件大小，提升登录页打开速度。未使用 imagemin 因为云桌面无法安装

## v2.4.2-beta2 (2023/02/27)

- 优化：固定 package.json 依赖版本
- 新增：添加全局下载文件功能
- 新增：统一认证功能(二维码、密码)

## v2.4.2-beta1 (2023/02/21)

- 优化：权限管理套件，后端适配 TBase 数据库时，部分字段无法使用
- 修复：scss 中的变量无法在 ts 中使用，vite 会识别.module.scss 的文件得到:export {} 内的变量
- 修复：Tab 页功能部署后有时会发生错误
- 优化：管理员登录时不应该切换租户、项目

## v2.4.1 (2023/02/07) 权限管理套件优化

- 修复：微前端跳转问题
- 修复：权限管理套件角色、租户角色、应用角色，初次进入页面请求用户菜单的问题。

## v2.4.0 (2023/01/12) 工程项目功能组件已开发完毕

- 新增：添加时间转换工具类
- 新增：开发 BasicUpload 批量上传组件
- 优化：示例工程升级基础框架，修复基础框架的问题
- 修复：权限管理套件优化

## v2.3.2 （2023/01/03)

- 新增：axios 添加对 isTransformResponse: false 的处理
- 新增：axios 添加 cancelToken 的处理(将异常响应处理为正常响应，为大文件断点续传提供功能)
- 修复：router/guard/permissionGuard.ts 124 行，对 permissionStore.setCurrentMenu(to as Menu); 添加 from.path === '/' 解决部署后 tab 页跳转与刷新出现冲突
- 优化、修复：权限管理套件
  - 1.平台管理里新增租户信息模块；
  - 2.根据菜单类型获取对应租户的按钮权限；
  - 3.优化租户下角色管理模块，新增角色按钮；
  - 4.优化首次登录获取租户和项目菜单；
