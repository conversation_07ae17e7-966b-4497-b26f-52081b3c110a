<!-- components/Iconfont.vue -->
<template>
  <i :class="`iconfont icon-${name}`" :style="iconStyle"></i>
</template>

<script setup>
  import { computed } from 'vue';

  const props = defineProps({
    name: { type: String, required: true }, // 图标名称
    size: { type: [Number, String], default: 16 }, // 图标大小
    color: { type: String }, // 图标颜色
    rotate: { type: Number }, // 旋转角度
  });

  const iconStyle = computed(() => ({
    fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
    color: props.color,
    transform: props.rotate ? `rotate(${props.rotate}deg)` : undefined,
  }));
</script>
