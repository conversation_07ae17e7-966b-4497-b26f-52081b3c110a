import { VerifyCodeEnum } from '/@/enums/appEnum';
import { useAppStore } from '/@/stores/modules/app';

export function projectConfigHook() {
  const appStore = useAppStore();

  function hasFrontCode() {
    return [VerifyCodeEnum.FRONT].includes(
      appStore.getProjectConfig.verifyCode as VerifyCodeEnum,
    );
  }

  function hasBackEndCode() {
    return [VerifyCodeEnum.BACKEND, VerifyCodeEnum.BOTH].includes(
      appStore.getProjectConfig.verifyCode as VerifyCodeEnum,
    );
  }

  return {
    hasFrontCode,
    hasBackEndCode,
  };
}
