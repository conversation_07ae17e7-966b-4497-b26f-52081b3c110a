import { computed, nextTick } from 'vue';
import { useTenantStore } from '/@/stores/modules/tenant';
import { ProjectInfo } from '/@/api/model/Tenant';
import { getHeaderInfo, setHeaderInfo } from '/@/utils/storage/tenant';
import { useUserStore } from '/@/stores/modules/user';
import { cloneDeep } from 'lodash-es';

export function useProjectChange() {
  const useTenant = useTenantStore();
  const userStore = useUserStore();
  const projects = computed(() => useTenant.getProjectInfos);
  const curProject = computed(() => useTenant.getCurProject);
  async function setCurProject(project: ProjectInfo) {
    // 修改本地缓存
    const headerInfo = getHeaderInfo();
    headerInfo.projectId = project.id;
    headerInfo.projectName = project.projectName;
    setHeaderInfo(headerInfo);
    const userInfo = cloneDeep(userStore.getUserInfo);
    userInfo.projectId = project.id;
    if (userInfo.currentDept) {
      userInfo.currentDept.id = '';
    }
    useTenant.keepProjectHeaderInfo();
    userStore.setUserInfo(userInfo);
    // 刷新用户的权限数据
    await userStore.reFreshUser();
    useTenant.setCurProject(project);
    // 获取 机构信息
    await useTenant.requestDeptInfos(userInfo);
    // 刷新租户数据
    nextTick(async () => {
      await userStore.refreshUserData();
    });
  }
  return { projects, curProject, setCurProject };
}
