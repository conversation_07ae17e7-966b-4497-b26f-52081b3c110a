<template>
  <div v-if="microUrl">
    <WujieVue
      :width="appInfo.width"
      :height="appInfo.height"
      :name="appInfo.name"
      :url="microUrl"
      :activated="activatedMount"
      :afterMount="afterMount"
      :sync="sync"
      :alive="alive"
    />
  </div>
</template>

<script setup lang="ts" name="microapp">
  import { ref, watch, computed, onMounted } from 'vue';
  import { usemicroState } from './microStore';
  import { useRouter } from 'vue-router';
  import { getAllMenuStorage } from '/@/router/tools/menuStorage';
  import { Menu } from '/@/stores/types';
  // 如果使用wujie-vue
  import WujieVue from 'wujie-vue3';
  import { getAuthToken } from '/@/utils/storage/auth';
  import { useUserStoreWithOut } from '/@/stores/modules/user';

  interface appInfoType {
    name: string;
    url: string;
    microPath: string;
    width: string;
    height: string;
  }
  const props = defineProps({
    appInfo: {
      type: Object as PropType<appInfoType>,
      default: () => {},
      required: true,
    },
    sync: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    alive: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
  });
  const emit = defineEmits(['afterMount']);
  const allMenuList = getAllMenuStorage();
  // 获取微前端的路由
  let microRouter: any = [];
  const findMicroRouter = (list: Menu[] = []) => {
    list.map((item: Menu) => {
      if (item.type === '2' && item?.meta?.microName == props.appInfo.name) {
        microRouter.push({ ...item, ...{ path: item.meta.microPath, type: '1' } });
      }
      if (item.children && item.children.length > 0) {
        findMicroRouter(item.children);
      }
    });
  };
  const userStore = useUserStoreWithOut();
  const { bus } = WujieVue;
  // 主应用监听事件
  const microOnMessage = () => {
    bus.$on('onMicroMessage-' + props.appInfo.name, function (data) {
      console.log('接收到子应用' + props.appInfo.name + '的数据:', data);
      microState.setmicroState(data);
      if (microState.getAppList.indexOf(props.appInfo.name) === -1) {
        microState.setAppList(props.appInfo.name);
      }
      if (data?.type == 'beforeLogin') {
        const token = getAuthToken();
        bus.$emit('sendMicroMessage-' + props.appInfo.name, {
          message: { username: 'wuxiaofan', password: '000000', token },
          type: 'login',
        });
      } else if (data?.type == 'needRouter') {
        microRouter = [];
        findMicroRouter(allMenuList);
        bus.$emit('sendMicroMessage-' + props.appInfo.name, {
          message: { routerList: microRouter },
          type: 'routerList',
        });
      } else if (data?.type == '401') {
        userStore.logout(401);
      } else if (data?.type == 'addTabs') {
        // 解决微应用中的隐藏菜单，在主应用的tab中不显示的问题
        let result: Menu = {
          path: '',
          id: undefined,
          name: '',
        }; // 运行结果
        function getTreeItem(data, id) {
          data.map((item) => {
            if (item.meta.microPath == id) {
              result = item; // 结果赋值
            } else {
              if (item.children) {
                getTreeItem(item.children, id);
              }
            }
          });
        }
        getTreeItem(allMenuList, data?.message?.path);
        route.push(result.path);
      } else {
        appObjectCreated.value[props.appInfo.name] = true;
        // emit('afterMount', {appInfo: appObjectCreated.value});
      }
    });
  };
  // 微应用创建完成
  const activatedMount = () => {
    if (microState.getAppList.indexOf(props.appInfo.name) > -1) {
      appObjectCreated.value[props.appInfo.name] = true;
      // emit('afterMount', {appInfo: appObjectCreated.value});
    }
  };
  const afterMount = () => {
    emit('afterMount', { appInfo: props.appInfo });
  };
  const route = useRouter();
  // const appInfo = ref<appInfoType>({ name: '', url: '', microPath:  '' });
  const microAppData = ref({
    message: {
      path: '',
    },
    type: 'router',
  });
  // 主应用控制子应用路由跳转
  const microPath = ref('');
  // 保存子应用通信的数据
  const microState = usemicroState();
  const setRouter = (type, message) => {
    microState.setActiveApp(props.appInfo.name);
    microAppData.value = {
      message: { ...message },
      type: type, //'router'
    };
    bus.$emit('sendMicroMessage-' + props.appInfo.name, microAppData.value);
  };
  const appObjectCreated = ref({});
  const needSend = ref({});
  onMounted(() => {
    microOnMessage();
    if (microState.getActiveApp && microState.getActiveApp != props.appInfo.name) {
      // 解决保活模式，切换微应用后，会缓存当前微应用最后一个页面的问题。修改为，微应用切换前，跳转到微应用的空白页
      // appChange.value = true;
      bus.$emit('sendMicroMessage-' + microState.getActiveApp, {
        message: { path: '/blank/nopage' },
        type: 'router',
      });
    }
    const path = props.appInfo?.microPath;
    if (path) {
      microPath.value = path + '';
      if (appObjectCreated.value[props.appInfo.name]) {
        setRouter('router', { path });
      } else {
        needSend.value[props.appInfo.name] = true;
      }
    }
  });
  watch(
    () => appObjectCreated.value[props.appInfo.name],
    (val) => {
      if (val && needSend.value[props.appInfo.name] && microPath.value) {
        needSend.value[props.appInfo.name] = false;
        microPath.value && setRouter('router', { path: microPath.value });
      }
    },
  );
  const microUrl = computed(() => {
    return props.appInfo.url;
  });
</script>
