<template>
  <div class="knowledge-table-container">
    <!-- 表格顶部操作区 -->
    <div class="table-header">
      <div class="search-area">
        <el-input
          v-model="searchKeyword"
          :placeholder="config.searchPlaceholder"
          clearable
          prefix-icon="Search"
        />
      </div>
      <div class="action-area">
        <el-dropdown @command="handleBatchCommand" trigger="click">
          <el-button type="primary" plain>
            批量操作
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="delete" :disabled="!hasSelected">批量删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
          <!-- 批量同步 -->
          
        </el-dropdown>
        <el-button type="primary" @click="handleAdd" :disabled="tableData.some(row => row.isEdit)">
          <el-icon><plus /></el-icon>新增
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="  tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <!-- 动态列渲染 -->
      <template v-for="column in config.columns" :key="column.prop">
        <!-- 普通文本列 -->
        <el-table-column 
          v-if="!column.type || column.type === 'text'"
          :prop="column.prop" 
          :label="column.label" 
          :min-width="column.minWidth || 150"
          :width="column.width"
        >
          <template #default="{ row }">
            <el-input 
              v-if="row.isEdit && column.editable" 
              v-model="row[column.prop]" 
              :placeholder="column.placeholder" 
            />
            <span v-else>{{ row[column.prop] }}</span>
          </template>
        </el-table-column>
        
        <!-- 文本域列 -->
        <el-table-column 
          v-else-if="column.type === 'textarea'"
          :prop="column.prop" 
          :label="column.label" 
          :min-width="column.minWidth || 250"
          :width="column.width"
        >
          <template #default="{ row }">
            <el-input 
              v-if="row.isEdit && column.editable" 
              v-model="row[column.prop]" 
              :placeholder="column.placeholder"
            />
            <span v-else>{{ row[column.prop] }}</span>
          </template>
        </el-table-column>
      </template>
      
      <!-- 数据源列 -->
      <el-table-column prop="dataSources" label="绑定数据源" min-width="240">
        <template #default="{ row }">
          <div v-loading="row.loading">
            <el-cascader
              v-if="row.isEdit"
              v-model="row.cascaderValue"
              :props="row.cascaderProps"
              placeholder="选择数据源/数据库/模式"
              clearable
              @change="handleCascaderChange(row, $event)"
            />
          </div>
          
          <span v-if="!row.isEdit">
            <el-tag >{{ getDataSourceLabel(row.dataSources) }}</el-tag>
          </span>
        </template>
      </el-table-column>
      
      <!-- 数据表列 -->
      <el-table-column prop="tableName" label="数据表" width="200">
        <template #default="{ row }">
          <template v-if="row.isEdit">
            <el-select 
              :clearable="true"
              v-model="row.tableName"
              placeholder="请选择数据表"
              :disabled="!row.dataSources?.dataSourceId"
              @focus="loadTableList(row)"
              @change="handleTableChange(row)"
            >
              <el-option 
                v-for="table in row.tableList" 
                :key="table.name" 
                :label="table.name" 
                :value="table.name"
              />
            </el-select>
            <el-tooltip 
              v-if="!row.dataSources?.dataSourceId" 
              effect="dark" 
              content="请先选择数据源" 
              placement="top"
            >
            </el-tooltip>
          </template>
          <span v-else>
            <el-tag v-if="row.tableName">{{ row.tableName || '' }}</el-tag>
          </span>
        </template>
      </el-table-column>
      
      <!-- 操作列 -->
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <div v-if="row.isEdit" class="operation-buttons">
            <el-button type="primary" link @click="handleSave(row)">保存</el-button>
            <el-button type="danger" link @click="handleCancel(row)">取消</el-button>
          </div>
          <div v-else class="operation-buttons">
            <el-button type="primary" link @click="handleEdit(row)" :disabled="tableData.some(r => r.isEdit)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search, Plus, ArrowDown } from '@element-plus/icons-vue'
import { useKnowledgeTable, type KnowledgeTableConfig } from '../composables/useKnowledgeTable'

/**
 * 组件属性接口
 */
interface Props {
  config: KnowledgeTableConfig
}

// 接收props
const props = defineProps<Props>()

// 使用通用表格逻辑
const {
  // 响应式数据
  tableData,
  loading,
  searchKeyword,
  selectedRows,
  currentPage,
  pageSize,
  totalItems,
  dataSourceOptions,
  
  // 计算属性
  hasSelected,
  
  // 方法
  loadTableData,
  handleSearch,
  handleSelectionChange,
  handleAdd,
  handleEdit,
  handleSave,
  handleCancel,
  handleDelete,
  handleBatchDelete,
  handleCurrentChange,
  handleSizeChange,
  handleDataSourceChange,
  loadTableList,
  handleTableChange,
  getDataSourceLabel,
  getCascaderValue,
  handleCascaderChange,
  handleBatchCommand
} = useKnowledgeTable(props.config)
</script>

<style scoped lang="scss">
.knowledge-table-container {
  width: 100%;
  margin-top: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-area {
  width: 250px;
}

.action-area {
  display: flex;
  gap: 12px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>

<style>
:root {
  --el-loading-spinner-size: 28px;
}
</style>