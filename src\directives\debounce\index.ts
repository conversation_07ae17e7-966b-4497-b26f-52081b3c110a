import { Directive } from 'vue';
import type { DirectiveBinding } from 'vue';
import { useDebounceFn } from '@vueuse/core';

export const debounce: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding<(value: string) => void>) {
    // 从 binding 中获取防抖时间和回调函数
    const { value: callback, arg: wait = 300 } = binding;

    if (typeof callback !== 'function') {
      console.warn('v-debounce 指令的值必须是一个函数');
      return;
    }

    // 使用 useDebounceFn 创建防抖函数
    const debouncedFn = useDebounceFn((event: Event) => {
      const target = event.target as HTMLInputElement;
      callback(target.value); // 直接传递 value 给回调函数
    }, Number(wait)); // 将 arg 转换为数字

    // 监听 input 事件，并调用防抖函数
    el.addEventListener('input', debouncedFn);

    // 将防抖函数保存到 WeakMap 中
    debounceMap.set(el, debouncedFn);
  },
  unmounted(el: HTMLElement) {
    // 从 WeakMap 中获取防抖函数并移除监听器
    const debouncedFn = debounceMap.get(el);
    if (debouncedFn) {
      el.removeEventListener('input', debouncedFn);
      debounceMap.delete(el);
    }
  },
};

// 使用 WeakMap 存储防抖函数
const debounceMap = new WeakMap<HTMLElement, (event: Event) => void>();

export function setupDebounceDirective(app: any) {
  app.directive('debounce', debounce);
}
