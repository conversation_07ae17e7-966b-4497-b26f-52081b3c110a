<template>
  <div>
    <div class="login-container">
      <p class="sys-logo"><img :src="logo" alt="logo" /></p>
      <div class="page-center">
        <div class="page-left"><img :src="logoLeft" alt="登录插图" /></div>
        <div class="login-box">
          <div class="login-form">
            <!--          <div class="sys-title">欢迎回来</div>-->
            <p class="sys-title" v-motion :initial="initial()" :enter="enter(100)">
              {{ VITE_APP_TITLE }}
            </p>
            <el-tabs
              v-model="loginType"
              :class="isUseLogin ? 'login-type-tabs' : ''"
              stretch
              @tab-click="changeLogin"
            >
              <el-tab-pane
                v-if="appStore.projectConfig?.loginCode"
                label="扫码登录"
                name="first"
                v-loading="codeLoading"
              >
                <div class="h-86">
                  <qrcode-vue
                    :loading="codeLoading"
                    :size="220"
                    :value="JSON.stringify(qrCodeData)"
                    class="login-code"
                  />
                  <span class="login-code-span">请使用邮我行APP进行扫码登录</span>
                </div>
              </el-tab-pane>
              <el-tab-pane
                v-if="appStore.projectConfig?.loginMsg"
                label="短信登录"
                name="msg"
              >
                <div class="h-86">
                  <div
                    v-motion
                    :initial="{
                      opacity: 0,
                      y: 100,
                    }"
                    :enter="{
                      opacity: 1,
                      y: 0,
                      transition: {
                        delay: 200,
                      },
                    }"
                  >
                    <div>
                      <el-input
                        type="text"
                        class="loign-input mb-4"
                        v-model="phoneNumber"
                        :prefix-icon="Message"
                        :placeholder="transformI18n(i18nType + 'phone', useI18n)"
                      />
                    </div>
                  </div>
                  <div
                    class="focus"
                    v-motion
                    :initial="{
                      opacity: 0,
                      y: 100,
                    }"
                    :enter="{
                      opacity: 1,
                      y: 0,
                      transition: {
                        delay: 300,
                      },
                    }"
                  >
                    <div>
                      <el-input
                        class="loign-input mb-4"
                        v-model="verifyCode"
                        :prefix-icon="ChatSquare"
                        :placeholder="transformI18n(i18nType + 'verifyCode', useI18n)"
                      >
                        <template #suffix>
                          <el-button type="primary" link @click.stop="getPhoneCode">{{
                            isActive ? `${60 - counterPhone}S后重新获取` : '获取验证码'
                          }}</el-button>
                        </template>
                      </el-input>
                    </div>
                  </div>
                  <div class="set-btn cursor-pointer" @click="onLogin">
                    <el-button
                      type="primary"
                      class="btn"
                      :disabled="!(phoneNumber && verifyCode)"
                      :loading="loading"
                      v-motion
                      :initial="{
                        opacity: 0,
                        y: 10,
                      }"
                      :enter="{
                        opacity: 1,
                        y: 0,
                        transition: {
                          delay: 400,
                        },
                      }"
                    >
                      {{ transformI18n(i18nType + 'hslogin', useI18n) }}
                    </el-button>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="账户密码" name="second">
                <div class="h-86">
                  <div
                    v-motion
                    :initial="{
                      opacity: 0,
                      y: 100,
                    }"
                    :enter="{
                      opacity: 1,
                      y: 0,
                      transition: {
                        delay: 200,
                      },
                    }"
                  >
                    <div>
                      <el-input
                        type="text"
                        class="loign-input mb-4"
                        v-model="user"
                        :prefix-icon="UserIcon"
                        :placeholder="transformI18n(i18nType + 'username', useI18n)"
                      />
                    </div>
                  </div>
                  <div
                    class="focus"
                    v-motion
                    :initial="{
                      opacity: 0,
                      y: 100,
                    }"
                    :enter="{
                      opacity: 1,
                      y: 0,
                      transition: {
                        delay: 300,
                      },
                    }"
                  >
                    <div>
                      <el-input
                        type="password"
                        class="loign-input mb-4"
                        show-password
                        v-model="pwd"
                        :prefix-icon="Lock"
                        :placeholder="transformI18n(i18nType + 'password', useI18n)"
                      />
                    </div>
                  </div>
                  <div
                    class="mb-4"
                    v-if="hasBackEndCode()"
                    v-motion
                    :initial="{
                      opacity: 0,
                      y: 100,
                    }"
                    :enter="{
                      opacity: 1,
                      y: 0,
                      transition: {
                        delay: 200,
                      },
                    }"
                  >
                    <div class="flex">
                      <el-input
                        class="loign-input"
                        v-model="verifyCode"
                        :prefix-icon="EditPen"
                        :placeholder="transformI18n(i18nType + 'verifyCode', useI18n)"
                      />
                      <div
                        @click="refreshCode"
                        class="verify-code-time flex items-center absolute h-full"
                        style="right: -10px"
                      >
                        <el-image
                          :style="{
                            width: verifyCodeImageWidth + 'px',
                            height: '100%',
                          }"
                          :src="`${VITE_APP_BASE_URL_PREFIX}/sys/image/code?randomStr=${randomStr}`"
                        >
                          <template #placeholder>
                            <div class="w-full h-full flex items-center justify-center">
                              <sys-icon class="is-loading" type="Loading" />
                            </div>
                          </template>
                        </el-image>
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="hasFrontCode()"
                    ref="elVerify"
                    v-motion
                    :initial="{
                      opacity: 0,
                      y: 100,
                    }"
                    :enter="{
                      opacity: 1,
                      y: 0,
                      transition: {
                        delay: 300,
                      },
                    }"
                  >
                    <BasicDragVerify
                      :width="verifyWidth"
                      ref="el2"
                      @success="handleSuccess"
                      circle
                    />
                    <div class="text-center h-6 text-xs text-green-500"
                      ><span v-if="canLogin">{{ successText }}</span></div
                    >
                  </div>

                  <div class="set-btn cursor-pointer" @click="onLogin">
                    <el-button
                      type="primary"
                      class="btn"
                      :disabled="hasFrontCode() && !canLogin"
                      :loading="loading"
                      v-motion
                      :initial="{
                        opacity: 0,
                        y: 10,
                      }"
                      :enter="{
                        opacity: 1,
                        y: 0,
                        transition: {
                          delay: 400,
                        },
                      }"
                    >
                      {{ transformI18n(i18nType + 'hslogin', useI18n) }}
                    </el-button>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
      <!-- <p class="fix-foot">Copyright © 2022-2033 中国邮政版权所有</p> -->
    </div>
  </div>
</template>

<script setup lang="ts" name="Login">
  import { loadEnv, warpperEnv } from '@build/index';
  import { useUserStore } from '/@/stores/modules/user';
  import { transformI18n } from '/@/utils/i18n';
  import { encryptedSM4 } from '/@/utils/cipher';
  import { ref, watch, onUnmounted } from 'vue';
  import SysIcon from '/@/components/sys/Icon';
  import logo from '/@/assets/images/login/logo-upms.svg';
  import logoLeft from '/@/assets/images/login/logo-left.png';
  import {
    BasicDragVerify,
    DragVerifyActionType,
    PassingData,
  } from '/@/components/sys/Verify';
  import { useInterval, useResizeObserver } from '@vueuse/core';
  import { projectConfigHook } from '/@/hooks/settings/projectConfig';
  import QrcodeVue from 'qrcode.vue';
  import { getLoginCode, getCrtLogin, getMsgCode } from '/@/api/sys';
  import { useAppStoreWithOut } from '/@/stores/modules/app';
  import {
    User as UserIcon,
    Lock,
    EditPen,
    Message,
    ChatSquare,
  } from '@element-plus/icons-vue';
  import { loginPhoneReg } from '/@/enums/Regs/regs';
  import { useMessage } from '/@/hooks/web/useMesage';
  import { PhoneLoginParams } from '/@/api/model/User';
  import { useAnimateData } from '/@/views/shares/components/login';
  // import { getFID } from 'web-vitals';
  // getFID(console.log);

  const { initial, enter } = useAnimateData();

  const appStore = useAppStoreWithOut();
  const { VITE_APP_BASE_URL_PREFIX } = warpperEnv(loadEnv());
  interface Body {
    userName: string;
    userPasswd: string;
    randomStr?: number;
    code?: string;
    type?: number;
  }

  const { counter, resume } = useInterval(200, { controls: true });
  const refreshTime = 60; // 60s 刷新一次
  const {
    counter: counterPhone,
    pause: pausePhone,
    resume: resumPhone,
    isActive,
  } = useInterval(1000, { controls: true, immediate: false }); // 60s 刷新一次

  watch(
    () => counterPhone.value,
    (value) => {
      if (isActive && value > 60) {
        pausePhone();
        counterPhone.value = 0;
      }
    },
  );

  /**
   * 获取短信验证码
   */
  function getPhoneCode() {
    if (!loginPhoneReg.test(phoneNumber.value)) {
      const { createMessage } = useMessage();
      createMessage.error('请输入正确的手机号');
      return;
    }
    getMsgCode(encryptedSM4(phoneNumber.value)).then(() => {
      resumPhone();
    });
  }

  const { hasFrontCode, hasBackEndCode } = projectConfigHook();

  // 拖拽验证码
  const el2 = ref<Nullable<DragVerifyActionType>>(null);
  const elVerify = ref<Nullable<HTMLInputElement>>(null);
  const verifyCodeImageWidth = ref(80);
  const verifyCodeImageBorderWidth = ref(80);
  // 扫码登录
  const loginType = ref(
    appStore.projectConfig?.loginCode
      ? 'first'
      : appStore.projectConfig?.loginMsg
      ? 'msg'
      : 'second',
  );
  // 不启用统一认证
  const isUseLogin = ref(!appStore.projectConfig?.loginType);

  const codeLoading = ref(true);
  const qrCodeData = ref({
    businessData: '',
    businessType: 'login',
    systemID: '10011',
    toPage: '',
    codeId: '',
  });
  const timer = ref<NodeJS.Timeout | number>();
  // 扫码登录时候成功标识
  const isSuccess = ref(false);
  changeLogin(loginType.value == 'first' ? { index: 0 } : { index: 1 });

  function changeLogin(tab) {
    if (!appStore.projectConfig?.loginType) return;
    if (timer.value) {
      clearInterval(timer.value);
    }
    if (tab.index == 0) {
      codeLoading.value = true;
      getLoginCode().then((data) => {
        qrCodeData.value = data;
        codeLoading.value = false;
        timer.value = setInterval(async () => {
          isSuccess.value = await getCrtLogin({ codeId: qrCodeData.value.codeId });
          if (isSuccess.value) {
            clearInterval(timer.value);
            userStore.crtlogin(isSuccess.value);
          }
        }, 3000);
      });
    }
  }

  onUnmounted(() => {
    if (timer.value) {
      clearInterval(timer.value);
    }
  });

  watch(
    () => counter.value,
    (value) => {
      if (value <= refreshTime * 5) {
        verifyCodeImageBorderWidth.value =
          verifyCodeImageWidth.value -
          (verifyCodeImageWidth.value / refreshTime / 5) * value;
      } else {
        resume();
        refreshCode();
        changeLogin(loginType.value == 'first' ? { index: 0 } : { index: 1 });
      }
    },
  );
  const verifyWidth = ref(200);

  const phoneNumber = ref('');
  const verifyCode = ref('');
  const successText = ref('');
  const canLogin = ref(false);
  const randomStr = ref(new Date().getTime());

  function refreshCode() {
    counter.value = 0;
    randomStr.value = new Date().getTime();
  }

  useResizeObserver(elVerify, (entries) => {
    const entry = entries[0];
    const { width } = entry.contentRect;
    verifyWidth.value = width;
    // text.value = `width: ${width}, height: ${height}`;
  });

  function handleSuccess(data: PassingData) {
    const { time } = data;
    successText.value = `校验成功 耗时${time}秒`;
    canLogin.value = true;
  }

  const { VITE_APP_TITLE } = warpperEnv();
  const userStore = useUserStore();
  const loading = ref<boolean>(false);
  let user = ref('');
  let pwd = ref('');
  // const randomStr = ref(new Date().getTime());
  const i18nType = 'buttons.';
  const useI18n = true;
  const onLogin = async (): Promise<void> => {
    if (loginType.value === 'msg') {
      // 短信登录
      const params: PhoneLoginParams = {
        phone: phoneNumber.value,
        code: verifyCode.value,
      };
      loading.value = true;
      userStore.login(params).finally(() => {
        loading.value = false;
        canLogin.value = false;
      });
      return;
    }
    if (hasFrontCode() && !canLogin.value) {
      return;
    }
    const body: Body = {
      userName: user.value,
      userPasswd: encryptedSM4(pwd.value),
      type: appStore.projectConfig?.loginType,
    };
    if (hasBackEndCode()) {
      // 如果是后端验证码，需要传递相关数据
      body.randomStr = randomStr.value;
      body.code = verifyCode.value;
    }
    loading.value = true;
    userStore.login(body).finally(() => {
      loading.value = false;
      // 刷新验证码
      refreshCode();
      el2.value?.resume();
      canLogin.value = false;
    });
  };

  defineExpose({ UserIcon, hasBackEndCode });
</script>

<style scoped lang="scss">
  @import url('/src/styles/login/login.css');
  .page-center {
    width: 1180px;
    height: calc(100vh - 110px);
    display: flex;
    /* margin: calc((100vh - 520px) / 2 - 50px) auto calc((100vh - 520px) / 2); */
    margin: 10px auto 55px;
    background-color: #ffffff;
    border-radius: 16px;
    .page-left {
      width: 720px;
      height: 100%;
      background-color: #f6fffe;
      border-radius: 16px;
      img {
        display: inline-block;
        width: 100%;
        height: 100%;
      }
    }
  }
  .fix-foot {
    width: 100%;
    text-align: center;
    position: fixed;
    bottom: 20px;
    font-size: 12px;
    color: #000;
    letter-spacing: 1.66px;
    line-height: 12px;
    font-weight: 400;
  }
  .verify-code {
    width: 25px;
    margin-left: -3px;
  }
  .verify-code-time:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 5px;
    right: 0;
    height: 2px;
    width: v-bind("verifyCodeImageBorderWidth + 'px'");
    background-color: #0d9e97;
  }
  .login-code {
    margin: 30px 40px;
  }
  .login-code-span {
    display: inline-block;
    width: 100%;
    text-align: center;
    color: #409eff;
  }
  .loign-input {
    height: 40px;
  }
  .login-type-tabs :deep(.el-tabs__item) {
    display: none;
  }
</style>
<route lang="yaml">
name: Login
meta:
  layout: BlankLayout
</route>
