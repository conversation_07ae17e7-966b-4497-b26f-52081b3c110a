import {
  PROJECT_SETTINGS,
  TOKEN_KEY,
  USER_INFO_KEY,
  MENU_KEY,
  HEADER_TENANT_PROJECT_KEY,
  DICT_KEY,
  TAB_PAGES_KEY,
  MICRO_DATA,
  HEADER_TENANT_REQUESTED,
  TABLE_LOCAL,
  ALL_MENU_KEY,
  THEME,
  PASS_WORD_STATUS,
  WEB_MODEL,
} from '/@/enums/cacheEnum';
import { ResDict, UserInfo } from '/#/store';
import { Menu } from '/@/stores/types';

interface BasicStore {
  [TOKEN_KEY]: string | number | null | undefined;
  [THEME]: ThemeColorObject;
  [MENU_KEY]: Menu[];
  [ALL_MENU_KEY]: Menu[];
  [USER_INFO_KEY]: UserInfo;
  [PROJECT_SETTINGS]: ProjectConfigs;
  [HEADER_TENANT_PROJECT_KEY]: string;
  [DICT_KEY]: ResDict;
  [TAB_PAGES_KEY]: Menu[];
  [MICRO_DATA]: Menu[];
  [HEADER_TENANT_REQUESTED]: boolean;
  [TABLE_LOCAL]: string[];
  [PASS_WORD_STATUS]: boolean;
  [WEB_MODEL]: string;
}

// 限定BasicKeys的类型为BasicStore的key TOKEN_KEY | USER_INFO_KEY
export type BasicKeys = keyof BasicStore;
