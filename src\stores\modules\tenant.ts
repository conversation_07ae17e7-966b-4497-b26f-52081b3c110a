import { defineStore } from 'pinia';
import { store } from '/@/stores';
import { DeptInfo, ProjectInfo, TenantInfo } from '/@/api/model/Tenant';
import {
  getProjectList,
  getTenantList,
  getUserProjectDeptList,
} from '/@/api/sys/tenant';
import {
  getHeaderInfo,
  getHeaderRequested,
  setHeaderInfo,
  setHeaderRequested,
} from '/@/utils/storage/tenant';
import { UserInfo, HeaderTenantProject } from '/#/store';
import { useUserStore } from '/@/stores/modules/user';
import { useDictStore } from '/@/stores/modules/dict';
import { getWebModel } from '/@/utils/storage/auth';
export interface TenantState {
  deptInfos: DeptInfo[];
  projectInfos: ProjectInfo[];
  tenantInfos: TenantInfo[];
  curDept: DeptInfo;
  curTenant: TenantInfo;
  curProject: ProjectInfo;
}

export const useTenantStore = defineStore({
  id: 'app-tenant',
  state: (): TenantState => ({
    projectInfos: [],
    tenantInfos: [],
    deptInfos: [],
    curTenant: { id: '', tenantName: '', tenantAlias: '' },
    curProject: { id: '', projectName: '', projectAlias: '' },
    curDept: { id: '', deptName: '', deptAlias: '' },
  }),
  getters: {
    getDeptInfos(): DeptInfo[] {
      return this.deptInfos;
    },
    getProjectInfos(): ProjectInfo[] {
      return this.projectInfos;
    },
    getTenantInfos(): TenantInfo[] {
      return this.tenantInfos;
    },
    getCurTenant(): TenantInfo {
      const storageHeaderTenant: TenantInfo = {} as TenantInfo;
      storageHeaderTenant.id = getHeaderInfo()?.tenantId || '';
      storageHeaderTenant.tenantName = getHeaderInfo()?.tenantName || '';
      return this.curTenant.id ? this.curTenant : storageHeaderTenant;
    },
    getCurProject(): ProjectInfo {
      const storageHeaderProject: ProjectInfo = {} as ProjectInfo;
      storageHeaderProject.id = getHeaderInfo()?.projectId || '';
      storageHeaderProject.projectName = getHeaderInfo()?.projectName || '';
      return this.curProject;
    },
    getCurDept(): DeptInfo {
      const storageHeaderDept: DeptInfo = {} as DeptInfo;
      storageHeaderDept.id = getHeaderInfo()?.deptId || '';
      storageHeaderDept.deptName = getHeaderInfo()?.deptName || '';
      return this.curDept.id ? this.curDept : storageHeaderDept;
    },
    getHeaderInfoStore() {
      return getHeaderInfo();
    },
    getHeaderRequested() {
      return getHeaderRequested();
    },
  },
  actions: {
    setHeaderRequested(status = true) {
      setHeaderRequested(status);
    },
    setCurTenant(tenantInfo: TenantInfo) {
      const userStore = useUserStore();
      this.curTenant = tenantInfo;
      const headerInfo = getHeaderInfo() || {};
      headerInfo.tenantId = this.curTenant?.id;
      headerInfo.tenantName = this.curTenant?.tenantName;
      setHeaderInfo(headerInfo);
      const userInfo = userStore.getUserInfo;
      if (userInfo) {
        userInfo.tenantId = this.curTenant?.id;
      }
      userStore.setUserInfo(userInfo);
    },
    setCurProject(projectInfo: ProjectInfo) {
      const userStore = useUserStore();
      this.curProject = projectInfo;
      const headerInfo = getHeaderInfo() || {};
      headerInfo.projectId = this.curProject.id;
      headerInfo.projectName = this.curProject.projectName;
      setHeaderInfo(headerInfo);
      const userInfo = userStore.getUserInfo;
      if (userInfo) {
        userInfo.projectId = this.curProject?.id;
      }
      userStore.setUserInfo(userInfo);
    },
    setCurDept(deptInfo: DeptInfo) {
      const userStore = useUserStore();
      this.curDept = deptInfo;
      const headerInfo = getHeaderInfo() || {};
      headerInfo.deptId = this.curDept.id;
      headerInfo.deptName = this.curDept.deptName;
      setHeaderInfo(headerInfo);
      const userInfo = userStore.getUserInfo;
      if (userInfo) {
        userInfo.deptId = this.curDept?.id;
      }
      userStore.setUserInfo(userInfo);
    },
    setTenantList(tenantList: TenantInfo[]) {
      this.tenantInfos = tenantList;
    },
    setProjectList(projectList: ProjectInfo[]) {
      this.projectInfos = projectList;
    },
    setDeptList(deptInfos: DeptInfo[]) {
      this.deptInfos = deptInfos;
    },
    setHeaderInfoStorage(info: HeaderTenantProject) {
      setHeaderInfo(info);
    },
    keepTenantHeaderInfo() {
      const headerInfo = getHeaderInfo();
      this.setCurProject({ id: '', projectName: '' });
      this.setCurDept({ id: '', deptName: '' });
      headerInfo.projectId = '';
      headerInfo.projectName = '';
      headerInfo.deptId = '';
      headerInfo.deptName = '';
      this.setHeaderInfoStorage(headerInfo);
    },
    keepProjectHeaderInfo() {
      const headerInfo = getHeaderInfo();
      this.setCurDept({ id: '', deptName: '' });
      headerInfo.deptId = '';
      headerInfo.deptName = '';
      this.setHeaderInfoStorage(headerInfo);
    },
    /**
     * 获取用户的 租户 项目 机构信息
     * @param userInfo
     */
    async requestData(userInfo: UserInfo) {
      // 首次进入系统 清除用户的 租户、应用、机构 列表
      this.setTenantList([]);
      this.setProjectList([]);
      this.setDeptList([]);
      const useDict = useDictStore();
      const sysHeader = useDict.get('sys_header');
      if (
        sysHeader?.find(
          (item) => item.code === 'sys_header_tenant' && item.value === '1',
        )
      ) {
        await this.requestTenantInfos(userInfo);
      }
      if (
        sysHeader?.find(
          (item) => item.code === 'sys_header_project' && item.value === '1',
        )
      ) {
        await this.requestProjectInfos(userInfo);
      }
      if (
        sysHeader?.find((item) => item.code === 'sys_header_dept' && item.value === '1')
      ) {
        await this.requestDeptInfos(userInfo);
      }
    },
    async requestTenantInfos(userInfo: UserInfo) {
      const res = await getTenantList();
      console.log('设置租户数据', res)
      this.setTenantList(res);
      if (res.length > 0) {
        const tenantInfo = res.find((item) => item.id === userInfo.tenantId);
        if (tenantInfo) {
          this.setCurTenant(tenantInfo);
        } else {
          this.setCurTenant({
            id: '',
            tenantName: '',
            tenantAlias: '',
          });
        }
      }
    },
    async requestProjectInfos(userInfo: Nullable<UserInfo> = null) {
      if (this.curTenant.id) {
        const initProject = {
          id: '',
          projectName: '',
          projectAlias: '',
        };
        const res = await getProjectList();
        this.setProjectList(res);
        if (res.length > 0) {
          let projectInfo: ProjectInfo | undefined;
          if (userInfo) {
            projectInfo = res.find((item) => item.id === userInfo.projectId);
          } else {
            projectInfo = res[0];
          }

          if (projectInfo) {
            this.setCurProject(projectInfo);
          } else {
            this.setCurProject(initProject);
          }
          // this.setCurProject(res[0]);
        } else {
          // 租户下不存在应用
          this.setCurProject(initProject);
        }
      }
    },
    async requestDeptInfos(userInfo: Nullable<UserInfo> = null) {
      // debugger
      const initDept = {
        id: '',
        deptName: '',
        deptAlias: '',
      };
      console.log(getWebModel());
      if (
        (this.curProject.id && getWebModel() === 'mix') ||
        getWebModel() === 'simple'
      ) {
        const res = await getUserProjectDeptList();
        this.setDeptList(res);
        if (res.length > 0) {
          let deptInfo: DeptInfo | undefined;
          if (userInfo) {
            userInfo.deptId = userInfo.currentDept?.id;
            userInfo.deptName = userInfo.currentDept?.instName;
            deptInfo = res.find((item) => item.id === userInfo.deptId);
          } else {
            deptInfo = res[0];
          }
          if (deptInfo) {
            this.setCurDept(deptInfo);
          } else {
            this.setCurDept(initDept);
          }
          // this.setCurProject(res[0]);
        } else {
          // 租户下不存在应用
          this.setCurDept(initDept);
        }
      }
    },
  },
});

// use outside the setup
export function useTenantStoreWithOut() {
  return useTenantStore(store);
}
