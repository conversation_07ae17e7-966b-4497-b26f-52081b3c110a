<template>
  <div class="h-86">
    <div v-motion :initial="initial()" :enter="enter(200)">
      <div>
        <el-input
          type="text"
          class="loign-input mb-4"
          v-model="phoneNumber"
          :placeholder="transformI18n(i18nType + 'phone', useI18n)"
        >
          <template #prepend>+86</template>
        </el-input>
      </div>
    </div>
    <div class="focus" v-motion :initial="initial()" :enter="enter()">
      <el-input
        class="loign-input mb-4 msg-input"
        v-model="verifyCode"
        :placeholder="transformI18n(i18nType + 'verifyCode', useI18n)"
      >
        <template #prepend><img class="verify" :src="verify" alt="verify" /></template>
      </el-input>
      <el-button class="identify" type="primary" link @click.stop="getPhoneCode">{{
        isActive ? `${60 - counterPhone}S后重新获取` : '获取验证码'
      }}</el-button>
    </div>
    <div
      v-if="
        slideVerifyShow &&
        (projectSettings.verifyCode === 'front' ||
          projectSettings.verifyCode === 'both')
      "
      class="slide-verify-content"
    >
      <slide-verify
        class="slide-verify"
        ref="block"
        :w="274"
        :h="150"
        :r="10"
        :l="82"
        :slider-text="text"
        :accuracy="accuracy"
        @again="onAgain"
        @success="onSuccess"
        @fail="onFail"
        @refresh="onRefresh"
        :imgs="imgs"
      />
    </div>
    <div class="set-btn cursor-pointer" @click="onLogin">
      <el-button
        type="primary"
        class="btn"
        :disabled="!(phoneNumber && verifyCode)"
        :loading="loading"
        v-motion
        :initial="initial(10)"
        :enter="enter(400)"
      >
        {{ transformI18n(i18nType + 'hslogin', useI18n) }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PropType, ref, watch, onMounted } from 'vue';
  import { Message } from '@element-plus/icons-vue';
  import { transformI18n } from '/@/utils/i18n';
  import { encryptedSM4 } from '/@/utils/cipher';
  import { loginPhoneReg } from '/@/enums/Regs/regs';
  import { useMessage } from '/@/hooks/web/useMesage';
  import { getMsgCode } from '/@/api/sys';
  import { PhoneLoginParams } from '/@/api/model/User';
  import { useInterval } from '@vueuse/core';
  import { useUserStore } from '/@/stores/modules/user';
  import { useAnimateData } from '/@/views/shares/components/login/index';
  import SlideVerify from 'vue3-slide-verify';
  import 'vue3-slide-verify/dist/style.css';
  import verify from '/@/assets/images/login/verify.svg';
  import { projectSettings } from '/@/settings/config/projectConfig';
  // const { hasFrontCode, hasBackEndCode } = projectConfigHook();
  const props = defineProps({
    currentType: {
      type: String as PropType<string>,
      default: '',
    },
    loginType: {
      type: String as PropType<string>,
      default: '',
    },
  });

  const { initial, enter } = useAnimateData();

  const i18nType = 'buttons.';
  const useI18n = true;
  const phoneNumber = ref('');
  const verifyCode = ref('');
  const slideVerifyShow = ref(false);
  const tacInit = ref();
  const msg = ref('');
  const {
    counter: counterPhone,
    pause: pausePhone,
    resume: resumPhone,
    isActive,
  } = useInterval(1000, { controls: true, immediate: false }); // 60s 刷新一次

  watch(
    () => counterPhone.value,
    (value) => {
      if (isActive && value > 60) {
        pausePhone();
        counterPhone.value = 0;
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );

  /**
   * 获取短信验证码
   */
  function getPhoneCode() {
    if (!loginPhoneReg.test(phoneNumber.value)) {
      const { createMessage } = useMessage();
      createMessage.error('请输入正确的手机号');
      return;
    }
    getMsgCode(encryptedSM4(phoneNumber.value)).then(() => {
      resumPhone();
    });
  }

  const userStore = useUserStore();
  const loading = ref<boolean>(false);

  const onLogin = async (): Promise<void> => {
    if (props.loginType === 'MSG') {
      if (!phoneNumber.value) {
        ElMessage({
          type: 'error',
          showClose: true,
          message: '手机号码不能为空',
        });
        return;
      }

      if (!verifyCode.value) {
        ElMessage({
          type: 'error',
          showClose: true,
          message: '验证码不能为空',
        });
        return;
      }
      imageLogin();
    }
  };
  function imageLogin() {
    const captchaConfig = {
      // 请求验证码接口 ?type=ROTATE
      requestCaptchaDataUrl: '/sys/image/gen?type=RANDOM',
      // 验证验证码接口
      validCaptchaUrl: '/sys/image/check',
      // 绑定的div
      bindEl: '#slider-valid',
      // 验证成功回调函数
      validSuccess: (res, c, t) => {
        console.log('res, c, t', res, c, t);
        onSuccess(t, res?.data?.id);
      },
      // 验证失败的回调函数(可忽略，如果不自定义 validFail 方法时，会使用默认的)
      validFail: (res, c, tac) => {
        // 验证失败后重新拉取验证码
        tac.reloadCaptcha();
      },
      // 刷新按钮回调事件
      btnRefreshFun: (el, tac) => {
        tac.reloadCaptcha();
      },
      // 关闭按钮回调事件
      btnCloseFun: (el, tac) => {
        tac.destroyWindow();
      },
    };

    window.initTAC('static/tac', captchaConfig).then((tac) => {
      tac.init();
      tacInit.value = tac;
    });
  }
  const onSuccess = (times: number, captchaId) => {
    const params: PhoneLoginParams = {
      phone: encryptedSM4(phoneNumber.value),
      smsCode: encryptedSM4(verifyCode.value),
      captchaId,
    };
    loading.value = true;
    userStore
      .login(params, true)
      .finally(() => {
        loading.value = false;
        if (projectSettings.verifyCode === 'slider') {
          // 关闭验证码
          tacInit.value.destroyWindow();
        } else {
          // 刷新验证码
          refreshCode();
        }
      })
      .catch(() => {
        slideVerifyShow.value = false;
      });
    msg.value = `login success, 耗时${(times / 1000).toFixed(1)}s`;
  };

  const onFail = () => {
    msg.value = '验证不通过';
  };

  const onRefresh = () => {
    msg.value = '点击了刷新小图标';
  };
  function refreshCode() {
    counter.value = 0;
    randomStr.value = new Date().getTime();
  }
  const keydown = (e) => {
    if (e.keyCode === 13) {
      onLogin();
    }
  };
  onMounted(() => {
    window.addEventListener('keydown', keydown);
  });

  defineExpose({ Message, initial });
</script>

<style scoped lang="scss">
  @import url('/src/styles/login/login.css');

  .loign-input {
    height: 40px;
  }
  .h-86 {
    :deep(.el-input__wrapper) {
      background: #f3f3f4;
    }
  }
  .focus {
    width: 100%;
    display: flex;
    .msg-input {
      width: 194px;
    }
    .identify {
      width: 104px;
      height: 38px;
      margin-left: 10px;
      margin-top: 1px;
      border: 1px solid rgba(0, 108, 255, 1);
      border-radius: 4px;
    }
    .verify {
      width: 50px;
      height: 20px;
    }
    .el-input-group__prepend {
      padding: 0 12px;
    }
    .el-button.is-link:not(.is-disabled):hover {
      border-color: #006cff;
    }
  }
</style>
