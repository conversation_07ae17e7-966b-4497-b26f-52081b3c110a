const colors = require('windicss/colors');

export default {
  darkMode: 'class', // or 'media' or 'class'
  extract: {
    include: ['index.html', './src/**/*.{js,jsx,ts,tsx,vue,html}'],
  },
  safelist: ['prose', 'prose-sm', 'm-auto'],
  theme: {
    screens: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
    },
    colors: {
      gray: colors.gray,
      red: colors.red,
      white: colors.white,
      green: colors.green,
      yellow: colors.amber,
      blue: colors.blue,
    },
    fontFamily: {
      sans: ['Graphik', 'sans-serif'],
      serif: ['Merriweather', 'serif'],
    },
    extend: {
      boxShadow: {
        DEFAULT: '0 1px 3px 2px rgba(0, 0, 0, 0.1)',
      },
      spacing: {
        128: '32rem',
        144: '36rem',
      },
      borderRadius: {
        '4xl': '2rem',
      },
    },
  },
};
