export const modelTypeOptions = [
  {
    value: 'openai',
    label: 'openai',
  },
  {
    value: 'ollama',
    label: 'ollama',
  },
];

export const modalNameFormSchema: any[] = [
  {
    field: 'name',
    label: '名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入名称',
    },
    required: true,
  },
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择类型',
      options: modelTypeOptions,
    },
    required: true,
  },
];

export const modalFormSchema: any[] = [
  {
    field: 'model',
    label: '模型名称',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入模型名称',
    },
    rules: [{ max: 30, message: '超过长度限制，最多30字', trigger: 'blur' }],
  },

  {
    field: 'maxTokens',
    label: '最大token数',
    component: 'InputNumber',
    colProps: { span: 24 },
    componentProps: {
      min: 1,
      step: 1,
    },
  },
  {
    field: 'temperature',
    label: '温度',
    component: 'InputNumber',
    colProps: { span: 24 },
    componentProps: {
      min: 0.1,
      step: 0.1,
    },
  },
  // {
  //   field: 'enableThinking',
  //   label: '是否思考',
  //   component: 'Switch',
  //   colProps: { span: 24 },
  //   componentProps: {
  //     activeText: '是',
  //     inactiveText: '否',
  //     style: '--el-switch-on-color: #13ce66; --el-switch-off-color: #dcdfe6',
  //   },
  // },
  {
    field: 'modelTypes',
    label: '模型类型',
    component: 'CheckboxGroup',
    colProps: { span: 24 },
    componentProps: {
      options: [
        // { label: '默认阶段', value: '0' },
        // { label: '认证阶段', value: '1' },
        // { label: '鉴权阶段', value: '2' },
        // { label: '统计阶段', value: '3' },
      ],
    },
  },
  {
    field: 'moreConfig',
    label: '配置样例',
    required: true,
    slot: 'moreConfig',
    component: 'Slot',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '模型配置',
    },
    // rules: [{ max: 200, message: '超过长度限制，最多200字', trigger: 'blur' }],
  },
];

export const modelData = [
  {
    id: '1940707847362478082',
    model: 'sss',
    maxTokens: 1,
    temperature: 0.7,
    enableThinking: false,
    moreConfig: {},
    modelTypes: null,
    teamId: '1',
    version: 0,
    modelEnable: true,
  },
  {
    id: '1935897109828042753',
    model: '11111',
    maxTokens: 4,
    temperature: 0.9,
    enableThinking: false,
    moreConfig: {
      a: '123',
    },
    modelTypes: 'MCP',
    teamId: '1',
    version: 5,
    modelEnable: true,
  },
  {
    id: '1935891047255212033',
    model: '模型名称',
    maxTokens: 3,
    temperature: 0.7,
    enableThinking: false,
    moreConfig: {
      a: '123',
    },
    modelTypes: 'MCP',
    teamId: '1',
    version: 1,
    modelEnable: true,
  },
];
