import NProgress from 'nprogress';
import { useUserStore } from '/@/stores/modules/user';
import { setTitle } from '/@/utils/settings';

// 进度条
import 'nprogress/nprogress.css';
import { Router } from 'vue-router';
import { PageEnum } from '/@/enums/pageEnum';
import { usePermissionStore } from '/@/stores/modules/permission';
import { buildMenus } from '/@/router/menus';
import { UrlToJson } from '/@/utils/operate/urlUtils';
import { PAGE_NOT_FOUND, WHITE_MENU_PATH_LIST } from '/@/router';
import { GetMicroData } from '/@/router/tools/menuStorage';
import { clearMicroPage } from '/@/views/micro/libs/clearPage';
const LOGIN_PATH = PageEnum.BASE_LOGIN;
// 路由白名单，这些路由不在权限拦截下，直接跳过

/**
 * 路由拦截
 * 权限验证
 */
export function createPermissionGuard(router: Router) {
  const userStore = useUserStore();
  const permissionStore = usePermissionStore();
  // await permissionStore.genFrontWhiteMenuList();
  router.beforeEach(async (to, from, next) => {
    // 进度条
    NProgress.start();

    if (!permissionStore.getDynamicAddedBusMenu) {
      // 加载 白名单菜单，无需登录的业务菜单
      permissionStore.genFrontWhiteMenuList();
    }
    // if (from.path === '/' && to.path === '/') {
    //   next(PageEnum.BASE_HOME);
    //   return;
    // }
    // Whitelist can be directly entered
    if (WHITE_MENU_PATH_LIST.includes(to.path as PageEnum)) {
      next();
      NProgress.done();
      return;
    }

    // debugger;
    const token = userStore.getToken;
    if (!token) {
      // You can access without permission. You need to set the routing meta.ignoreAuth to true
      if (to.meta.ignoreAuth) {
        next();
        NProgress.done();
        return;
      }
      // redirect login page
      const redirectData: {
        path: string;
        replace: boolean;
        query?: Recordable<string>;
      } = {
        path: LOGIN_PATH,
        replace: true,
      };
      if (to.path && to.path !== '/') {
        redirectData.query = {
          ...redirectData.query,
          redirect: to.fullPath,
        };
      }
      next(redirectData);
      NProgress.done();
      return;
    }
    // 解决从微应用切换到主应用后，微应用最后一个页面被缓存的问题
    if (
      from.fullPath.indexOf('/micro/vite/') > -1 &&
      to.fullPath.indexOf('/micro/vite/') === -1
    ) {
      clearMicroPage();
    }
    // 当存在token
    if (
      from.path === '/' &&
      to.path === PageEnum.BASE_HOME &&
      userStore.userInfo?.homePath
    ) {
      next(userStore.userInfo.homePath);
      return;
    }

    // 存在token, 当需要打开 '/'转跳到用户首页，否则转跳到系统配置的首页
    if (to.path === '/') {
      if (userStore.userInfo?.homePath) {
        next(userStore.userInfo.homePath);
      } else {
        next(PageEnum.BASE_HOME);
      }
      return;
    }
    if (permissionStore.getDynamicAddedRoute) {
      if (from.path === LOGIN_PATH) {
        // 登录流程，默认登录
        const redirectString = from.query.redirect as string;
        if (!redirectString || redirectString.split('?')[0] === to.path) {
          // 没有redirect参数，或者redirect参数的主url等于将要跳转的地址，则直接跳homePath或者BASE_HOME
          permissionStore.setCurrentMenu(to);
          next();
        } else {
          /**
           * 有redirect地址，并将query值带上 （说明是token消失了）
           */
          // const redirectPath = (redirectString || to.path) as string;
          const redirectPath = redirectString as string;
          // 将 login的redirect 中带的参数，转换为真正的query
          let realQuery = {};
          if (redirectString) {
            const index = redirectString.indexOf('?');
            if (index > -1) {
              realQuery = UrlToJson(
                redirectString.slice(index + 1, redirectString.length),
              );
            }
          }
          const redirect = decodeURIComponent(redirectPath);
          next({ path: redirect, query: realQuery });
        }
      } else {
        // 一切正常 会走这里,设置选中setCurrentMenu

        // 解决因为刷新导致的微前端数据丢失的问题
        if (to.path.indexOf('/micro/vite') > -1 && !to.meta.microName) {
          const microList = GetMicroData();
          if (microList.length) {
            const microMeta = GetMicroData().find(
              (item) => item.path === to.path,
            )?.meta;
            // @ts-ignore
            to.meta = microMeta || to.meta;
          }
        }
        // 处理手动输入url的情况，点击的情况会在菜单或者tab标签中进行处理
        permissionStore.setCurrentMenu(to);

        next();
      }
      return;
    }

    // 刷新页面，加载菜单，筛选路由
    await buildMenus();

    // 本地刷新
    if (to.name === PAGE_NOT_FOUND.name) {
      // 动态添加路由后，此处应当重定向到fullPath，否则会加载404页面内容
      // 重定向
      next({ path: to.fullPath, replace: true, query: to.query });
    } else {
      next();
    }
  });

  router.afterEach((to) => {
    // 进度条
    NProgress.done();
    const title = to.meta.title;
    // if (to.path.startsWith('/micro')) {
    //   const meta: Meta = permissionStore.getCurrentMenu.meta as Meta;
    //   title = meta.title || '';
    // }
    setTitle(title as string);
  });

  /**
   * 解决子应用部署后出现加载历史文件导致的路由加载失败的问题
   */
  router.onError((error) => {
    if (error.message.indexOf('Failed to fetch dynamically') !== -1) {
      window.location.reload();
    }
  });
}
