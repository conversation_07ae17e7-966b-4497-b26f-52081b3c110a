<script setup lang="ts">
  import { computed, withDefaults } from 'vue';

  interface Props {
    prefix?: string;
    name?: string;
    size?: number;
    color?: string;
    className?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    prefix: 'icon',
    name: '',
    size: 25,
    className: '',
    color: '#000',
  });

  const symbolId = computed(() => `#${props.prefix}-${props.name}`);
</script>

<template>
  <svg aria-hidden="true" :style="{ width: `${size}px`, height: `${size}px` }">
    <use :xlink:href="symbolId" :fill="color" />
  </svg>
</template>
