import WujieVue from 'wujie-vue3';
import { isMicro } from '/@/utils/operate/micro';

const { bus } = WujieVue;

export class MicroStore<T> {
  private key: string;
  constructor(key: string, callback: Function) {
    this.key = key;
    if (!isMicro()) return;
    bus.$on(key, function (data) {
      callback(data);
    });
  }

  setMicroData(data: Nullable<T>) {
    if (isMicro()) return;
    bus.$emit(this.key, data);
  }
}
