<template>
  <div class="right-group">
    <el-card shadow="hover" class="mb-2">
      <template #header>
        <div class="card-header flex justify-between items-center">
          <span>快速开始</span>
          <!--<el-select
            v-if="selectShow"
            v-model="value"
            placeholder="请选择"
            size="small"
            style="width: 240px"
            @change="onChange"
          >
            <el-option
              v-for="item in gotoList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button type="primary" :icon="Plus" circle @click="selectShow = true"/>-->
        </div>
      </template>
      <div class="flex flex-wrap">
        <el-tag
          class="tag"
          v-for="item in tagList"
          :key="item.path"
          :closable="item.delete"
          @close="handleClose(item)"
        >
          <router-link class="goto" :to="item.path">{{ item.name }}</router-link>
        </el-tag>
        <!--<el-tag class="tag">
          <router-link class="goto" to="/upms/userList">平台员工管理</router-link>
        </el-tag>
        <el-tag class="tag">
          <router-link class="goto" to="/upms/roleList">平台角色管理</router-link>
        </el-tag>-->

        <!--<div v-for="o in 7" :key="o" class="p-1">

        </div>-->
        <el-select
          v-if="selectShow"
          v-model="value"
          placeholder="请选择"
          style="width: 100px; margin-top: 10px"
          filterable
          @change="onChange"
        >
          <el-option
            v-for="item in gotoList"
            :key="item.path"
            :label="item.name"
            :value="item.path"
          />
        </el-select>
        <el-button
          v-else
          class="button-new-tag"
          size="small"
          @click="selectShow = true"
          :icon="Plus"
          style="margin-top: 10px; padding: 15px 10px"
        />
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts">
  import { ref } from 'vue';
  import { Plus } from '@element-plus/icons-vue';
  import { getAllMenuStorage } from '/@/router/tools/menuStorage';
  import { quicklyStartSearch, quicklyStartStorage } from '../api/project';
  // import { gotoList } from './dashboard.data';
  const gotoList = ref([]);
  const selectShow = ref(false);
  const value = ref('');
  const tagList = ref([
    {
      name: '平台账号管理',
      path: '/upms/accountList',
    },
    // {
    //   name: '平台员工管理',
    //   path: '/upms/userList'
    // },
    {
      name: '平台角色管理',
      path: '/upms/roleList',
    },
  ]);

  getAllMenuStorage().map((item) => {
    if (item.path !== '/dashboard') {
      item.children.map((childItem) => {
        gotoList.value.push({
          name: childItem.label,
          path: childItem.path,
        });
      });
    }
  });
  const getFilterList = function () {
    const filterList = gotoList.value;
    gotoList.value.map((item, index) => {
      tagList.value.map((tagItem) => {
        if (tagItem.path == item.path) {
          filterList.splice(index, 1);
        }
      });
    });
    gotoList.value = filterList;
  };
  const onChange = function (e) {
    gotoList.value.map((item, index) => {
      if (item.path == e) {
        tagList.value.push({
          name: item.name,
          path: item.path,
          delete: true,
        });
        gotoList.value.splice(index, 1);
        selectShow.value = false;
        value.value = '';
      }
    });
    saveList();
  };
  const handleClose = (tag) => {
    tagList.value.splice(tagList.value.indexOf(tag), 1);
    gotoList.value.unshift(tag);
    saveList();
  };
  const getlist = () => {
    quicklyStartSearch().then((res) => {
      tagList.value = res.length > 0 ? res : tagList.value.concat(res);
      getFilterList();
    });
  };
  const saveList = () => {
    quicklyStartStorage(tagList.value).then(() => {
      // getFilterList();
    });
  };
  const init = () => {
    getlist();
  };
  init();
</script>

<style scoped lang="scss">
  .right-group {
    width: 100%;
    height: 100%;
    // overflow-y: auto;
    :deep(.el-card) {
      height: 100%;
    }
    :deep(.el-card__body) {
      height: 100%;
    }
    .tag {
      margin: 10px 10px 0 0;
      padding: 15px 10px;
    }
    .goto {
      color: #1a5efe;
    }

    //   .group-list {
    //     width: 100%;
    //     height: 390px;
    //     background: #ffffff;
    //     border-radius: 8px;
    //     padding: 20px 24px;
    //     .title-all {
    //       width: 100%;
    //       height: 30px;
    //       display: flex;
    //       justify-content: space-between;
    //       margin-bottom: 20px;
    //       .title {
    //         font-size: 18px;
    //         color: #333333;
    //         letter-spacing: 0;
    //         line-height: 24px;
    //         font-weight: 600;
    //       }
    //       .all {
    //         font-size: 12px;
    //         color: #999999;
    //         font-weight: 400;
    //       }
    //     }
    //     .member {
    //       width: 100%;
    //       display: flex;
    //       margin-bottom: 20px;
    //       .img {
    //         width: 36px;
    //         height: 36px;
    //         background: #ddd;
    //       }
    //       .member-right {
    //         width: calc(100% - 46px);
    //         margin-left: 10px;
    //       }
    //       .mes {
    //         display: flex;
    //         .name {
    //           width: calc(100% - 80px);
    //           height: 20px;
    //           font-size: 14px;
    //           color: #333333;
    //           letter-spacing: 0;
    //           line-height: 22px;
    //           font-weight: 600;
    //         }
    //         // .time {
    //         //   width: 80px;
    //         //   height: 20px;
    //         //   font-size: 12px;
    //         //   color: #666666;
    //         //   letter-spacing: 0;
    //         //   line-height: 20px;
    //         //   font-weight: 400;
    //         //   text-align: right;
    //         // }
    //       }
    //       .dept {
    //         font-size: 12px;
    //         color: #666666;
    //         letter-spacing: 0;
    //         line-height: 20px;
    //         font-weight: 400;
    //       }
    //     }
    //   }
  }
</style>
