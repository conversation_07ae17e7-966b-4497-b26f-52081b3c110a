<!--
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
-->
<template>
  <div class="datasource-cascader">
    <!-- 选择框 -->
    <div 
      ref="inputRef"
      class="cascader-input" 
      @click="toggleDropdown" 
      :class="{ active: showDropdown }"
    >
      <span class="cascader-value" v-if="displayValue">{{ displayValue }}</span>
      <span class="cascader-placeholder" v-else>请选择数据源</span>
      <div class="cascader-actions">
        <Icon
          v-if="displayValue"
          icon="carbon:close"
          class="clear-btn"
          @click.stop="clearSelection"
          title="清除选择"
        />
        <Icon icon="carbon:chevron-down" class="cascader-arrow" :class="{ rotated: showDropdown }" />
      </div>
    </div>

    <!-- 下拉面板 -->
    <teleport to="body">
      <div 
        v-if="showDropdown" 
        ref="dropdownRef"
        class="cascader-dropdown" 
        @click.stop
        :style="dropdownStyle"
      >
        <div class="cascader-panels">
        <!-- 数据源面板 -->
        <div class="cascader-panel">
          <div class="panel-header">
            <Icon icon="carbon:data-base" />
            <span>数据源</span>
          </div>
          <div class="panel-content">
            <div v-if="dataSourceLoading" class="loading-item">
              <Icon icon="carbon:circle-dash" class="spinning" />
              <span>加载中...</span>
            </div>
            
            <div 
              v-else
              v-for="dataSource in dataSourceList" 
              :key="dataSource.id"
              class="cascader-item"
              :class="{ active: selectedDataSource?.id === dataSource.id }"
              @click="selectDataSource(dataSource)"
            >
              <Icon icon="carbon:data-base" />
              <span>{{ dataSource.name }}</span>
              <Icon icon="carbon:chevron-right" class="item-arrow" />
            </div>
          </div>
        </div>

        <!-- 数据库面板 -->
        <div class="cascader-panel" v-if="selectedDataSource">
          <div class="panel-header">
            <Icon icon="carbon:data-table" />
            <span>数据库</span>
          </div>
          <div class="panel-content">
            <div v-if="databaseLoading" class="loading-item">
              <Icon icon="carbon:circle-dash" class="spinning" />
              <span>加载中...</span>
            </div>
            <div
              v-else
              v-for="database in databaseList"
              :key="database.name"
              class="cascader-item"
              :class="{ active: selectedDatabase?.name === database.name }"
              @click="selectDatabase(database)"
            >
              <Icon icon="carbon:data-table" />
              <span>{{ database.name }}</span>
              <Icon
                v-if="needsSchemaSelection"
                icon="carbon:chevron-right"
                class="item-arrow"
              />
            </div>
          </div>
        </div>

        <!-- 模式面板 (只有POSTGRESQL和KINGBASE类型才显示) -->
        <div class="cascader-panel" v-if="selectedDatabase && needsSchemaSelection">
          <div class="panel-header">
            <Icon icon="carbon:table" />
            <span>模式</span>
          </div>
          <div class="panel-content">
            <div v-if="schemaLoading" class="loading-item">
              <Icon icon="carbon:circle-dash" class="spinning" />
              <span>加载中...</span>
            </div>
            <div
              v-else
              v-for="schema in schemaList"
              :key="schema.name"
              class="cascader-item"
              :class="{ active: selectedSchema?.name === schema.name }"
              @click="selectSchema(schema)"
            >
              <Icon icon="carbon:table" />
              <span>{{ schema.name }}</span>
            </div>
          </div>
        </div>
      </div>
      </div>
    
  </teleport>
</div>

</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  watch,
  onMounted,
  onUnmounted,
  onBeforeUpdate,
  nextTick
} from 'vue';
import { Icon } from '@iconify/vue';
import { AxiosApiService } from '../../chatagent/api/axios-api-service';
import { ElMessage } from 'element-plus';

export default defineComponent({
  name: 'DataSourceCascader',
  components: {
    Icon,
  },
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        dataSourceId: undefined,
        dataSourceName: undefined,
        databaseName: undefined,
        schemaName: undefined,
      }),
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const showDropdown = ref(false);
    const dataSourceList = ref<any[]>([]);
    const databaseList = ref<any[]>([]);
    const schemaList = ref<any[]>([]);

    const selectedDataSource = ref<any | null>(null);
    const selectedDatabase = ref<any | null>(null);
    const selectedSchema = ref<any | null>(null);

    const dataSourceLoading = ref(false);
    const databaseLoading = ref(false);
    const schemaLoading = ref(false);

    const dropdownRef = ref<HTMLDivElement | null>(null);
    const inputRef = ref<HTMLDivElement | null>(null);
    const dropdownStyle = ref({});

    const displayValue = computed(() => {
      if (selectedDataSource.value && selectedDatabase.value && selectedSchema.value) {
        return `${selectedDataSource.value.name}/${selectedDatabase.value.name}/${selectedSchema.value.name}`;
      }
      if (selectedDataSource.value && selectedDatabase.value) {
        return `${selectedDataSource.value.name}/${selectedDatabase.value.name}`;
      }
      return '';
    });

    const needsSchemaSelection = computed(() => {
      if (!selectedDataSource.value) return false;
      const type = selectedDataSource.value.type?.toUpperCase();
      return type === 'POSTGRESQL' || type === 'KINGBASE' || type === 'HIVE';
    });

    const loadDataSources = async () => {
      dataSourceLoading.value = true;
      try {
        const response = await AxiosApiService.getDataSourceList();
        dataSourceList.value = response;
      } catch (error) {
        console.error('加载数据源失败:', error);
        ElMessage.error('加载数据源失败');
      } finally {
        dataSourceLoading.value = false;
      }
    };

    const loadDatabases = async () => {
      if (!selectedDataSource.value) return;

      databaseLoading.value = true;
      try {
        const response = await AxiosApiService.getDatabaseList(
          selectedDataSource.value.id,
        );
        databaseList.value = response;
      } catch (error) {
        console.error('加载数据库列表失败:', error);
        ElMessage.error('加载数据库列表失败');
      } finally {
        databaseLoading.value = false;
      }
    };

    const loadSchemas = async () => {
      if (!selectedDataSource.value || !selectedDatabase.value) return;

      schemaLoading.value = true;
      try {
        const response = await AxiosApiService.getSchemaList(
          selectedDataSource.value.id,
          selectedDatabase.value.name,
        );
        schemaList.value = response;
      } catch (error) {
        console.error('加载模式列表失败:', error);
        ElMessage.error('加载模式列表失败');
      } finally {
        schemaLoading.value = false;
      }
    };

    const selectDataSource = async (dataSource: any) => {
      selectedDataSource.value = dataSource;
      selectedDatabase.value = null;
      selectedSchema.value = null;
      await loadDatabases();
    };

    const selectDatabase = async (database: any) => {
      selectedDatabase.value = database;
      selectedSchema.value = null;

      if (needsSchemaSelection.value) {
        await loadSchemas();
      } else {
        emitSelectedValue();
      }
    };

    const selectSchema = (schema: any) => {
      selectedSchema.value = schema;
      emitSelectedValue();
    };

    const emitSelectedValue = () => {
      const value = {
        dataSourceId: selectedDataSource.value?.id,
        dataSourceName: selectedDataSource.value?.name,
        databaseName: selectedDatabase.value?.name,
        schemaName: selectedSchema.value?.name,
      };
      emit('update:modelValue', value);
      showDropdown.value = false;
    };

    const clearSelection = () => {
      selectedDataSource.value = null;
      selectedDatabase.value = null;
      selectedSchema.value = null;
      emit('update:modelValue', {
        dataSourceId: undefined,
        dataSourceName: undefined,
        databaseName: undefined,
        schemaName: undefined,
      });
    };

    const updateDropdownPosition = () => {
      if (inputRef.value && dropdownRef.value) {
        const rect = inputRef.value.getBoundingClientRect();
        dropdownStyle.value = {
          position: 'fixed',
          top: `${rect.bottom + window.scrollY}px`,
          left: `${rect.left + window.scrollX}px`,
          width: `${rect.width}px`,
        };
      }
    };

    const toggleDropdown = () => {
      showDropdown.value = !showDropdown.value;
      if (showDropdown.value && dataSourceList.value.length === 0) {
        loadDataSources();
      }
      if (showDropdown.value) {
        nextTick(() => updateDropdownPosition());
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.value &&
        !inputRef.value.contains(event.target as Node) &&
        dropdownRef.value &&
        !dropdownRef.value.contains(event.target as Node)
      ) {
        showDropdown.value = false;
      }
    };

    watch(
      () => props.modelValue,
      (newValue) => {
        if (newValue?.dataSourceId) {
          loadDataSources().then(() => {
            const dataSource = dataSourceList.value.find(
              (ds) => ds.id === newValue.dataSourceId,
            );
            if (dataSource) {
              selectedDataSource.value = dataSource;

              loadDatabases().then(() => {
                if (newValue.databaseName) {
                  const database = databaseList.value.find(
                    (db) => db.name === newValue.databaseName,
                  );
                  if (database) {
                    selectedDatabase.value = database;

                    if (needsSchemaSelection.value && newValue.schemaName) {
                      loadSchemas().then(() => {
                        const schema = schemaList.value.find(
                          (s) => s.name === newValue.schemaName,
                        );
                        if (schema) {
                          selectedSchema.value = schema;
                        }
                      });
                    }
                  }
                }
              });
            }
          });
        }
      },
      { immediate: true },
    );

    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
      window.addEventListener('resize', updateDropdownPosition);
      window.addEventListener('scroll', updateDropdownPosition);
    });

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
      window.removeEventListener('resize', updateDropdownPosition);
      window.removeEventListener('scroll', updateDropdownPosition);
    });

    return {
      showDropdown,
      dataSourceList,
      databaseList,
      schemaList,
      selectedDataSource,
      selectedDatabase,
      selectedSchema,
      dataSourceLoading,
      databaseLoading,
      schemaLoading,
      displayValue,
      needsSchemaSelection,
      dropdownRef,
      inputRef,
      toggleDropdown,
      selectDataSource,
      selectDatabase,
      selectSchema,
      clearSelection,
      emitSelectedValue,
    };
  },
});
</script>

<style scoped>
.datasource-cascader {
  position: relative;
  width: 100%;
}

.cascader-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 12px;
  border-radius: 0.375rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px;
  width: 100%;
  border: 1px solid #d9d9d9;
}

.cascader-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.cascader-input:hover {
  border-color: #9ca3af;
}

.cascader-input.active {
  /* border-color: #3b82f6; */
  /* box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); */
}

.cascader-value {
  color: #374151;
  font-size: 0.875rem;
}

.cascader-placeholder {
  color: #9ca3af;
  font-size: 0.875rem;
}

.clear-btn {
  color: rgb(96, 98, 102);
  cursor: pointer;
  padding: 0.125rem;
  border-radius: 50%;
  /* transition: all 0.2s ease; */
}

.clear-btn:hover {
  color: rgb(96, 98, 102);
  background: #fff;
  border: 1px solid #d9d9d9;
}

.cascader-arrow {
  color: #9ca3af;
  transition: transform 0.2s ease;
}

.cascader-arrow.rotated {
  transform: rotate(180deg);
}

.cascader-dropdown {
  position: fixed;
  z-index: 2000;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-height: 400px;
  overflow-y: auto;
  min-width: 600px;
  max-width: calc(100vw - 40px); /* Ensure it doesn't exceed viewport width */
}

.cascader-panels {
  display: flex;
  height: 100%;
  max-height: 400px;
  width: 100%;
}

.cascader-panel {
  flex: 1;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  min-width: 200px;
  max-width: 250px;
  max-height: 400px;
  overflow-y: auto;
}

.cascader-panel:last-child {
  border-right: none;
}

.panel-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
  font-size: 0.875rem;
  color: #374151;
  flex-shrink: 0;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  max-height: calc(400px - 60px);
  min-height: 0;
}

/* 美化滚动条 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.cascader-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
  color: #374151;
  white-space: nowrap; /* Prevent text wrapping */
  overflow: hidden;
  text-overflow: ellipsis; /* Show ellipsis for long text */
}

.cascader-item:hover {
  background: #f3f4f6;
}

.cascader-item.active {
  background: #eff6ff;
  color: #3b82f6;
}

.item-arrow {
  margin-left: auto;
  color: #9ca3af;
  font-size: 0.75rem;
}

.loading-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>