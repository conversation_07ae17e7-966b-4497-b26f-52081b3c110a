<!--
 * @Author: wangneng <EMAIL>
 * @Date: 2022-08-01 14:30:52
 * @LastEditors: wangneng <EMAIL>
 * @LastEditTime: 2022-08-02 12:31:15
 * @FilePath: \cpvf-pools\src\components\sys\Menu\src\SysSubMenu.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-sub-menu v-if="item.children && item.children?.length > 0" :index="item.path">
    <template #title>
      <!--      <el-icon><location /></el-icon>-->
      <sys-icon :type="item.categoryIcon" />
      <span>{{ item.meta?.title }}</span>
    </template>
    <template v-for="itemX in item.children">
      <sys-sub-menu
        v-if="itemX.children && itemX.children?.length > 0"
        :item="itemX"
        :key="itemX.path"
      />
      <el-menu-item v-else :index="itemX.path" :key="itemX.path">
        <sys-icon :type="itemX.categoryIcon" v-if="itemX.categoryIcon !== 'menu'" />
        <span>{{ itemX.meta?.title }}</span>
      </el-menu-item>
    </template>
  </el-sub-menu>
  <el-menu-item v-else :index="item.path">{{ item.meta?.title }}</el-menu-item>
</template>

<script setup lang="ts" name="SysSubMenu">
  import SysIcon from '/@/components/sys/Icon';
  import { PropType } from 'vue';
  import { Menu } from '/@/stores/types';

  defineProps({
    item: {
      // route object
      type: Object as PropType<Menu>,
      required: true,
    },
    deeps: {
      type: Number as PropType<number>,
      default: 0,
    },
    flodPath: {
      type: String,
      default: '',
    },
    collapsed: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  });
</script>
<style scoped>
  /* :deep(.el-menu-item.is-active) {
    background-color: var(--el-color-primary);
  } */
  .el-menu-item.is-active {
    background-color: rgba(205, 208, 220, 0.8);
  }

  .el-menu-item {
    color: rgba(38, 36, 76, 0.88);
    height: 36px;
    line-height: 36px;
    margin: 8px 18px;
    border-radius: 5px;
  }

  .el-menu-item:hover {
    background-color: rgba(205, 208, 220, 0.8);
  }

  .el-menu {
    width: 100%;
  }

  .el-menu.el-menu--collapse > .el-menu-item {
    padding: 0 6px;
    margin: 8px 10px;
  }

  :deep(.el-menu-item .el-menu-tooltip__trigger) {
    padding: 0 8px;
  }

  :deep(.el-sub-menu__title) {
    color: rgba(38, 36, 76, 0.88);
  }
</style>
