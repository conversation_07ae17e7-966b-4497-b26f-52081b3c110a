# 知识库模块重构说明

## 重构概述

本次重构将 `BusinessLogicTable.vue`、`CaseOptimizationTable.vue` 和 `TermTable.vue` 三个文件中的大量相似和重复代码进行了抽离，创建了可复用的公共组件和工具函数。

## 重构成果

### 1. 公共组件
- **KnowledgeTable.vue** (`components/KnowledgeTable.vue`)
  - 通用的知识库表格组件
  - 封装了表格的结构和操作逻辑
  - 支持搜索、批量操作、新增、编辑、保存、取消、删除等功能
  - 集成了数据源和数据表的级联选择

### 2. 工具函数
- **useKnowledgeTable.ts** (`composables/useKnowledgeTable.ts`)
  - 知识库表格的通用数据管理和操作逻辑
  - 包含数据加载、搜索、增删改查、分页、批量操作等功能
  - 提供数据源和数据表的级联选择和相关处理函数

### 3. 重构后的页面组件
- **TermTable.vue** - 名词解释表格
- **BusinessLogicTable.vue** - 业务逻辑表格  
- **CaseOptimizationTable.vue** - 案例优化表格

每个页面组件现在只需要：
1. 导入 `KnowledgeTable` 组件
2. 定义对应的表格配置
3. 使用 `<KnowledgeTable :config="tableConfig" />` 渲染表格

## 代码对比

### 重构前
每个文件都有 600+ 行代码，包含大量重复的：
- 表格操作逻辑
- 数据源级联选择器
- 表格列表组件
- 通用接口调用逻辑

### 重构后
每个文件只有约 50 行代码，只需要配置：
- `promptType` - 数据类型
- `title` - 表格标题
- `searchPlaceholder` - 搜索占位符
- `addButtonText` - 新增按钮文本
- `columns` - 列配置

## 配置示例

```typescript
const tableConfig = computed<KnowledgeTableConfig>(() => ({
  promptType: 'NOUN_DESCRIPTION',
  title: '名词解释',
  searchPlaceholder: '搜索名词',
  addButtonText: '新增名词解释',
  columns: [
    {
      prop: 'promptName',
      label: '名词解释',
      minWidth: 150,
      editable: true,
      required: true,
      placeholder: '请输入名词'
    },
    {
      prop: 'promptContent',
      label: '名词描述',
      minWidth: 250,
      editable: true,
      required: true,
      type: 'textarea',
      placeholder: '请输入描述'
    },
    {
      prop: 'dataSources',
      label: '绑定数据源',
      minWidth: 180,
      type: 'dataSource',
      editable: true
    },
    {
      prop: 'tableName',
      label: '数据表',
      width: 200,
      type: 'table',
      editable: true
    }
  ]
}));
```

## 优势

1. **代码复用性** - 公共逻辑只需维护一份
2. **可维护性** - 修改功能只需要修改公共组件
3. **一致性** - 所有表格的行为和样式保持一致
4. **扩展性** - 新增类似表格只需要配置即可
5. **代码量减少** - 总代码量减少约 80%

## 文件结构

```
src/views/knowledgebase/
├── components/
│   ├── DataSourceCascader.vue (已存在)
│   └── KnowledgeTable.vue (新增)
├── composables/
│   └── useKnowledgeTable.ts (新增)
└── pages/base/
    ├── TermTable.vue (重构)
    ├── BusinessLogicTable.vue (重构)
    └── CaseOptimizationTable.vue (重构)
```

## 注意事项

1. 所有原有功能都得到保留
2. API 调用逻辑保持不变
3. 样式和交互体验保持一致
4. 支持后续功能扩展和定制