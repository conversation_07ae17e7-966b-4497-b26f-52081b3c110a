worker_processes  1;

events {
    worker_connections  1024;
}

http {

    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;
    gzip on;
    gzip_static on;
    gzip_comp_level 5;
    gzip_http_version 1.1;
    gzip_vary on;
    gzip_types text/plain text/css text/javascript application/javascript application/xml application/x-httpd-php application/vnd.ms.fontobject image/jpeg image/png image/gif image/svg+xml font/ttf font/opentype font/x-woff;

    server {
        listen 80;
        server_name localhost;

        location / {
            root   /usr/share/nginx/html;    # 指定前端项目绝对路径
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;# 处理刷新重定向404问题
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}

