export function checkKeyboardContinuousChar(str) {
  const c1 = [
    ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+'],
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '{', '}', '|'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ':', '"'],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm', '<', '>', '?'],
  ];
  const c2 = [
    ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='],
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'"],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/'],
  ];
  str = str.toLowerCase().split('');
  // 获取坐标位置
  const y: any = [];
  const x: any = [];
  for (let c = 0; c < str.length; c++) {
    y[c] = 0; // 当做~`键处理
    x[c] = -1;
    for (let i = 0; i < c1.length; i++) {
      for (let j = 0; j < c1[i].length; j++) {
        if (str[c] == c1[i][j]) {
          y[c] = i;
          x[c] = j;
        }
      }
    }
    if (x[c] != -1) continue;
    for (let i = 0; i < c2.length; i++) {
      for (let j = 0; j < c2[i].length; j++) {
        if (str[c] == c2[i][j]) {
          y[c] = i;
          x[c] = j;
        }
      }
    }
  }
  // 匹配坐标连线
  for (let c = 1; c < str.length - 1; c++) {
    // 横着同一行
    if (y[c - 1] == y[c] && y[c] == y[c + 1]) {
      // 从左往右或者从右往左一排
      if (
        (x[c - 1] + 1 == x[c] && x[c] + 1 == x[c + 1]) ||
        (x[c + 1] + 1 == x[c] && x[c] + 1 == x[c - 1])
      ) {
        return true;
      }
    }
    // 竖着同一列
    if (x[c - 1] == x[c] && x[c] == x[c + 1]) {
      // 从下往上或者从下往下同一列
      if (
        (y[c - 1] + 1 == y[c] && y[c] + 1 == y[c + 1]) ||
        (y[c + 1] + 1 == y[c] && y[c] + 1 == y[c - 1])
      ) {
        return true;
      }
    }
    // 竖着同一列（类似/而不是\的一列）
    if (
      (x[c - 1] + 1 == x[c] && x[c] + 1 == x[c + 1]) ||
      (x[c - 1] - 1 == x[c] && x[c] - 1 == x[c + 1])
    ) {
      // 从下往上或者从下往下同一列
      if (
        (y[c - 1] + 1 == y[c] && y[c] + 1 == y[c + 1]) ||
        (y[c + 1] + 1 == y[c] && y[c] + 1 == y[c - 1])
      ) {
        return true;
      }
    }
  }
  return false;
}
export function checkPasswordStrength(password, num) {
  // 定义字符集范围
  const numberPattern = /\d/;
  const letterPattern = /[a-zA-Z]/;
  const specialCharPattern = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/;

  // 计数器，用于记录涵盖的字符集数量
  let count = 0;

  // 检查密码是否包含数字字符集
  if (numberPattern.test(password)) {
    count++;
  }

  // 检查密码是否包含字母字符集
  if (letterPattern.test(password)) {
    count++;
  }

  // 检查密码是否包含特殊符号字符集
  if (specialCharPattern.test(password)) {
    count++;
  }

  // 检查是否至少包含两项字符集
  if (count >= num) {
    return true; // 符合要求
  } else {
    return false; // 不符合要求
  }
}
