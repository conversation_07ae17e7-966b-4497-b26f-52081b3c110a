<template>
  <ul class="setting">
    <li class="flex">
      <span class="pr-2 w-36 pt-2">布局</span>
      <el-radio-group v-model="getProjectConfig.layout" @change="saveProjectConfig">
        <el-radio-button label="vertical">侧边栏菜单</el-radio-button>
        <el-radio-button label="horizontal">顶部菜单</el-radio-button>
      </el-radio-group>
    </li>
    <li class="flex pt-4">
      <span class="pr-2 w-36 pt-2">菜单模式</span>
      <el-radio-group
        v-model="getProjectConfig.permissionMode"
        @change="saveProjectConfig"
      >
        <el-radio-button label="FRONT_END">纯前端</el-radio-button>
        <el-radio-button label="ROUTE_MAPPING">纯前端+后端</el-radio-button>
      </el-radio-group>
    </li>
    <li class="flex pt-4">
      <span class="pr-2 w-36 pt-2">菜单折叠</span>
      <el-radio-group
        v-model="getProjectConfig.menuSetting.collapsed"
        @change="saveProjectConfig"
      >
        <el-radio-button :label="true">菜单折叠</el-radio-button>
        <el-radio-button :label="false">菜单展开</el-radio-button>
      </el-radio-group>
    </li>
    <li class="flex pt-4">
      <span class="pr-2 w-36 pt-2">菜单展开宽度</span>
      <div class="flex">
        <el-input-number
          v-model="getProjectConfig.menuWidth"
          @blur="saveProjectConfig"
        />
      </div>
    </li>
    <li class="flex pt-4">
      <span class="pr-2 w-36 pt-2">菜单折叠宽度</span>
      <div class="flex">
        <el-input-number
          v-model="getProjectConfig.collapseWidth"
          @blur="saveProjectConfig"
        />
      </div>
    </li>
    <!-- <li class="pt-4">
      <span class="pr-2 w-36 pt-2">登录验证码</span>
      <el-radio-group v-model="getProjectConfig.verifyCode" @change="saveProjectConfig">
        <el-radio-button label="both">全部</el-radio-button>
        <el-radio-button label="front">前端拖拽</el-radio-button>
        <el-radio-button label="backend">后端</el-radio-button>
        <el-radio-button label="none">都不使用</el-radio-button>
      </el-radio-group>
    </li> -->
    <!-- <li class="flex pt-4">
      <span class="pr-2 w-36 pt-2">缓存前缀</span>
      <el-tag class="pr-2 w-32 pt-2">{{ getProjectConfig.storageName }}</el-tag>
    </li>
    <li class="flex pt-4">
      <span class="pr-2 w-36 pt-2">样式前缀(style)</span>
      <el-tag class="pr-2 w-32 pt-2">{{ getProjectConfig.prefixCls }}</el-tag>
    </li> -->
    <li class="flex pt-4">
      <span class="pr-2 w-36 pt-2">统一认证(账号)</span>
      <el-radio-group v-model="getProjectConfig.loginType" @change="saveProjectConfig">
        <el-radio-button :label="0">不启用</el-radio-button>
        <el-radio-button :label="1">启用</el-radio-button>
      </el-radio-group>
    </li>
    <li class="flex pt-4">
      <span class="pr-2 w-36 pt-2">统一认证(二维码)</span>
      <el-radio-group v-model="getProjectConfig.loginCode" @change="saveProjectConfig">
        <el-radio-button :label="false">不启用</el-radio-button>
        <el-radio-button :label="true">启用</el-radio-button>
      </el-radio-group>
    </li>
  </ul>
</template>

<script setup lang="ts">
  import { useAppStore } from '/@/stores/modules/app';
  const { getProjectConfig, setProjectConfig } = useAppStore();

  function saveProjectConfig() {
    setProjectConfig(getProjectConfig);
  }
</script>

<style scoped>
  .setting {
    width: 100%;

    li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 25px;
    }
  }
</style>
