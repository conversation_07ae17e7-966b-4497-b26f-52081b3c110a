/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 数据源API服务
 * 基于统一的fetch封装实现
 */

import { ApiClient } from './api-client'
import type { DataSource, Database, Schema, RequestOptions, PaginatedResponse } from './types'

// 数据源API响应接口
export interface DataSourceListResponse {
  success: boolean
  message?: string
  data: {
    data: any[]
    total: number
    pageNo: number
    pageSize: number
  }
}

// 数据库列表响应接口
export interface DatabaseListResponse {
  success: boolean
  message?: string
  data: any[]
}

// 模式列表响应接口
export interface SchemaListResponse {
  success: boolean
  message?: string
  data: any[]
}

export class DataSourceApi extends ApiClient {
  private static readonly RDB_BASE_URL = '/api/rdb'

  constructor() {
    super('/api/connection/datasource', {
      enableLogging: true,
      enableCache: true,
      defaultCacheTime: 5 * 60 * 1000, // 5分钟缓存
      enableRetry: true,
      defaultRetryCount: 2
    })
  }

  /**
   * 获取所有数据源列表
   */
  async getDataSourceList(options?: RequestOptions): Promise<DataSource[]> {
    try {
      const params = {
        pageNo: 1,
        pageSize: 1000,
        refresh: true
      }

      const response = await this.get<DataSourceListResponse>('/list', params, {
        cache: true,
        cacheTime: 5 * 60 * 1000, // 5分钟缓存
        ...options
      })

      // 处理响应数据格式
      if (response && response.success && response.data && response.data.data) {
        return response.data.data.map((item: any) => ({
          id: item.id,
          name: item.alias || `数据源${item.id}`,
          type: item.type,
          host: item.host,
          port: item.port,
          description: item.url,
          status: item.status,
          supportDatabase: item.supportDatabase,
          supportSchema: item.supportSchema
        }))
      }

      return []
    } catch (error) {
      console.error('获取数据源列表失败:', error)
      throw new Error(`获取数据源列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据数据源ID获取数据库列表
   */
  async getDatabaseList(dataSourceId: number, options?: RequestOptions): Promise<Database[]> {
    try {
      const params = { dataSourceId }

      // 使用RDB API端点
      const response = await this.get<DatabaseListResponse>(
        `${DataSourceApi.RDB_BASE_URL}/database/list`,
        params,
        {
          cache: true,
          cacheTime: 3 * 60 * 1000, // 3分钟缓存
          ...options
        }
      )

      // 处理响应数据格式
      if (response && response.success && response.data) {
        return response.data.map((item: any) => ({
          name: item.name,
          description: item.description,
          count: item.count
        }))
      }

      return []
    } catch (error) {
      console.error('获取数据库列表失败:', error)
      throw new Error(`获取数据库列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据数据源ID和数据库名获取模式列表
   */
  async getSchemaList(
    dataSourceId: number, 
    databaseName: string, 
    options?: RequestOptions
  ): Promise<Schema[]> {
    try {
      const params = {
        dataSourceId,
        databaseName
      }

      // 使用RDB API端点
      const response = await this.get<SchemaListResponse>(
        `${DataSourceApi.RDB_BASE_URL}/schema/list`,
        params,
        {
          cache: true,
          cacheTime: 3 * 60 * 1000, // 3分钟缓存
          ...options
        }
      )

      // 处理响应数据格式
      if (response && response.success && response.data) {
        return response.data.map((item: any) => ({
          name: item.name
        }))
      }

      return []
    } catch (error) {
      console.error('获取模式列表失败:', error)
      throw new Error(`获取模式列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 测试数据源连接
   */
  async testConnection(dataSourceId: number, options?: RequestOptions): Promise<boolean> {
    try {
      const response = await this.post<{ success: boolean }>(
        `/${dataSourceId}/test`,
        {},
        {
          retry: false, // 连接测试不重试
          ...options
        }
      )

      return response?.success === true
    } catch (error) {
      console.error('测试数据源连接失败:', error)
      return false
    }
  }

  /**
   * 根据数据源ID获取详细信息
   */
  async getDataSourceById(id: number, options?: RequestOptions): Promise<DataSource | null> {
    try {
      const response = await this.get<DataSource>(`/${id}`, undefined, {
        cache: true,
        cacheTime: 5 * 60 * 1000, // 5分钟缓存
        ...options
      })

      return response || null
    } catch (error) {
      console.error('获取数据源详情失败:', error)
      return null
    }
  }

  /**
   * 创建数据源
   */
  async createDataSource(dataSource: Omit<DataSource, 'id'>, options?: RequestOptions): Promise<DataSource> {
    try {
      const response = await this.post<DataSource>('/', dataSource, options)
      
      // 清除列表缓存
      this.clearCache()
      
      return response
    } catch (error) {
      console.error('创建数据源失败:', error)
      throw new Error(`创建数据源失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 更新数据源
   */
  async updateDataSource(id: number, dataSource: Partial<DataSource>, options?: RequestOptions): Promise<DataSource> {
    try {
      const response = await this.put<DataSource>(`/${id}`, dataSource, options)
      
      // 清除相关缓存
      this.clearCache()
      
      return response
    } catch (error) {
      console.error('更新数据源失败:', error)
      throw new Error(`更新数据源失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 删除数据源
   */
  async deleteDataSource(id: number, options?: RequestOptions): Promise<void> {
    try {
      await this.delete(`/${id}`, options)
      
      // 清除相关缓存
      this.clearCache()
    } catch (error) {
      console.error('删除数据源失败:', error)
      throw new Error(`删除数据源失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 构建工具上下文对象
   */
  buildToolContext(dataSourceId: number, databaseName?: string, schemaName?: string): Record<string, any> {
    const context: Record<string, any> = {
      dataSourceId
    }
    
    if (databaseName) {
      context.databaseName = databaseName
    }
    
    if (schemaName) {
      context.schemaName = schemaName
    }
    
    return context
  }

  /**
   * 批量获取数据源信息
   */
  async batchGetDataSources(ids: number[], options?: RequestOptions): Promise<DataSource[]> {
    try {
      const requests = ids.map(id => ({
        url: `/${id}`,
        method: 'GET' as const
      }))

      const results = await this.batchRequest<DataSource>(requests, options)
      return results.filter(Boolean)
    } catch (error) {
      console.error('批量获取数据源失败:', error)
      throw new Error(`批量获取数据源失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 搜索数据源
   */
  async searchDataSources(
    keyword: string, 
    type?: string, 
    options?: RequestOptions
  ): Promise<DataSource[]> {
    try {
      const params = this.buildQueryParams({
        keyword,
        type,
        pageNo: 1,
        pageSize: 100
      })

      const response = await this.get<DataSourceListResponse>('/search', params, options)

      if (response && response.success && response.data && response.data.data) {
        return response.data.data.map((item: any) => ({
          id: item.id,
          name: item.alias || `数据源${item.id}`,
          type: item.type,
          host: item.host,
          port: item.port,
          description: item.url,
          status: item.status,
          supportDatabase: item.supportDatabase,
          supportSchema: item.supportSchema
        }))
      }

      return []
    } catch (error) {
      console.error('搜索数据源失败:', error)
      throw new Error(`搜索数据源失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}

// 导出单例实例
export const dataSourceApi = new DataSourceApi()
