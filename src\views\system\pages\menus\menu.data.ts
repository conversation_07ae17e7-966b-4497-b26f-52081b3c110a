import { FormOptions } from '/@/components/sys/BasicForm/types';
import { TableOptions } from '/@/components/sys/BasicTable/types';

// 表格列配置
export const columns: TableOptions[] = [
  {
    label: '菜单名称',
    prop: 'title',
    width: 200,
    align: 'left',
  },
  {
    label: '图标',
    prop: 'icon',
    width: 80,
    align: 'center',
    slot: 'icon',
  },
  {
    label: '排序',
    prop: 'orderNum',
    width: 80,
    align: 'center',
  },
  {
    label: '权限标识',
    prop: 'perms',
    width: 150,
    align: 'center',
  },
  {
    label: '组件路径',
    prop: 'component',
    width: 200,
    align: 'center',
  },
  {
    label: '状态',
    prop: 'status',
    width: 80,
    align: 'center',
    slot: 'status',
  },
  {
    label: '创建时间',
    prop: 'createTime',
    width: 160,
    align: 'center',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    width: 200,
    align: 'center',
  },
];

// 搜索表单配置
export const searchFormSchema: FormOptions[] = [
  {
    field: 'title',
    label: '菜单名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入菜单名称',
    },
    span: 8,
  },
];

// 菜单表单配置
export const menuFormSchema: FormOptions[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'title',
    label: '菜单名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入菜单名称',
    },
    span: 12,
  },
  {
    field: 'name',
    label: '路由名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入路由名称',
    },
    span: 12,
  },
  {
    field: 'parentId',
    label: '上级菜单',
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择上级菜单',
      data: [],
      nodeKey: 'id',
      defaultValue: '0',
    },
    span: 12,
  },
  {
    field: 'orderNum',
    label: '显示排序',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入显示排序',
      min: 0,
    },
    span: 12,
  },
  {
    field: 'path',
    label: '路由地址',
    component: 'Input',
    componentProps: {
      placeholder: '请输入路由地址',
    },
    span: 12,
  },
  {
    field: 'component',
    label: '组件路径',
    component: 'Input',
    componentProps: {
      placeholder: '请输入组件路径',
    },
    span: 12,
  },
  {
    field: 'redirect',
    label: '重定向',
    component: 'Input',
    componentProps: {
      placeholder: '请输入重定向地址',
    },
    span: 12,
  },
  {
    field: 'menuType',
    label: '菜单类型',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择菜单类型',
      options: [
        { label: '目录', value: 'M' },
        { label: '菜单', value: 'C' },
        { label: '按钮', value: 'F' },
      ],
    },
    span: 12,
  },
  {
    field: 'visible',
    label: '显示状态',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择显示状态',
      options: [
        { label: '显示', value: '0' },
        { label: '隐藏', value: '1' },
      ],
    },
    span: 12,
  },
  {
    field: 'status',
    label: '菜单状态',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择菜单状态',
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' },
      ],
    },
    span: 12,
  },
  {
    field: 'perms',
    label: '权限标识',
    component: 'Input',
    componentProps: {
      placeholder: '请输入权限标识',
    },
    span: 12,
  },
  {
    field: 'icon',
    label: '菜单图标',
    component: 'Input',
    componentProps: {
      placeholder: '请输入菜单图标',
    },
    span: 12,
  },
  {
    field: 'isFrame',
    label: '是否外链',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否外链',
      options: [
        { label: '否', value: '0' },
        { label: '是', value: '1' },
      ],
    },
    span: 12,
  },
  {
    field: 'isCache',
    label: '是否缓存',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否缓存',
      options: [
        { label: '缓存', value: '0' },
        { label: '不缓存', value: '1' },
      ],
    },
    span: 12,
  },
  {
    field: 'query',
    label: '路由参数',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入路由参数',
      rows: 3,
    },
    span: 24,
  },
];
