<template>
  <div class="!w-6 pr-2 flex items-center text-center scroll-pane">
    <div>
      <sys-icon
        v-if="arrowShow"
        @click="tabScrollAction('left')"
        class="pt-2 m-auto cursor-pointer"
        :size="20"
        type="DArrowLeft"
      />
    </div>
  </div>
  <el-scrollbar :height="0" ref="tabScroll" class="w-full bg-white tab-scroll">
    <draggable
      ref="realScroll"
      :component="componentData"
      class="pt-1 pb-1 pr-2 inline-flex"
      v-model="bindTabRoutes"
      :animation="300"
      @end="dragEnd"
      :item-key="(key) => key"
    >
      <template #item="{ element }">
        <span
          class="pr-2 ml-2 inline-flex list-group-item items-center"
          @click="selectMenu(element)"
        >
          <div
            v-if="currentRoute.path === element.path"
            :class="currentRoute.path === element.path ? 'active' : ''"
            class="rounded-full ml-2 h-3 w-3 bg-white"
          ></div>
          <div
            :ref="(el) => setTabRef(el, element.path)"
            class="px-2 whitespace-nowrap"
            >{{ element.meta.title }}</div
          >
          <span
            v-if="!isHome(element)"
            class="align-middle cursor-pointer"
            @click.stop="closePage(element)"
            ><sys-icon class="pt-2" :size="12" type="Close"
          /></span>
        </span>
      </template>
    </draggable>
  </el-scrollbar>
  <div class="w-24 pr-2 bg-white flex items-center text-center pane-right">
    <div class="w-1/2">
      <sys-icon
        v-if="arrowShow"
        class="pt-2 m-auto cursor-pointer"
        @click="tabScrollAction('right')"
        :size="20"
        type="DArrowRight"
      />
    </div>
    <el-dropdown class="pr-4">
      <span class="el-dropdown-link">
        <sys-icon class="pt-2 m-auto cursor-pointer" :size="20" type="More" />
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item class="flex items-center" @click="refreshPage">
            <div>
              <sys-icon type="Refresh" />
            </div>
            <div>刷新</div>
          </el-dropdown-item>
          <el-dropdown-item
            :disabled="isHome(currentRoute)"
            class="flex items-center"
            @click="closePage(currentRoute)"
          >
            <div>
              <sys-icon type="Close" />
            </div>
            <div>关闭当前</div>
          </el-dropdown-item>
          <el-dropdown-item
            :disabled="isHome(currentRoute)"
            class="flex items-center"
            @click="closeLeftPage(currentRoute)"
          >
            <div>
              <sys-icon type="DArrowLeft" />
            </div>
            <div>关闭左侧</div>
          </el-dropdown-item>
          <el-dropdown-item
            class="flex items-center"
            @click="closeRightPage(currentRoute)"
          >
            <div>
              <sys-icon type="DArrowRight" />
            </div>
            <div>关闭右侧</div>
          </el-dropdown-item>
          <el-dropdown-item divided @click="closeAllPage()">关闭所有</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
  import { inject, onMounted } from 'vue';
  import { useResizeObserver } from '@vueuse/core';
  import { useElementSize } from '@vueuse/core';
  import draggable from 'vuedraggable';
  import { useTabRoutesStore } from '/@/stores/modules/tabRoutes';
  import { Menu } from '/@/stores/types';
  import { usePermissionStore } from '/@/stores/modules/permission';
  import { useRouter } from 'vue-router';
  import { PageEnum } from '/@/enums/pageEnum';
  import { useUserStore } from '/@/stores/modules/user';
  import { computed, nextTick, ref, watch } from 'vue';
  import { cloneDeep } from 'lodash-es';
  class Size {
    width: number;
    height: number;
    constructor(width = 0, height = 0) {
      this.width = width;
      this.height = height;
    }
  }

  const tabRoutesStore = useTabRoutesStore();
  const permissionStore = usePermissionStore();
  const router = useRouter();
  const userStore = useUserStore();

  const arrowShow = ref(false);
  const realScroll = ref(null);
  const tabScroll = ref(null);
  const tabScrollSize = ref<Size>(new Size());
  const realScrollSize = ref<Size>(new Size());
  const { currentRoute } = useRouter();
  const tabRefs = ref({});
  const clickTab = ref(false);
  // 设置 Tab 标签的 ref
  const setTabRef = (el, path) => {
    if (el) {
      tabRefs.value[path] = el;
    }
  };
  // 初始化时激活默认 Tab 并滚动到中间
  onMounted(() => {
    setTimeout(() => {
      scrollToTab(router.currentRoute.value.path);
    }, 10);
  });

  watch(
    () => currentRoute.value.path,
    (value) => {
      if (!clickTab.value) {
        scrollToTab(value);
      }
      clickTab.value = false;
    },
  );

  useResizeObserver(tabScroll, (entries) => {
    const entry = entries[0];
    const { width, height } = entry.contentRect;
    tabScrollSize.value.width = width;
    tabScrollSize.value.height = height;
  });
  const { width: tabScrollWidth } = useElementSize(tabScroll);

  useResizeObserver(realScroll, (entries) => {
    const entry = entries[0];
    const { width } = entry.contentRect;
    realScrollSize.value.width = width;
  });
  const { width: realScrollWidth } = useElementSize(realScroll);

  // 是否显示tab页签的左右箭头
  watch(
    () => [realScrollWidth.value, tabScrollWidth.value],
    ([realWidth, tabWidth]) => {
      if (realWidth !== 0 && tabWidth !== 0 && realWidth >= tabWidth) {
        arrowShow.value = true;
      }
    },
  );
  // 拖拽结束，更新缓存数据
  function dragEnd() {
    tabRoutesStore.setTabs(bindTabRoutes.value);
  }

  // 滚动到指定 Tab
  const scrollToTab = (path) => {
    const tabElement = tabRefs.value[path];
    const tabWidth = tabElement.offsetWidth;
    const tabOffsetLeft = tabElement.offsetLeft;
    // @ts-ignore
    tabScroll.value?.setScrollLeft(
      tabOffsetLeft - tabScrollWidth.value / 2 + tabWidth / 2,
    );
  };

  // 判断是否home页面
  function isHome(menu: Menu) {
    return [PageEnum.BASE_HOME, userStore.userInfo?.homePath].includes(
      menu.path as PageEnum,
    );
  }

  const bindTabRoutes = ref<Menu[]>([]);
  const tabRoutes = computed(() => tabRoutesStore.getTabs);
  const tabOldRoutes = ref<Menu[]>([]);
  watch(
    () => tabRoutes.value,
    (value) => {
      if (value.length > tabOldRoutes.value.length) {
        nextTick(() => {
          tabScrollAction('right');
        });
      }
      bindTabRoutes.value = value;
      nextTick(() => {
        tabOldRoutes.value = cloneDeep(value);
      });
    },
    { immediate: true, deep: true },
  );
  const componentData = ref({
    type: 'transition',
    name: 'flip-list',
  });

  // 激活对应tab
  function selectMenu(menu: Menu) {
    clickTab.value = true;
    push(menu);
  }
  // 刷新页面
  const refreshPage = inject('reload');
  function closePage(menu: Menu) {
    if (isHome(menu)) {
      return;
    }
    if (
      menu.path === permissionStore.getCurrentMenu(router.currentRoute.value.path)?.path
    ) {
      const menuIndex = tabRoutes.value.findIndex((item) => item.path === menu.path);
      if (menuIndex > -1) {
        if (menuIndex === tabRoutes.value.length - 1) {
          // 关闭最后一个tab
          push(tabRoutes.value[tabRoutes.value.length - 2]);
        } else {
          // 激活前一个
          push(tabRoutes.value[menuIndex - 1]);
        }
      }
    }
    tabRoutesStore.delTabs(menu);
  }

  function closeAllPage() {
    push(tabRoutes.value[0]);
    tabRoutesStore.delAllTabs();
  }

  function closeLeftPage(menu: Menu) {
    tabRoutesStore.delLeftTabs(menu);
  }

  function closeRightPage(menu: Menu) {
    tabRoutesStore.delRightTabs(menu);
  }

  function tabScrollAction(val: 'left' | 'right', add = false) {
    switch (val) {
      case 'left':
        // @ts-ignore
        tabScroll.value?.setScrollLeft(-20);
        break;
      case 'right':
        // @ts-ignore
        tabScroll.value?.setScrollLeft(realScrollSize.value.width);
        if (add) {
        }
        break;
    }
  }

  function push(menu: Menu) {
    router.push({
      path: menu.path,
      params: menu.params || {},
      query: menu.query || {},
    });
  }
</script>

<style scoped>
  .list-group-item:first-child {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
  }

  .list-group-item {
    height: 36px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 2px;
    cursor: move;
  }

  .flip-list-move {
    transition: transform 0.5s;
  }

  .no-move {
    transition: transform 0s;
  }

  :deep(.el-scrollbar__bar.is-horizontal) {
    height: 4px !important;
  }

  .bg-white {
    background: #f6f8f9 !important;
  }

  .scroll-pane {
    height: 46px;
    background: #f6f8f9;
    width: 5px;
  }

  .tab-scroll {
    height: 46px;
  }

  .pane-right {
    height: 46px;
  }

  .active {
    background: var(--el-color-primary) !important;
  }
</style>
