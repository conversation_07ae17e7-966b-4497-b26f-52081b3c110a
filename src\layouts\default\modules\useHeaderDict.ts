import { useDictStore } from '/@/stores/modules/dict';
import { ref, watch } from 'vue';
import { Dict } from '/#/store';

export function useHeaderDict() {
  const useDict = useDictStore();

  // 租户切换
  const tenantChange = ref<Dict>();
  // 应用切换
  const projectChange = ref<Dict>();
  // 机构切换
  const deptChange = ref<Dict>();

  watch(
    () => useDict.get('sys_header'),
    (value) => {
      tenantChange.value = value?.find(
        (item) => item.code === 'sys_header_tenant' && item.value === '1',
      );
      projectChange.value = value?.find(
        (item) => item.code === 'sys_header_project' && item.value === '1',
      );
      deptChange.value = value?.find(
        (item) => item.code === 'sys_header_dept' && item.value === '1',
      );
    },
    {
      immediate: true,
      deep: true,
    },
  );
  return { tenantChange, projectChange, deptChange };
}
