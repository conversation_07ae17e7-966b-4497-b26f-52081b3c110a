import { defineStore } from 'pinia';
import { store } from '/@/stores';
import { getSettingsStorage } from '/@/settings/settingsStorage';
import { setStorage, getStorage } from '/@/utils/storage';
import { THEME } from '/@/enums/cacheEnum';
interface SettingState {
  epTheme: ThemeColorObject;
}
export const useSettingStore = defineStore({
  id: 'setting',
  state: (): SettingState => ({
    epTheme: getStorage(THEME) || getSettingsStorage().theme,
  }),
  getters: {
    getTheme(): ThemeColorObject {
      return this.epTheme;
    },
  },
  actions: {
    setEpThemeColor(value) {
      setStorage(THEME, value);
      this.epTheme = value;
    },
  },
});

export function useSettingStoreWithOut() {
  return useSettingStore(store);
}
