import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { AxiosKnowledgeService, Knowledge } from '../api/index'
import { AxiosApiService } from '../../chatagent/api/axios-api-service'
import type { DataSource } from '../../chatagent/api/axios-api-service.d.ts'
import debounce from 'lodash/debounce'

/**
 * 知识库表格通用数据接口
 */
export interface KnowledgeTableItem {
  id?: string
  promptName?: string
  promptContent?: string
  promptType?: string
  dataSourceId?: number
  schemaName?: string
  databaseName?: string
  tableName?: string
  version?: number | string
  tableList?: { name: string }[]
  dataSources?: {
    dataSourceId?: number
    databaseName?: string
    schemaName?: string
  }
  originalData?: Partial<KnowledgeTableItem>
  isEdit?: boolean
  isNew?: boolean
  cascaderValue?: string[]
  dataSourceLoading?: boolean
  loading?: boolean
  cascaderProps?: any
}

/**
 * 表格列配置接口
 */
export interface KnowledgeTableColumn {
  prop: string
  label: string
  minWidth?: number
  width?: number
  editable?: boolean
  required?: boolean
  type?: 'text' | 'textarea' | 'dataSource' | 'table'
  placeholder?: string
}

/**
 * 知识库表格配置接口
 */
export interface KnowledgeTableConfig {
  promptType: string
  title?: string
  desc?: string
  searchPlaceholder: string
  addButtonText?: string
  columns: KnowledgeTableColumn[]
  // 兼容旧版本配置
  nameLabel?: string
  contentLabel?: string
  nameField?: string
  contentField?: string
  namePlaceholder?: string
  contentPlaceholder?: string
}

/**
 * 知识库表格通用逻辑Hook
 * @param config 表格配置
 */
export function useKnowledgeTable(config: KnowledgeTableConfig) {
  // 响应式数据
  const tableData = ref<KnowledgeTableItem[]>([])
  const loading = ref(false)
  const searchKeyword = ref('')
  const selectedRows = ref<KnowledgeTableItem[]>([])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const totalItems = ref(0)
  const dataSourceOptions = ref<{ value: number; label: string }[]>([])

  // 计算属性：是否有选中行
  const hasSelected = computed(() => selectedRows.value.length > 0)

  /**
   * 创建级联选择器配置
   * @param row 表格行数据
   * @returns 级联选择器配置对象
   */
  const createCascaderProps = (row: KnowledgeTableItem) => {
    return {
      lazy: true,
      lazyLoad: async (node: any, resolve: (children: any[]) => void) => {
        row.loading = true
        const { level } = node
        
        try {
          if (level === 0) {
            // 加载数据源列表
            const sources: DataSource[] = await AxiosApiService.getDataSourceList()
            const nodes = sources.map(s => ({
              value: s.id.toString(),
              label: s.name,
              leaf: false,
              data: { type: s.type }
            }))
            resolve(nodes)
          } else if (level === 1) {
            // 加载数据库列表
            const dataSourceId = parseInt(node.value)
            const type = node.data.data.type
            const databases = await AxiosApiService.getDatabaseList(dataSourceId)
            const needsSchema = type?.toUpperCase() === 'POSTGRESQL' || type?.toUpperCase() === 'KINGBASE'
            const nodes = databases.map(db => ({
              value: db.name,
              label: db.name,
              leaf: !needsSchema
            }))
            resolve(nodes)
          } else if (level === 2) {
            // 加载模式列表
            const dataSourceId = parseInt(node.pathValues[0])
            const databaseName = node.pathValues[1]
            
            const schemas = await AxiosApiService.getSchemaList(dataSourceId, databaseName)
            const nodes = schemas.map(s => ({
              value: s.name,
              label: s.name,
              leaf: true
            }))
            resolve(nodes)
          } else {
            resolve([])
          }
        } catch (error) {
          console.error('Failed to load cascader data:', error)
          ElMessage.error('加载数据失败')
          resolve([])
        } finally {
          row.loading = false
        }
      }
    }
  }

  /**
   * 加载表格数据
   */
  const loadTableData = async (promptName?:string) => {
    loading.value = true
    try {
      const response = await AxiosKnowledgeService.getList(config.promptType, promptName, currentPage.value, pageSize.value)
      
      if (response && response.records) {
        tableData.value = response.records.map(item => {
          const dataSourceInfo = {
            dataSourceId: item.dataSourceId,
            databaseName: item.databaseName,
            schemaName: item.schemaName
          }
          
          return {
            ...item,
            isEdit: false,
            originalData: { ...item },
            dataSources: dataSourceInfo
          }
        })
        totalItems.value = response.total || 0
      }
      
      // 加载数据源列表
      const dataSources = await AxiosApiService.getDataSourceList()
      dataSourceOptions.value = dataSources.map(source => ({
        value: source.id,
        label: source.name
      }))
    } catch (error) {
      console.error('Failed to load data:', error)
      ElMessage.error('加载数据失败')
    } finally {
      loading.value = false
    }
  }

  const debouncedLoadTableData = debounce((keyword: string) => {
    currentPage.value = 1
    loadTableData(keyword)
  }, 1000) // 300ms 可根据实际需求调整

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    debouncedLoadTableData(searchKeyword.value)
  }

  /**
   * 处理选择变化
   */
  const handleSelectionChange = (rows: KnowledgeTableItem[]) => {
    selectedRows.value = rows
  }

  /**
   * 处理添加新行
   */
  const handleAdd = () => {
    try {
      const newRow: KnowledgeTableItem = {
        id: Date.now().toString(),
        promptName: '',
        promptContent: '',
        isNew: true,
        isEdit: true,
        tableList: [],
        dataSources: {
          dataSourceId: undefined,
          databaseName: undefined,
          schemaName: undefined
        },
        cascaderValue: [],
        loading: false
      }
      
      // 使用公共函数初始化级联选择器配置
      newRow.cascaderProps = createCascaderProps(newRow)
      
      tableData.value.unshift(newRow)
    } catch (error) {
      console.error('Error in handleAdd:', error)
      ElMessage.error('添加新行失败')
    }
  }

  /**
   * 处理编辑
   */
  const handleEdit = (row: KnowledgeTableItem) => {
    // 保存原始数据，以便取消时恢复
    row.originalData = { ...row }
    row.isEdit = true
    row.dataSourceLoading = true
    row.loading = false

    // 确保 dataSources 存在且包含完整的数据源信息
    if (!row.dataSources) {
      row.dataSources = {
        dataSourceId: undefined,
        databaseName: undefined,
        schemaName: undefined
      }
    }

    // 初始化 cascaderValue 以支持回显
    row.cascaderValue = getCascaderValue(row) as any

    // 使用公共函数设置级联选择器配置
    row.cascaderProps = createCascaderProps(row)
  }

  /**
   * 处理保存
   */
  const handleSave = async (row: KnowledgeTableItem) => {
    console.log(config)
    const nameLabel = config.title || '名称';
    const contentLabel = config.desc || '内容';
    const errors: string[] = [];
    if (!row.promptName?.trim()) errors.push(nameLabel);
    if (!row.promptContent?.trim()) errors.push(contentLabel);
    if (!row.dataSources || !row.dataSources.dataSourceId) errors.push('数据源');
    if (errors.length) {
      ElMessage.warning(`${errors.join('、')}不能为空`);
      return;
    }
    try {
      loading.value = true
      const saveData: Knowledge = {
        id: row.id || '',
        promptName: row.promptName || '',
        promptContent: row.promptContent || '',
        promptType: config.promptType,
        dataSourceId: row.dataSources?.dataSourceId || 0,
        schemaName: row.dataSources?.schemaName || '',
        databaseName: row.dataSources?.databaseName || '',
        tableName: row.tableName || '',
        version: row.version || 0
      }

      // 根据是否是新增行调用不同的方法
      await (row.isNew 
        ? AxiosKnowledgeService.add(saveData)
        : AxiosKnowledgeService.update(saveData))

      ElMessage.success('保存成功')
      row.isEdit = false
      row.isNew = false
      
      // 重新加载数据
      await loadTableData(searchKeyword.value)
    } catch (error) {
      console.error('Save failed:', error)
      ElMessage.error('保存失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理取消
   */
  const handleCancel = (row: KnowledgeTableItem) => {
    if (row.isNew) {
      // 如果是新增的行，直接从表格中移除
      const index = tableData.value.findIndex(item => item === row)
      if (index !== -1) {
        tableData.value.splice(index, 1)
      }
    } else {
      // 恢复原始数据
      if (row.originalData) {
        Object.assign(row, row.originalData)
      }
      row.isEdit = false
    }
  }

  /**
   * 处理删除
   */
  const handleDelete = (row: KnowledgeTableItem) => {
    ElMessageBox.confirm(
      '确定要删除这一项吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      try {
        if (row.id) {
          await AxiosKnowledgeService.delete([row.id])
          ElMessage.success('删除成功')
          loadTableData(searchKeyword.value)
        } else {
          ElMessage.warning('无效的记录，无法删除')
        }
      } catch (error) {
        console.error('Delete failed:', error)
        ElMessage.error('删除失败')
      }
    }).catch(() => {
      // 用户取消删除
    })
  }

  /**
   * 处理批量删除
   */
  const handleBatchDelete = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请先选择要删除的项')
      return
    }
    
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRows.value.length} 项吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const ids = selectedRows.value
        .map(row => row.id)
        .filter((id): id is string => id !== undefined && typeof id === 'string')

      await AxiosKnowledgeService.delete(ids)
      ElMessage.success('删除成功')
      loadTableData(searchKeyword.value)
    } catch (error) {
      console.error('Batch delete failed:', error)
      ElMessage.error('删除失败')
    }
  }

  /**
   * 处理页码变化
   */
  const handleCurrentChange = (page: number) => {
    currentPage.value = page
    loadTableData(searchKeyword.value)
  }

  /**
   * 处理每页条数变化
   */
  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    loadTableData(searchKeyword.value)
  }

  /**
   * 处理数据源变化
   */
  const handleDataSourceChange = (row: KnowledgeTableItem, value: { 
    dataSourceId?: number, 
    databaseName?: string, 
    schemaName?: string 
  }) => {
    // 重置相关联的字段
    row.dataSources = {
      dataSourceId: value.dataSourceId,
      databaseName: value.databaseName,
      schemaName: value.schemaName
    }
    row.tableName = undefined
    row.tableList = []
  }

  /**
   * 加载数据表列表
   */
  const loadTableList = async (row: KnowledgeTableItem) => {
    const dataSourceId = row.dataSources?.dataSourceId
    const databaseName = row.dataSources?.databaseName

    if (!dataSourceId || !databaseName) {
      ElMessage.warning('请先选择数据源和数据库')
      return
    }

    try {
      const tables = await AxiosApiService.getTableList(
        dataSourceId, 
        databaseName, 
        row.dataSources?.schemaName || ''
      )
      
      row.tableList = tables.data
    } catch (error) {
      console.error('加载数据表失败:', error)
      ElMessage.error('加载数据表失败')
    }
  }   

  /**
   * 处理数据表选择变化
   */
  const handleTableChange = (row: KnowledgeTableItem) => {
    console.log('选择的数据表:', row.tableName)
  }

  /**
   * 获取数据源标签
   */
  const getDataSourceLabel = (dataSource: { dataSourceId?: number; databaseName?: string; schemaName?: string }) => {
    const source = dataSourceOptions.value.find(opt => opt.value === (typeof dataSource === 'object' ? dataSource.dataSourceId : dataSource))
    
    if (source) {
      const databaseName = typeof dataSource === 'object' ? dataSource.databaseName : ''
      const schemaName = typeof dataSource === 'object' ? dataSource.schemaName : ''
      const label = `${source.label}/${databaseName}/${schemaName}`.replace(/\/+$/, '')
      
      return label
    }
    
    return '未选择'
  }

  /**
   * 获取级联选择器值
   */
  const getCascaderValue = (row: KnowledgeTableItem) => {
    if (!row.dataSources?.dataSourceId) return []
    const val = [row.dataSources.dataSourceId.toString(), row.dataSources.databaseName]
    if (row.dataSources.schemaName) val.push(row.dataSources.schemaName)
    return val
  }

  /**
   * 处理级联选择变更
   */
  const handleCascaderChange = (row: KnowledgeTableItem, value: any) => {
    if (!value || value.length < 2) {
      row.dataSources = {}
      return
    }
    const [dsId, db, schema] = value
    row.dataSources = {
      dataSourceId: parseInt(dsId),
      databaseName: db,
      schemaName: schema
    }
    handleDataSourceChange(row, row.dataSources)
  }

  /**
   * 处理批量操作命令
   */
  const handleBatchCommand = (command: string) => {
    switch (command) {
      case 'exportAll':
        ElMessage.success('已触发全部导出操作')
        break
      case 'exportSelected':
        if (selectedRows.value.length === 0) {
          ElMessage.warning('请先选择要导出的项')
          return
        }
        const ids = selectedRows.value
          .map(row => row.id)
          .filter((id): id is string => id !== undefined && typeof id === 'string')
        ElMessage.success(`已触发批量导出操作，选中ID: ${ids.join(', ')}`)
        break
      case 'import':
        ElMessage.warning('导入功能暂未开放')
        break
      case 'delete':
        handleBatchDelete()
        break
    }
  }

  // 组件挂载时加载数据
  onMounted(() => {
    loadTableData(searchKeyword.value)
  })

  // 监听 searchKeyword，节流调用接口
  const fetchTableDataByKeyword = debounce((keyword: string) => {
    currentPage.value = 1
    loadTableData(keyword)
  }, 600)

  watch(searchKeyword, (val) => {
    fetchTableDataByKeyword(val)
  })

  return {
    // 响应式数据
    tableData,
    loading,
    searchKeyword,
    selectedRows,
    currentPage,
    pageSize,
    totalItems,
    dataSourceOptions,
    
    // 计算属性
    hasSelected,
    
    // 方法
    loadTableData,
    handleSearch,
    handleSelectionChange,
    handleAdd,
    handleEdit,
    handleSave,
    handleCancel,
    handleDelete,
    handleBatchDelete,
    handleCurrentChange,
    handleSizeChange,
    handleDataSourceChange,
    loadTableList,
    handleTableChange,
    getDataSourceLabel,
    getCascaderValue,
    handleCascaderChange,
    handleBatchCommand
  }
}