import { defHttp } from '/@/utils/axios';

enum Api {
  agents = '/api/agents',
  tools = '/api/agents/tools',
}

export const getAgentsApi = () => defHttp.get({ url: `${Api.agents}` });
// tools
export const getToolsApi = () => defHttp.get({ url: `${Api.tools}` });

export const addAgentAPI = (params) => defHttp.post({ url: Api.agents, params });

export const putAgentAPI = (id: string, params) =>
  defHttp.put({ url: `${Api.agents}/${id}`, params });

export const deleteAgentAPI = (id: string) =>
  defHttp.delete({ url: `${Api.agents}/${id}` });

// MCP工具管理相关API
enum McpApi {
  mcpList = '/api/mcp/list',
  mcpAdd = '/api/mcp/add',
  mcpRemove = '/api/mcp/remove',
  mcpTools = '/api/mcp/tools',
}

// 获取MCP服务器列表
export const getMcpServersApi = () => defHttp.get({ url: McpApi.mcpList });

// 添加MCP服务器
export const addMcpServerApi = (params) => {
  // 确保参数格式正确
  const requestData = {
    mcpServerName: params.mcpServerName || params.name,
    connectionType: params.connectionType || 'STUDIO',
    configJson: params.configJson || params.config,
  };
  return defHttp.post({ url: McpApi.mcpAdd, params: requestData });
};

// 删除MCP服务器
export const removeMcpServerApi = (id: string) =>
  defHttp.get({ url: `${McpApi.mcpRemove}?id=${id}` });

// 获取所有可用工具
export const getMcpToolsApi = () => defHttp.get({ url: McpApi.mcpTools });

// 智能体编排相关API
enum WorkflowApi {
  planTemplateList = '/api/plan-template/list',
  planTemplateCreate = '/api/plan-template/save',
  planTemplateUpdate = '/api/plan-template/update',
  planTemplateDelete = '/api/plan-template/delete',
  planTemplateDetail = '/api/plan-template/get',
  planTemplatePublish = '/api/plan-template/publish',
}

// 获取编排模板列表
export const getWorkflowListApi = () =>
  defHttp.get({ url: WorkflowApi.planTemplateList });

// 创建编排模板
export const createWorkflowApi = (params) =>
  defHttp.post({ url: WorkflowApi.planTemplateCreate, params });

// 更新编排模板
export const updateWorkflowApi = (params) =>
  defHttp.post({ url: WorkflowApi.planTemplateUpdate, params });

// 删除编排模板
export const deleteWorkflowApi = (id: string) =>
  defHttp.delete({ url: `${WorkflowApi.planTemplateDelete}/${id}` });

// 获取编排模板详情
export const getWorkflowDetailApi = (params) =>
  defHttp.get({ url: `${WorkflowApi.planTemplateDetail}`, params });

export const planTemplatePublishApi = (id, status) =>
  defHttp.put({
    url: `${WorkflowApi.planTemplatePublish}/${id}?isPublished=${status}`,
  });
// planId
enum GlobalApi {
  manus = '/api/config/group/manus',
  updateManus = '/api/config/batch-update',
}
// /api/config/group/manus
export const getManusListApi = () => defHttp.get({ url: GlobalApi.manus });
export const putManusListApi = (params) =>
  defHttp.post({ url: GlobalApi.updateManus, params });
