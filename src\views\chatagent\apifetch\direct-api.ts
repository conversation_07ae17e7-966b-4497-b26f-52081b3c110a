/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 直接执行API服务
 * 基于统一的fetch封装实现
 */

import { ApiClient } from './api-client'
import type { 
  RequestOptions, 
  DirectStreamingRequest, 
  DirectStreamEvent, 
  StreamHandler,
  StandardApiResponse 
} from './types'

// 直接执行响应接口
export interface DirectExecuteResponse extends StandardApiResponse<{
  planId: string
  status: string
  result?: any
}> {}

// 流式响应处理器类型
export type DirectStreamHandler = StreamHandler<DirectStreamEvent>

export class DirectApi extends ApiClient {
  constructor() {
    super('/api', {
      enableLogging: true,
      enableCache: false, // 直接执行不缓存
      enableRetry: true,
      defaultRetryCount: 1, // 减少重试次数
      timeout: 60000 // 60秒超时
    })
  }

  /**
   * 直接发送任务（直接执行模式）
   */
  async sendMessage(query: string, options?: RequestOptions): Promise<any> {
    try {
      const response = await this.post<DirectExecuteResponse>(
        '/executor/execute',
        { query },
        {
          timeout: 60000, // 60秒超时
          ...options
        }
      )

      return response
    } catch (error) {
      console.error('直接发送任务失败:', error)
      throw new Error(`发送任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 发送消息并获取流式响应（使用新的/direct端点）
   */
  async sendMessageWithStreamingDirect(
    planTemplateId: string,
    request: DirectStreamingRequest,
    handler: DirectStreamHandler,
    options?: RequestOptions
  ): Promise<void> {
    try {
      console.log('🚀 开始直接流式请求:', { planTemplateId, request })

      const url = `/streaming-events/template/${encodeURIComponent(planTemplateId)}/direct`
      
      const config = {
        url,
        method: 'POST' as const,
        data: request,
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        timeout: 0 // 流式请求不设置超时
      }

      // 使用流式处理器
      await this.streamHandler.handleSSEStream<DirectStreamEvent>(config, handler)
    } catch (error) {
      console.error('直接流式请求失败:', error)
      handler.onError(error as Error)
    }
  }

  /**
   * 创建流式请求控制器
   */
  createStreamController(): AbortController {
    return this.streamHandler.createStreamController()
  }

  /**
   * 取消流式请求
   */
  cancelStream(controller: AbortController, reason?: string): void {
    this.streamHandler.cancelStream(controller, reason)
  }

  /**
   * 获取执行详情
   */
  async getExecutionDetails(planId: string, options?: RequestOptions): Promise<any | null> {
    try {
      const response = await this.get<any>(`/executor/details/${planId}`, undefined, {
        cache: true,
        cacheTime: 30 * 1000, // 30秒缓存
        ...options
      })

      return response
    } catch (error: any) {
      // 404 返回 null
      if (error?.status === 404) {
        return null
      }
      
      console.error('获取执行详情失败:', error)
      return null
    }
  }

  /**
   * 提交用户表单输入
   */
  async submitFormInput(planId: string, formData: any, options?: RequestOptions): Promise<any> {
    try {
      const response = await this.post<any>(
        `/executor/submit-input/${planId}`,
        formData,
        options
      )

      return response
    } catch (error) {
      console.error('提交表单输入失败:', error)
      throw new Error(`提交表单输入失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 停止执行
   */
  async stopExecution(planId: string, options?: RequestOptions): Promise<void> {
    try {
      await this.post<void>(`/executor/stop/${planId}`, {}, options)
    } catch (error) {
      console.error('停止执行失败:', error)
      throw new Error(`停止执行失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取执行状态
   */
  async getExecutionStatus(planId: string, options?: RequestOptions): Promise<string | null> {
    try {
      const response = await this.get<{ status: string }>(`/executor/status/${planId}`, undefined, {
        cache: false, // 状态查询不缓存
        ...options
      })

      return response?.status || null
    } catch (error) {
      console.error('获取执行状态失败:', error)
      return null
    }
  }

  /**
   * 获取执行日志
   */
  async getExecutionLogs(planId: string, options?: RequestOptions): Promise<string[]> {
    try {
      const response = await this.get<{ logs: string[] }>(`/executor/logs/${planId}`, undefined, {
        cache: true,
        cacheTime: 10 * 1000, // 10秒缓存
        ...options
      })

      return response?.logs || []
    } catch (error) {
      console.error('获取执行日志失败:', error)
      return []
    }
  }

  /**
   * 重新执行任务
   */
  async retryExecution(planId: string, options?: RequestOptions): Promise<any> {
    try {
      const response = await this.post<any>(`/executor/retry/${planId}`, {}, options)
      return response
    } catch (error) {
      console.error('重新执行任务失败:', error)
      throw new Error(`重新执行任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 批量执行任务
   */
  async batchExecute(queries: string[], options?: RequestOptions): Promise<any[]> {
    try {
      const requests = queries.map(query => ({
        url: '/executor/execute',
        method: 'POST' as const,
        data: { query }
      }))

      const results = await this.batchRequest<DirectExecuteResponse>(requests, options)
      return results.filter(Boolean)
    } catch (error) {
      console.error('批量执行任务失败:', error)
      throw new Error(`批量执行任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取执行历史
   */
  async getExecutionHistory(
    userId?: string,
    page: number = 0,
    size: number = 20,
    options?: RequestOptions
  ): Promise<any[]> {
    try {
      const params = this.buildQueryParams({
        userId,
        page,
        size
      })

      const response = await this.get<{ content: any[] }>('/executor/history', params, {
        cache: true,
        cacheTime: 60 * 1000, // 1分钟缓存
        ...options
      })

      return response?.content || []
    } catch (error) {
      console.error('获取执行历史失败:', error)
      return []
    }
  }

  /**
   * 删除执行记录
   */
  async deleteExecution(planId: string, options?: RequestOptions): Promise<void> {
    try {
      await this.delete(`/executor/${planId}`, options)
    } catch (error) {
      console.error('删除执行记录失败:', error)
      throw new Error(`删除执行记录失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 导出执行结果
   */
  async exportExecution(planId: string, format: 'json' | 'csv' | 'xlsx' = 'json', options?: RequestOptions): Promise<Blob> {
    try {
      const params = { format }
      
      const response = await this.get<Blob>(`/executor/export/${planId}`, params, {
        ...options,
        // 这里需要特殊处理，因为返回的是文件
      })

      return response
    } catch (error) {
      console.error('导出执行结果失败:', error)
      throw new Error(`导出执行结果失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取执行统计信息
   */
  async getExecutionStats(
    startDate?: string,
    endDate?: string,
    options?: RequestOptions
  ): Promise<any> {
    try {
      const params = this.buildQueryParams({
        startDate,
        endDate
      })

      const response = await this.get<any>('/executor/stats', params, {
        cache: true,
        cacheTime: 5 * 60 * 1000, // 5分钟缓存
        ...options
      })

      return response
    } catch (error) {
      console.error('获取执行统计信息失败:', error)
      return null
    }
  }

  /**
   * 创建简单的流式处理器
   */
  createSimpleStreamHandler(
    onEvent: (event: DirectStreamEvent) => void,
    onError?: (error: Error) => void,
    onComplete?: () => void
  ): DirectStreamHandler {
    return {
      onEvent,
      onError: onError || ((error) => console.error('流式处理错误:', error)),
      onComplete: onComplete || (() => console.log('流式处理完成'))
    }
  }
}

// 导出单例实例
export const directApi = new DirectApi()
