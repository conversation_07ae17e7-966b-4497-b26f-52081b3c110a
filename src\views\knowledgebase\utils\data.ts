// 数据源选项
export const dataSources = [
  {
    value: 'database1',
    label: '数据源1',
  },
  {
    value: 'database2',
    label: '数据源2',
  },
  {
    value: 'database3',
    label: '数据源3',
  }
];

// 名词解释数据
export const termExplanations = [
  {
    id: 1,
    term: 'SQL',
    description: '结构化查询语言(Structured Query Language)的缩写，是一种特定目的编程语言，用于管理关系数据库管理系统。',
    dataSources: ['database1', 'database2']
  },
  {
    id: 2,
    term: 'API',
    description: '应用程序接口(Application Programming Interface)的缩写，是一组定义、程序及协议的集合，用于构建应用软件和服务。',
    dataSources: ['database2']
  },
  {
    id: 3,
    term: 'JSON',
    description: 'JavaScript对象表示法(JavaScript Object Notation)的缩写，是一种轻量级的数据交换格式。',
    dataSources: ['database1', 'database3']
  },
  {
    id: 4,
    term: 'REST',
    description: '表述性状态转移(Representational State Transfer)的缩写，是一种软件架构风格。',
    dataSources: ['database3']
  },
  {
    id: 5,
    term: 'ORM',
    description: '对象关系映射(Object Relational Mapping)的缩写，是一种程序设计技术，用于实现面向对象编程语言里不同类型系统的数据之间的转换。',
    dataSources: ['database1']
  }
];

// 业务逻辑解释数据
export const businessLogicExplanations = [
  {
    id: 1,
    title: '用户注册流程',
    description: '用户注册需要验证邮箱，设置密码，并同意用户协议。',
    dataSources: ['database1']
  },
  {
    id: 2,
    title: '订单处理流程',
    description: '订单创建后需要经过支付确认、库存检查、发货等步骤。',
    dataSources: ['database2', 'database3']
  }
];

// 案例优化数据
export const caseOptimizations = [
  {
    id: 1,
    title: '数据库查询优化案例',
    description: '通过添加索引和优化SQL语句，将查询时间从2秒降低到0.1秒。',
    dataSources: ['database1']
  },
  {
    id: 2,
    title: 'API响应时间优化',
    description: '通过实现缓存机制，将API响应时间降低了80%。',
    dataSources: ['database2']
  }
];