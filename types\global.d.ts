declare interface Fn<T = any, R = T> {
  (...arg: T[]): R;
}

declare type Nullable<T> = T | null;

declare type Recordable<T = any> = Record<string, T>;

declare interface ViteEnv {
  VITE_APP_BASE_URL: string;
  VITE_APP_BASE_URL_PREFIX: string;
  VITE_PORT: number;
  VITE_APP_TITLE: string;
  VITE_APP_STATIC_URL: string;
  VITE_PUBLIC_PATH: string;
  VITE_PROXY: [string, string][];
  VITE_MOCK: boolean;
  VITE_MICRO: boolean;
  VITE_APP_SHORT_NAME: string;
  VITE_LOGIN_PAGE: string;
  VITE_PROJECT_BASE_URL: string;
}

declare type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends Object ? DeepPartial<T[P]> : T[P];
};

declare interface Window {
  eventCenterForAppNameVite: any;
  __MICRO_APP_NAME__: string;
  __MICRO_APP_ENVIRONMENT__: string;
  __MICRO_APP_BASE_APPLICATION__: string;
}

declare interface __APP_INFO__ {
  pkg: {
    name: string;
    version: string;
    dependencies: Recordable<string>;
    devDependencies: Recordable<string>;
  };
}
