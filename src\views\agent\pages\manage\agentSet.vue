<template>
  <div class="agent-set-content">
    <div class="agent-set-padding">
      <div class="agent-set-top">
        <el-input
          v-model="searchText"
          style="width: 240px; margin-right: 12px"
          placeholder="搜索Agent"
          :prefix-icon="Search"
          clearable
          @clear="handleSearch"
        />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button type="primary" @click="handleAdd" :icon="Plus">新建</el-button>
      </div>
      <div class="card-content" v-loading="dataLoading">
        <el-row :gutter="20">
          <el-col
            v-for="(item, index) in filteredItems"
            :key="index"
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <div class="card">
              <p class="card-top">
                <span class="card-top-name">{{ item.name }}</span>
                <span class="card-top-id">{{ item.id }}</span>
              </p>
              <div class="card-description">
                {{ item.description }}
              </div>
              <div class="card-tools">
                <el-tag
                  v-for="it in item.availableTools.slice(0, 3)"
                  :key="it"
                  type="primary"
                  style="margin-right: 6px"
                >
                  {{ it }}
                </el-tag>
                <el-tag v-if="item.availableTools.length > 3" type="primary">
                  +{{ item.availableTools.length - 3 }}
                </el-tag>
                <!-- <span
                  class="tool-tag"
                  v-for="it in item.availableTools.slice(0, 3)"
                  :key="it"
                  >{{ it }}</span
                >
                <span class="tool-tag" v-if="item.availableTools.length > 3">
                  +{{ item.availableTools.length - 3 }}
                </span> -->
              </div>
              <div class="card-bottom">
                <div class="card-bottom-actions">
                  <el-button @click="handleEdit(item)" :icon="Edit">编辑</el-button>
                  <el-popconfirm
                    :icon="InfoFilled"
                    title="确定要删除吗?"
                    @confirm="handleDelete(item.id)"
                  >
                    <template #reference>
                      <el-button :icon="DeleteFilled">删除</el-button>
                    </template>
                  </el-popconfirm>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <el-drawer v-model="drawer" size="600px">
      <template #header>
        <p class="drawer-title">{{ formData.id === '' ? '新建Agent' : '编辑Agent' }}</p>
      </template>
      <template #default>
        <div>
          <el-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-width="auto"
            label-position="top"
          >
            <el-form-item label="Agent名称" prop="name">
              <el-input v-model="formData.name" />
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input v-model="formData.description" :rows="3" type="textarea" />
            </el-form-item>
            <el-form-item label="下一步提示词" prop="nextStepPrompt">
              <el-input v-model="formData.nextStepPrompt" :rows="10" type="textarea" />
            </el-form-item>
            <el-form-item label="类名" prop="className">
              <el-input v-model="formData.className" />
            </el-form-item>
            <el-form-item label="可用工具" prop="availableTools">
              <!-- <el-input v-model="formData.name" /> -->
              <div class="form-tools">
                <!-- <span
                  class="tool-tag"
                  v-for="item in formData.availableTools"
                  :key="item"
                  >{{ item }}</span
                > -->

                <div v-if="formData.availableTools.length > 0">
                  <el-tag
                    v-for="tag in formData.availableTools"
                    :key="tag"
                    closable
                    type="primary"
                    style="margin-right: 6px"
                    @close="closeTag(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
                <div v-else> 暂未选择任何工具 </div>
              </div>

              <div class="form-tools-btn">
                <el-button @click="handleAddTools">添加工具</el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" :loading="loading" @click="confirmClick(formRef)"
            >保存</el-button
          >
        </div>
      </template>
    </el-drawer>

    <el-dialog
      v-model="dialogVisible"
      width="800"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <template #header>
        <p class="drawer-title">选择工具</p>
      </template>
      <!-- <span>This is a message</span> -->
      <div>
        <div class="top-total">
          <span
            >共 {{ groupedTools.length }} 个MCP服务，{{ tools.length }} 个工具（已选择
            {{ toolOpenNum }} 个）</span
          >
        </div>
        <div class="collapse-content">
          <el-collapse v-model="activeNames" expand-icon-position="left">
            <el-collapse-item
              v-for="(item, index) in groupedTools"
              :name="index"
              :key="item.serviceGroup"
            >
              <template #title>
                <div class="collapse-header">
                  <div class="collapse-header-left">
                    <span>{{ item.serviceGroup }}</span>
                  </div>
                  <div class="collapse-header-right" style="margin-right: 12px">
                    <el-checkbox
                      v-model="item.checkedGroup"
                      :indeterminate="item.isIndeterminate"
                      @change="(value) => handleCheckAllChange(value, index)"
                      @click.stop
                    >
                      启用全部
                    </el-checkbox>
                  </div>
                </div>
              </template>
              <div class="tool-group-content">
                <div
                  class="tool-item"
                  v-for="it in item.data"
                  :key="it.key"
                  :class="{ selected: it.checked }"
                >
                  <div>
                    <div>
                      {{ it.name }}
                    </div>

                    <div class="tool-item-description">
                      <!-- <el-tooltip
                      :content="it.description"
                      placement="top"
                      popper-class="custom-tooltip-class"
                    > -->
                      <span> {{ it.description }}</span>
                      <!-- </el-tooltip> -->
                    </div>
                  </div>
                  <div style="margin-left: 40px">
                    <el-checkbox
                      v-model="it.checked"
                      @change="(value) => handleCheckChange(value, index)"
                    />
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="changeTools"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import {
    Search,
    InfoFilled,
    Plus,
    Edit,
    DeleteFilled,
  } from '@element-plus/icons-vue';
  import {
    getAgentsApi,
    getToolsApi,
    addAgentAPI,
    putAgentAPI,
    deleteAgentAPI,
  } from '../../api/agentSet';
  import { cloneDeep } from 'lodash-es';
  import type { FormInstance, FormRules } from 'element-plus';
  const agentSetList = ref<any[]>([]);
  const filteredItems = ref<any[]>([]);
  const searchText = ref('');
  const drawer = ref(false);
  const dialogVisible = ref(false);

  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const formData = ref({
    name: '',
    description: '',
    nextStepPrompt: '',
    className: '',
    availableTools: [],
    id: '',
    // availableTools: ['browser_use'],
  });
  const dataLoading = ref(false);
  const rules = ref<FormRules>({
    name: { required: true, message: '请输入Agent名称', trigger: 'blur' },
    description: { required: true, message: '请输入描述', trigger: 'blur' },
    nextStepPrompt: { required: true, message: '请输入下一步提示词', trigger: 'blur' },
    className: { required: true, message: '请输入类名', trigger: 'blur' },
    // name: { required: true, message: '请输入Agent名称', trigger: 'blur' },
    availableTools: {
      type: 'array',
      required: true,
      message: '请选择工具',
      trigger: ['change', 'blur'],
    },
  });

  const activeNames = ref([0]);

  const tools = ref<any[]>([]);
  const groupedTools = ref<any[]>([]);
  const toolOpenNum = ref(0);
  //   const filteredToolItems = ref<any[]>([]);

  const getAgents = async () => {
    dataLoading.value = true;
    const res = await getAgentsApi();
    console.log(res);
    agentSetList.value = res;
    dataLoading.value = false;
    filteredItems.value = cloneDeep(agentSetList.value);
  };
  getAgents();

  const getTools = async () => {
    const res = await getToolsApi();
    console.log(res);
    tools.value = res;

    // filteredToolItems.value = cloneDeep(tools.value);

    groupedTools.value = transformTools(tools.value);
    console.log('groupedTools');
    console.log(groupedTools.value);
  };

  getTools();

  const transformTools = (toolsArray) => {
    const grouped = toolsArray.reduce((acc, tool) => {
      acc[tool.serviceGroup] = acc[tool.serviceGroup] || [];
      tool.checked = false;
      acc[tool.serviceGroup].push(tool);
      return acc;
    }, {});

    return Object.entries(grouped).map(([serviceGroup, data]) => ({
      serviceGroup,
      data,
      isIndeterminate: false, // isIndeterminate  true 半选
      checkedGroup: false,
      checkedArr: [],
    }));
  };

  const handleSearch = () => {
    if (!searchText.value.trim()) {
      filteredItems.value = [...agentSetList.value];
      return;
    }

    const searchTerm = searchText.value.toLowerCase();
    filteredItems.value = agentSetList.value.filter((item) =>
      item.name.toLowerCase().includes(searchTerm),
    );
  };

  const cancelClick = () => {
    drawer.value = false;
  };
  const confirmClick = async (formEl: FormInstance | undefined) => {
    // drawer.value = false;
    if (!formEl) return;
    await formEl.validate((valid) => {
      if (valid) {
        loading.value = true;
        console.log('submit!');
        // console.log(fields);
        console.log(formData.value);
        // addAgentAPI

        if (!formData.value.id) {
          addAgentAPI(formData.value)
            .then(() => {
              drawer.value = false;
              getAgents();
              loading.value = false;
              ElMessage({
                type: 'success',
                message: '新建成功！',
              });
            })
            .catch(() => {
              loading.value = false;
            });
        } else {
          //

          putAgentAPI(formData.value.id, formData.value)
            .then(() => {
              drawer.value = false;
              getAgents();
              loading.value = false;
              ElMessage({
                type: 'success',
                message: '编辑成功！',
              });
            })
            .catch(() => {
              loading.value = false;
            });
        }
      }
    });
  };
  const handleAdd = () => {
    drawer.value = true;
    formData.value = {
      name: '',
      description: '',
      nextStepPrompt: '',
      className: '',
      // availableTools: [],
      availableTools: [],
      id: '',
    };
    // const keySet = new Set(formData.value.availableTools);
    // groupedTools.value.forEach((group) => {
    //   group.data.forEach((item) => {
    //     if (keySet.has(item.key)) {
    //       item.checked = true;
    //     }
    //   });
    // });

    // for (let i = 0; i < groupedTools.value.length; i++) {
    //   const status = checkArrayProperty(groupedTools.value[i].data, 'checked');
    //   if (status === 'all_true' || status === 'all_false' || status === 'empty') {
    //     groupedTools.value[i].isIndeterminate = false;
    //     groupedTools.value[i].checkedGroup = status === 'all_true' ? true : false;
    //   } else {
    //     groupedTools.value[i].isIndeterminate = true;
    //   }
    // }
    // toolOpenNum.value = groupedTools.value.reduce(
    //   (total, item) => total + item.data.filter((obj) => obj.checked === true).length,
    //   0,
    // );
    // console.log(groupedTools.value);
  };

  const toolShowFn = (availableTools) => {
    const keySet = new Set(availableTools);
    groupedTools.value.forEach((group) => {
      group.data.forEach((item) => {
        if (keySet.has(item.key)) {
          item.checked = true;
        } else {
          item.checked = false;
        }
      });
    });

    for (let i = 0; i < groupedTools.value.length; i++) {
      const status = checkArrayProperty(groupedTools.value[i].data, 'checked');
      if (status === 'all_true' || status === 'all_false' || status === 'empty') {
        groupedTools.value[i].isIndeterminate = false;
        groupedTools.value[i].checkedGroup = status === 'all_true' ? true : false;
      } else {
        groupedTools.value[i].isIndeterminate = true;
      }
    }
    toolOpenNum.value = groupedTools.value.reduce(
      (total, item) => total + item.data.filter((obj) => obj.checked === true).length,
      0,
    );
    console.log(groupedTools.value);
  };

  const handleAddTools = () => {
    dialogVisible.value = true;
  };

  const handleCheckAllChange = (value, index) => {
    groupedTools.value[index].data.forEach((item) => {
      item.checked = value;
    });
    groupedTools.value[index].isIndeterminate = false;
    toolOpenNum.value = groupedTools.value.reduce(
      (total, item) => total + item.data.filter((obj) => obj.checked === true).length,
      0,
    );
  };
  const handleCheckChange = (value, index) => {
    console.log(value);
    const status = checkArrayProperty(groupedTools.value[index].data, 'checked');
    if (status === 'all_true' || status === 'all_false' || status === 'empty') {
      groupedTools.value[index].isIndeterminate = false;
      groupedTools.value[index].checkedGroup = status === 'all_true' ? true : false;
    } else {
      groupedTools.value[index].isIndeterminate = true;
    }
    toolOpenNum.value = groupedTools.value.reduce(
      (total, item) => total + item.data.filter((obj) => obj.checked === true).length,
      0,
    );
  };

  const checkArrayProperty = (arr, propName) => {
    if (arr.length === 0) return 'empty';
    if (arr.every((item) => item[propName] === true)) {
      return 'all_true';
    }
    if (arr.every((item) => item[propName] === false)) {
      return 'all_false';
    }
    return 'mixed';
  };
  const closeTag = (val) => {
    formData.value.availableTools = formData.value.availableTools.filter(
      (item) => item !== val,
    );
    toolShowFn(formData.value.availableTools);
  };
  const changeTools = () => {
    dialogVisible.value = false;

    // if (formData.value.availableTools && formData.value.availableTools.length > 0) {
    formData.value.availableTools = groupedTools.value
      .flatMap((item) => item.data)
      .filter((obj) => obj.checked === true)
      .map((obj) => obj.key);
    // }

    console.log(formData.value);

    if (formData.value.availableTools.length > 0) {
      formRef.value.clearValidate(['availableTools']); // 清除校验
    }
  };

  const handleEdit = (item) => {
    formData.value = item;
    toolShowFn(item.availableTools);
    drawer.value = true;
  };

  const handleDelete = (id) => {
    console.log(id);

    deleteAgentAPI(id).then(() => {
      getAgents();
      ElMessage({
        type: 'success',
        message: '删除成功！',
      });
    });
  };
</script>

<style scoped lang="scss">
  .agent-set-content {
    background: #ffffff;
    // background: #f9fafb;
    height: 100%;
    .agent-set-padding {
      padding: 20px;
      .agent-set-top {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        margin-bottom: 24px;
        margin-top: 4px;
      }
      .card-content {
        overflow-y: auto;
        overflow-x: hidden;
        height: calc(100vh - 180px);
        .el-row {
          margin-top: 8px;
        }
        .card {
          // width: 400px;
          height: 240px;
          background: #f9fafb;
          border-radius: 16px;
          border: 1px solid #e5e7eb;
          padding: 24px;
          box-shadow: var(--shadow-sm);
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          cursor: pointer;
          overflow: hidden;
          margin-bottom: 12px;
          .card-top {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            .card-top-name {
              font-weight: 700;
              font-size: 1.25rem;
              color: #111827;
              letter-spacing: -0.025em;
              margin: 0;
            }
            .card-top-id {
              // display: inline-block;
              // padding: 4px 6px;
              border-radius: 6px;
              font-size: 0.75rem;
              color: #6b7280;
              // #D9D9D9
              background: #f3f4f6;
              padding: 4px 8px;
              border-radius: 6px;
              font-weight: 500;
            }
          }
          .card-description {
            //   font-size: 0.875rem;
            color: #4b5563;
            line-height: 1.5;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          .card-tools {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 16px;
            .tool-tag {
              background: #e0e7ff;
              color: #6366f1;
              padding: 4px 8px;
              border-radius: 16px;
              font-size: 12px;
              font-weight: 500;
            }
          }
          .card-bottom {
            position: absolute;
            bottom: 18px;
            right: 25px;
            width: 100%;

            .card-bottom-actions {
              display: flex;
              gap: 4px;
              justify-content: flex-end;
            }
          }
        }
        .card:hover {
          // #ecf5ff
          border-color: #409eff;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
            0 10px 10px -5px rgba(0, 0, 0, 0.04);
          transform: translateY(-4px);
        }
        .card:hover::before {
          opacity: 1;
        }
        .card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: #409eff;
          opacity: 0;
          transition: opacity 0.2s ease;
        }
      }
    }

    .drawer-title {
      font-size: 18px;
    }
  }
  .form-tools {
    width: 100%;
    margin-bottom: 8px;
  }

  :deep(.el-tag.el-tag--primary) {
    background: rgb(235, 245, 255);
    border: none;
    color: #1a5efe;
    padding: 16px;
  }
  :deep(.el-collapse) {
    border-bottom: none;
    border-top: none;
  }
  :deep(.el-collapse-item) {
    border: 1px solid rgba(211, 211, 212, 0.4);
    border-radius: 8px;
    .el-icon {
      margin-left: 12px;
    }
  }
  :deep(.el-collapse-item__header) {
    background-color: rgba(35, 36, 41, 0.02);
    border-bottom: 1px solid rgba(211, 211, 212, 0.4);
    border-radius: 8px 8px 0 0;
    height: 40px;
  }
  :deep(.el-collapse-item__content) {
    padding-bottom: 0;
  }
  :deep(.el-collapse-item__wrap) {
    border-bottom: none;
  }
  .collapse-header {
    display: flex;
    justify-content: space-between;
  }

  .tool-group-content {
    padding: 16px 16px 0;
    background: white;
    .tool-item {
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .tool-item-description {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
    .tool-item.selected {
      border-color: #007bff;
      background-color: #f8f9ff;
    }
  }
  .custom-tooltip-class {
    max-width: 400px !important;
    word-wrap: break-word;
  }
  .collapse-content {
    height: 500px;
    overflow: auto;
  }
  .top-total {
    font-size: 16px;
    margin: 12px 0;
  }
</style>
