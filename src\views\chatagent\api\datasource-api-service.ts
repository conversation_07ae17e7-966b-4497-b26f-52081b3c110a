/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// 数据源接口定义
export interface DataSource {
  id: number
  name: string
  type: string
  host?: string
  port?: number
  database?: string
  schema?: string
  description?: string
  status?: string
}

// 数据库接口定义
export interface Database {
  name: string
  schemas?: Schema[]
}

// 模式接口定义
export interface Schema {
  name: string
}

// 数据源API响应接口
export interface DataSourceResponse {
  success: boolean
  message?: string
  data?: DataSource[]
}

// 数据库列表API响应接口
export interface DatabaseResponse {
  success: boolean
  message?: string
  data?: Database[]
}

/**
 * 数据源API服务类
 * 负责与后端数据源相关接口进行交互
 */
export class DataSourceApiService {
  private static readonly BASE_URL = '/api/connection/datasource'
  private static readonly RDB_URL = '/api/rdb'

  /**
   * 处理HTTP响应
   */
  private static async handleResponse(response: Response) {
    if (!response.ok) {
      try {
        const errorData = await response.json()
        throw new Error(errorData.errorMessage || errorData.message || `API请求失败: ${response.status}`)
      } catch (e) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
      }
    }
    return response
  }

  /**
   * 获取所有数据源列表
   */
  static async getDataSourceList(): Promise<DataSource[]> {
    try {
      const response = await fetch(`${this.BASE_URL}/list?pageNo=1&pageSize=1000&refresh=true`)
      const result = await this.handleResponse(response)
      const data = await result.json()

      // 根据新的API结构提取数据
      if (data && data.success && data.data && data.data.data) {
        return data.data.data.map((item: any) => ({
          id: item.id,
          name: item.alias || `数据源${item.id}`,
          type: item.type,
          host: item.host,
          port: item.port,
          description: item.url,
          supportDatabase: item.supportDatabase,
          supportSchema: item.supportSchema
        }))
      }

      return []
    } catch (error) {
      console.error('获取数据源列表失败:', error)
      throw error
    }
  }

  /**
   * 根据数据源ID获取数据库列表
   */
  static async getDatabaseList(dataSourceId: number): Promise<Database[]> {
    try {
      const response = await fetch(`${this.RDB_URL}/database/list?dataSourceId=${dataSourceId}`)
      const result = await this.handleResponse(response)
      const data = await result.json()

      // 根据新的API结构提取数据
      if (data && data.success && data.data) {
        return data.data.map((item: any) => ({
          name: item.name,
          description: item.description,
          count: item.count
        }))
      }

      return []
    } catch (error) {
      console.error('获取数据库列表失败:', error)
      throw error
    }
  }

  /**
   * 根据数据源ID和数据库名获取模式列表
   */
  static async getSchemaList(dataSourceId: number, databaseName: string): Promise<Schema[]> {
    try {
      const response = await fetch(`${this.RDB_URL}/schema/list?dataSourceId=${dataSourceId}&databaseName=${encodeURIComponent(databaseName)}`)
      const result = await this.handleResponse(response)
      const data = await result.json()

      // 根据新的API结构提取数据
      if (data && data.success && data.data) {
        return data.data.map((item: any) => ({
          name: item.name
        }))
      }

      return []
    } catch (error) {
      console.error('获取模式列表失败:', error)
      throw error
    }
  }

  /**
   * 测试数据源连接
   */
  static async testConnection(dataSourceId: number): Promise<boolean> {
    try {
      const response = await fetch(`${this.BASE_URL}/${dataSourceId}/test`, {
        method: 'POST'
      })
      const result = await this.handleResponse(response)
      const data = await result.json()
      
      return data.success === true
    } catch (error) {
      console.error('测试数据源连接失败:', error)
      return false
    }
  }

  /**
   * 根据数据源ID获取详细信息
   */
  static async getDataSourceById(id: number): Promise<DataSource | null> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`)
      const result = await this.handleResponse(response)
      const data = await result.json()
      
      // 如果返回的是包装对象，提取data字段
      if (data && typeof data === 'object' && data.data) {
        return data.data
      }
      
      // 如果直接返回对象
      if (data && typeof data === 'object' && data.id) {
        return data
      }
      
      return null
    } catch (error) {
      console.error('获取数据源详情失败:', error)
      return null
    }
  }

  /**
   * 构建工具上下文对象
   */
  static buildToolContext(dataSourceId: number, databaseName?: string, schemaName?: string): Record<string, any> {
    const context: Record<string, any> = {
      dataSourceId
    }
    
    if (databaseName) {
      context.databaseName = databaseName
    }
    
    if (schemaName) {
      context.schemaName = schemaName
    }
    
    return context
  }
}
