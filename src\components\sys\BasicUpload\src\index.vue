<template>
  <div>
    <div class="flex justify-between">
      <div class="pt-2">
        <el-tag>
          <sys-icon type="InfoFilled" />
          {{ getHelpText }}
        </el-tag>
      </div>
      <el-upload
        ref="sysUpload"
        v-bind="$attrs"
        v-model:file-list="fileList"
        :accept="getStringAccept"
        :multiple="multiple"
        :auto-upload="false"
        :limit="maxNumber"
        :on-exceed="handleExceed"
        :show-file-list="false"
        :on-change="uploadChange"
        :http-request="uploadFile"
      >
        <el-button type="primary">{{ buttonText }}</el-button>
      </el-upload>
    </div>
    <el-table :data="fileList">
      <el-table-column prop="previewUrl" label="预览图">
        <template #default="scope">
          <el-image
            v-if="scope.row.url"
            style="width: 100px; height: 100px"
            :src="scope.row.url"
            :zoom-rate="1.2"
            :preview-src-list="[scope.row.url]"
            :initial-index="1"
            preview-teleported
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="name" label="文件名" />
      <el-table-column prop="size" label="文件大小" width="180">
        <template #default="scope">
          <el-tooltip
            effect="light"
            :content="transformI18n('uploads.maxSize', true, [maxSize])"
            placement="top-start"
            :disabled="!exceedMaxSize(scope.row.size)"
          >
            <el-tag :type="exceedMaxSize(scope.row.size) ? 'danger' : ''">{{
              sizeChange(scope.row.size)
            }}</el-tag>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="180">
        <template #default="scope">
          <el-progress
            :text-inside="true"
            :stroke-width="20"
            :percentage="scope.row.percentage"
            :status="statusShow(scope.row.status)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="action" label="操作" width="180">
        <template #default="scope">
          <el-button text type="danger" @click="deleteRow(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
  import { basicProps } from './props';
  import { PropType, ref, toRefs, watch } from 'vue';
  import { useUploadType } from './useUpload';
  import SysIcon from '/@/components/sys/Icon';
  import { UploadFile, UploadFiles, UploadProps, UploadUserFile } from 'element-plus';
  import type { UploadInstance } from 'element-plus';
  import { sizeChange } from '/@/utils/tools/sizeChangeTools';
  import { useMessage } from '/@/hooks/web/useMesage';
  import { transformI18n } from '/@/utils/i18n';

  const { createMessage } = useMessage();
  const sysUpload = ref<UploadInstance>();

  const emits = defineEmits(['change', 'delete', 'update:modelValue', 'errorFiles']);

  const props = defineProps({
    ...basicProps,
    previewFileList: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  });

  const { accept, helpText, maxNumber, maxSize } = toRefs(props);

  const { getStringAccept, getHelpText } = useUploadType({
    acceptRef: accept,
    helpTextRef: helpText,
    maxNumberRef: maxNumber,
    maxSizeRef: maxSize,
  });

  function statusShow(status) {
    let result = '';
    switch (status) {
      case 'success':
        result = 'success';
        break;
      case 'fail':
        result = 'exception';
        break;
      default:
        break;
    }
    return result;
  }

  function uploadFile(item: any) {
    console.log('item', item);
    const currentFile = fileList.value.find((file) => file.uid === item.file.uid);
    let param = new FormData();
    param.append('file', item.file);
    function onUploadProgress(progressEvent) {
      let persent = ((progressEvent.loaded / progressEvent.total) * 100) | 0; //上传进度百分比
      currentFile!.percentage = persent;
      console.log(persent, progressEvent);
    }
    props.api(param, onUploadProgress).then(
      (res) => {
        console.log('res', res);
        // currentFile!.result = 'success';
        currentFile!.status = 'success';
        currentFile!.response = res;
      },
      (error) => {
        currentFile!.status = 'fail';
        currentFile!.response = error.response.data;
      },
    );
  }

  function uploadAction() {
    if (maxSizeFiles.value.length > 0) {
      createMessage.error(transformI18n('uploads.maxSize', true, [maxSize?.value]));
      return;
    }
    sysUpload.value?.submit();

    // fileList.value.forEach((file: UploadFile) => {
    //   let param = new FormData(); //创建form对象
    //   const blob = new Blob([file.raw], { type: file.raw?.type });
    //   console.log('blob', blob)
    //   param.append('file', blob); //为创建的form对象增加上传的文件
    //   console.log('aaaa');
    //   props.api(param, onUploadProgress)
    // })
  }

  defineExpose({ getStringAccept, uploadAction });

  const fileList = ref<UploadUserFile[]>([]);
  const maxSizeFiles = ref<UploadUserFile[]>([]);

  watch(
    () => fileList.value,
    (value) => {
      emits('update:modelValue', value);
      maxSizeFiles.value = checkMaxSizeFiles(value);
      emits('errorFiles', maxSizeFiles.value);
    },
  );

  function checkMaxSizeFiles(fileList: UploadUserFile[]) {
    return fileList.filter((file) => {
      if (file.size) {
        return exceedMaxSize(file.size);
      }
    });
  }

  function imgToUrl(item: UploadFile) {
    return URL.createObjectURL(item.raw!);
  }

  function uploadChange(uploadFile: UploadFile, uploadFiles: UploadFiles) {
    emits('change', uploadFile, uploadFiles);
    if (uploadFile.raw && uploadFile.raw.type.includes('image')) {
      uploadFile.url = imgToUrl(uploadFile);
    }
  }

  const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
    console.log(files, uploadFiles);
    if (files) {
      createMessage.error(transformI18n('uploads.maxNumber', true, [maxNumber.value]));
    }
  };

  function exceedMaxSize(size: number) {
    if (maxSize.value) {
      return size / 1024 / 1024 > maxSize.value;
    }
    return false;
  }

  function deleteRow(row: Recordable) {
    fileList.value = fileList.value.filter((item) => item.uid !== row.uid);
  }
</script>

<style scoped></style>
