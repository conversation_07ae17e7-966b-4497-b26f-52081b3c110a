# 饼图功能示例

## 饼图数据格式示例

### 输入数据格式
当 `chartType` 为 `"pie"` 时，系统会自动将数据转换为饼图格式：

```json
{
  "chartType": "pie",
  "select_data": [
    {"product": "产品A", "sales": "1200"},
    {"product": "产品B", "sales": "800"},
    {"product": "产品C", "sales": "600"},
    {"product": "产品D", "sales": "400"}
  ],
  "XFields": "product",
  "YFields": "sales",
  "title": "产品销售分布"
}
```

### 自动转换后的ECharts格式
系统会自动将上述数据转换为ECharts饼图所需的格式：

```javascript
{
  title: {
    text: '产品销售分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [{
    name: '产品销售分布',
    type: 'pie',
    radius: '50%',
    data: [
      {name: '产品A', value: 1200},
      {name: '产品B', value: 800},
      {name: '产品C', value: 600},
      {name: '产品D', value: 400}
    ],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    },
    label: {
      show: true,
      formatter: '{b}: {d}%'
    }
  }]
}
```

## 饼图特性

### 1. 数据转换
- 自动将 `XFields` 字段作为饼图的 `name`
- 自动将 `YFields` 字段作为饼图的 `value`
- 支持字符串数字自动转换为数值类型

### 2. 视觉效果
- 显示百分比标签
- 左侧显示图例
- 鼠标悬停时有阴影效果
- 特殊的🥧图标区分于其他图表类型

### 3. 交互功能
- 鼠标悬停显示详细信息
- 点击图例可以隐藏/显示对应扇区
- 响应式设计，自动适应容器大小

## 使用场景

饼图适用于以下数据展示场景：
- 分类数据的占比分析
- 市场份额分布
- 用户类型分布
- 产品销售比例
- 地区分布统计

## 测试用例

### 测试数据1：简单分类
```json
{
  "chartType": "pie",
  "select_data": [
    {"type": "移动端", "count": "65"},
    {"type": "PC端", "count": "25"},
    {"type": "平板", "count": "10"}
  ],
  "XFields": "type",
  "YFields": "count"
}
```

### 测试数据2：销售数据
```json
{
  "chartType": "pie", 
  "select_data": [
    {"region": "华东", "amount": "2500000"},
    {"region": "华南", "amount": "1800000"},
    {"region": "华北", "amount": "2200000"},
    {"region": "西南", "amount": "1200000"},
    {"region": "其他", "amount": "800000"}
  ],
  "XFields": "region",
  "YFields": "amount",
  "title": "各地区销售额分布"
}
```

## 注意事项

1. **数据类型**: YFields字段的值会自动转换为数值类型
2. **空值处理**: 空值或无效值会被转换为0
3. **标签显示**: 自动显示百分比，格式为 "名称: 百分比%"
4. **颜色方案**: 使用ECharts默认颜色方案，自动分配不同颜色
5. **响应式**: 图表会自动适应容器大小变化
