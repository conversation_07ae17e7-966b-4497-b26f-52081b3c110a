import StaticFile from '/@/components/sys/StaticFile.vue';
import SvgIcon from '/@/components/sys/SvgIcon.vue';
import SysIcon from '/@/components/sys/Icon';
const components = [StaticFile, SvgIcon, SysIcon];

const plugins = [];

function registerGlobComp(app: any) {
  components.forEach((component) => {
    app.component(component.name, component);
  });
}

function registerGlobPlugin(app: any) {
  plugins.forEach((plugin) => {
    app.use(plugin);
  });
}

export { registerGlobComp, registerGlobPlugin };
