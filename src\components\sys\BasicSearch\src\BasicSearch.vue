<template>
  <div>
    <div class="search-header">
      <div class="flex w-full">
        <el-row class="w-full" :gutter="0">
          <el-col :span="item.span" v-for="item in searchArray" :key="item.field">
            <div class="flex justify-between items-center search-label">
              <div
                v-if="!!labelShow"
                :style="{
                  width: `${
                    item.labelWidth || labelWidth
                      ? (item.labelWidth || labelWidth) + 'px'
                      : 'unset'
                  }`,
                }"
              >
                <span>{{ item.label }}</span>
              </div>
              <el-input
                :disabled="item.componentProps?.disabled"
                v-if="item.component && item.component === 'Input'"
                :placeholder="item.placeholder ? item.placeholder : '请输入内容'"
                v-model="searchValue[item.field]"
                :clearable="item.componentProps?.clearable"
                class="flex-1"
                @input="searchEvent"
              />
              <el-input-number
                :disabled="item.componentProps?.disabled"
                v-else-if="item.component && item.component === 'InputNumber'"
                :placeholder="item.placeholder ? item.placeholder : '请输入内容'"
                v-model="searchValue[item.field]"
                class="flex-1"
              />
              <el-date-picker
                :disabled="item.componentProps?.disabled"
                v-else-if="item.component && item.component === 'DatePicker'"
                v-model="searchValue[item.field]"
                type="date"
                :format="item.componentProps?.format || 'YYYY-MM-DD'"
                :value-format="item.componentProps?.format || 'YYYY-MM-DD'"
                placeholder="请选择时间"
                class="flex-1"
              />
              <el-date-picker
                :disabled="item.componentProps?.disabled"
                v-else-if="item.component && item.component === 'DatePickerTime'"
                v-model="searchValue[item.field]"
                datetime
                :format="item.componentProps?.format || 'YYYY-MM-DD HH:mm:ss'"
                :value-format="item.componentProps?.format || 'YYYY-MM-DD HH:mm:ss'"
                :default-time="
                  item.componentProps?.defaultTime
                    ? new Date(`2022/1/1 ${item.componentProps?.defaultTime}`)
                    : new Date()
                "
                placeholder="请选择时间"
                class="flex-1"
              />
              <el-date-picker
                :disabled="item.componentProps?.disabled"
                v-else-if="item.component && item.component === 'DateRangePicker'"
                type="daterange"
                v-model="searchValue[item.field]"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                class="flex-1"
                @change="searchEvent"
              />
              <!-- :options="item.componentProps?.options" -->
              <el-select
                :disabled="item.componentProps?.disabled"
                :clearable="item.componentProps?.clearable"
                v-else-if="item.component && item.component === 'Select'"
                v-model="searchValue[item.field]"
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                class="flex-1"
                style="width: 100%"
                @change="searchEvent"
              >
                <el-option
                  v-for="optionItem in item.componentProps?.options || []"
                  :key="optionItem.value"
                  :label="optionItem.label"
                  :value="optionItem.value"
                />
              </el-select>
              <el-select
                :disabled="item.componentProps?.disabled"
                v-else-if="item.component && item.component === 'SelectMultiple'"
                v-model="searchValue[item.field]"
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                multiple
                class="flex-1"
                style="width: 100%"
              >
                <el-option
                  v-for="optionItem in item.componentProps?.options || []"
                  :key="optionItem.value"
                  :label="optionItem.label"
                  :value="optionItem.value"
                />
              </el-select>
              <slot
                :name="item.slot"
                :value="searchValue[item.field]"
                :record="searchValue"
                :key="item.field"
              ></slot>
            </div>
          </el-col>
          <el-col
            v-if="btnShow"
            :offset="
              24 - countSpan - actionSpan < 0
                ? 24 - actionSpan
                : 24 - countSpan - actionSpan
            "
            :span="actionSpan"
          >
            <div ref="actionRef" class="btn-group">
              <slot name="button">
                <div v-if="actionWidth >= 135">
                  <el-button type="primary" @click="onSearch">查询</el-button>
                  <el-button @click="reset">重置</el-button>
                </div>
                <el-dropdown
                  v-if="actionWidth < 135"
                  trigger="click"
                  class="pl-2 pr-8 btn-dropdown-area"
                >
                  <el-button type="primary" link
                    >操作<el-icon class="el-icon--right pt-1"><arrow-down /> </el-icon
                  ></el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="onSearch">查询</el-dropdown-item>
                      <el-dropdown-item @click="reset">重置</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </slot>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <slot></slot>
  </div>
</template>
<script setup lang="ts">
  import { PropType, watch, ref } from 'vue';
  import { useResizeObserver } from '@vueuse/core';
  import { SearchOptions } from '../types';
  import { isDef, isNumber } from '/@/utils/is';
  const props = defineProps({
    searchArray: {
      type: Array as PropType<SearchOptions[]>,
      default: () => {},
    },
    labelWidth: {
      type: Number as PropType<number>,
      default: null,
    },
    actionSpan: {
      type: Number as PropType<number>,
      default: 4,
    },
    labelShow: {
      type: Boolean,
      default: () => true,
    },
    btnShow: {
      type: Boolean,
      default: () => true,
    },
  });
  const countSpan = ref<number>(0);
  const searchValue = ref({});
  const emit = defineEmits(['onSearch', 'reset']);
  const actionRef = ref(null);
  const actionWidth = ref(0);
  const actionHeight = ref(0);
  const timer = ref<any>(null);
  useResizeObserver(actionRef, (entries) => {
    const entry = entries[0];
    const { width, height } = entry.contentRect;
    actionWidth.value = width;
    actionHeight.value = height;
  });
  watch(
    () => props.searchArray,
    (value) => {
      value.map((item) => {
        if (isDef(item.componentProps?.defaultValue)) {
          searchValue.value[item.field] = item.componentProps?.defaultValue;
        }
        if (item.component === 'InputNumber') {
          if (!isNumber(searchValue.value[item.field])) {
            searchValue.value[item.field] = 0;
          }
        }
        item.span = item.span || 6;
        countSpan.value += item.span;
        //搜索条件多，多行展示
        if (countSpan.value > 24) {
          countSpan.value = item.span;
        }
      });
    },
    { immediate: true, deep: true },
  );
  const clearTimer = () => {
    if (timer.value) {
      clearTimeout(timer.value);
    }
  };
  const searchEvent = () => {
    clearTimer();
    timer.value = setTimeout(() => {
      // this.getList();
      console.log(searchValue.value);
      emit('onSearch', searchValue.value);
    }, 500);
  };
  const onSearch = () => {
    emit('onSearch', searchValue.value);
  };
  const reset = () => {
    searchValue.value = {};
    // Object.keys(searchValue.value).map(() => {
    //
    // });
    emit('reset', searchValue.value);
  };
  defineExpose({ searchValue });
</script>
<style scoped>
  .search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 0;
    margin: 0 10px;
  }

  .w-full .el-col {
    margin-bottom: 10px;
  }

  .search-label {
    margin: 0px 10px;
  }

  .search-label span {
    margin-right: 15px;
  }

  .btn-group {
    padding-right: 10px;
    text-align: right;
  }

  .btn-dropdown-area {
    width: 56px;
  }
</style>
