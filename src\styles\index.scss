@import './public.css';
@import './layout/index.css';
@import './theme.css';
@import './element-variables.scss';
@import './dark.scss';
@import './system-pages.scss';
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
}

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s !important;
}

html {
  overflow: hidden;
  -webkit-text-size-adjust: 100%;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: visible !important;
  overflow-x: hidden !important;
  font-size: 14px;
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none !important;
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}
.el-button--primary {
  background-color: rgba(26, 94, 254, 1);
}
// .el-button {
//   background-color: rgba(242, 243, 245, 1);
// }

// .el-drawer__header,
// .el-dialog__header {
//   margin-bottom: 0 !important;
//   padding: 20px !important;
//   border-bottom: 1px solid #e4e7ed;
//   margin-right: 0 !important;
// }
// .el-drawer__footer,
// .el-dialog__footer {
//   padding: 20px !important;
//   border-top: 1px solid #e4e7ed;
// }
.el-dialog {
  border-radius: 8px !important;
}
.el-dialog.batch-Confirmation-dialog {
  margin-top: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.el-notification__content {
  text-align: inherit !important;
}
.notify-message {
  width: 380px !important;
}
.default-theme.splitpanes--vertical > .splitpanes__splitter,
.default-theme .splitpanes--vertical > .splitpanes__splitter {
  width: 16px !important;
  background: none !important;
  border-left: 0 !important;
  margin-left: 0 !important;
}
