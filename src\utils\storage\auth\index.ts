import { getStorage, setStorage, removeStorage } from '/@/utils/storage';
import {
  TOKEN_KEY,
  USER_INFO_KEY,
  TABLE_LOCAL,
  PASS_WORD_STATUS,
  WEB_MODEL,
} from '/@/enums/cacheEnum';
import { UserInfo } from '/#/store';

export function getAuthToken(): string {
  return getStorage(TOKEN_KEY);
}

export function setAuthToken(value: string) {
  if (!value) {
    const array: string[] = getStorage(TABLE_LOCAL) || [];
    array.forEach((element) => {
      removeStorage(element);
    });
    removeStorage(TABLE_LOCAL);
  }
  setStorage(TOKEN_KEY, value);
}

export function getAuthStorage(): UserInfo {
  return getStorage(USER_INFO_KEY);
}

export function setAuthStorage(value: Nullable<UserInfo>) {
  return setStorage(USER_INFO_KEY, value);
}

export function getPassWordStatusStorage(): boolean | undefined {
  return getStorage(PASS_WORD_STATUS);
}

export function setPassWordStatusStorage(value: boolean | undefined) {
  setStorage(PASS_WORD_STATUS, value);
}

export function getWebModel(): string {
  // 控制页面右上角，是否显示租户和应用切换，simple是不显示，mix就是显示
  return getStorage(WEB_MODEL);
}

export function setWebModel(value: string) {
  setStorage(WEB_MODEL, value);
}

// model
