// import { getStorage, setStorage } from '/@/utils/storage';
// // import { PASS_WORD_STATUS } from '/@/enums/cacheEnum';
// // import { UserInfo } from '/#/store';

// export function getPassWordStatusStorage(): string {
//   return getStorage(PASS_WORD_STATUS);
// }

// export function setPassWordStatusStorage(value: string) {
//   console.log(value);
//   setStorage(PASS_WORD_STATUS, value);
// }

// export function getPassWordStatusStorage() {
//   return getStorage(PASS_WORD_STATUS);
// }

// export function setPassWordStatusStorage(value) {
//   return setStorage(PASS_WORD_STATUS, value);
// }
