import { useAppStoreWithOut } from '/@/stores/modules/app';
import WujieVue from 'wujie-vue3';
import { computed } from 'vue';
import { Menu } from '/@/stores/types';
const appStore = useAppStoreWithOut();
const { bus } = WujieVue;
export const keepAliveList = computed(() => {
  // return Array.from(new Set(appStore.getKeepAliveList.concat(appStore.getExcludeList)));
  return appStore.getKeepAliveList;
});
// 点新增tab时
export const addKpAliveList = (menu: Menu) => {
  if (menu.type == '2' || menu.path.indexOf('/micro/vite') > -1) {
    bus.$emit('sendMicroMessage-' + menu?.meta?.microName, {
      message: { path: menu?.meta?.microPath },
      type: 'addKeepAlive',
    });
  } else {
    const aliveItem = menu.path;
    if (
      keepAliveList.value.indexOf(aliveItem) === -1 &&
      appStore.getIncludeList.indexOf(aliveItem) > -1
    ) {
      const addAlive = keepAliveList.value.concat([aliveItem]);
      appStore.setKeepAliveList(addAlive);
    }
  }
};

// 点删除tab时
export const setKpAliveList = (menu: Menu) => {
  const keepAliveList = appStore.getKeepAliveList;
  if (menu.type == '2' || menu.path.indexOf('/micro/vite') > -1) {
    bus.$emit('sendMicroMessage-' + menu?.meta?.microName, {
      message: { path: menu?.meta?.microPath },
      type: 'delKeepAlive',
    });
  } else {
    const hasIndex = keepAliveList.indexOf(menu.path);
    if (hasIndex > -1) {
      const addAlive = keepAliveList.concat([]);
      addAlive.splice(hasIndex, 1);
      appStore.setKeepAliveList(addAlive);
    }
  }
};
