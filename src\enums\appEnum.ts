/**
 * 权限模式
 */
export enum PermissionModeEnum {
  // 纯前端路由
  FRONT_END = 'FRONT_END',
  // 前后结合路由
  ROUTE_MAPPING = 'ROUTE_MAPPING',
}
/**
 * @description: i18n type
 */
export enum I18nTypeEnum {
  none = 'none', // 不展示切换
  zh = 'zh', // 中文
  en = 'en', // 英文
}

export enum ThemeTypeEnum {
  none = 'none', // 不展示切换
  default = 'default', // 默认
  red = 'red', // 红色
  green = 'green', // 绿色
}

export enum Layout {
  VERTICAL = 'vertical',
  HORIZONTAL = 'horizontal',
}

export enum VerifyCodeEnum {
  FRONT = 'front',
  BACKEND = 'backend',
  BOTH = 'both',
  NONE = 'none',
  SLIDER = 'slider',
}

export enum SettingsEnum {
  CACHE,
  FILE,
}

export enum LoginEnum {
  QrCode = 'QrCode',
  MSG = 'MSG',
  USER = 'USER',
}
// 控制页面右上角，是否显示租户和应用切换，simple是不显示，mix就是显示
export enum LoginWebModel {
  SIMPLE = 'simple',
  MIX = 'mix',
}
