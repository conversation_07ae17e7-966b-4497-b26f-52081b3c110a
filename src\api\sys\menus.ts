import { defHttp } from '/@/utils/axios';

// 菜单管理相关接口
enum Api {
  MenuList = '/sys/system/sysMenus/list',
  MenuPageList = '/sys/system/sysMenus/pageByParams',
  MenuSave = '/sys/system/sysMenus/save',
  MenuUpdate = '/sys/system/sysMenus/updateById',
  MenuDelete = '/sys/system/sysMenus/delete/{ids}',
  MenuGet = '/sys/system/sysMenus/get/{id}',
}

// 菜单查询参数
export interface MenuQueryParams {
  title?: string;
  visible?: string;
}

// 菜单信息
export interface MenuInfo {
  id?: string | number;
  title: string;
  name?: string;
  path?: string;
  component?: string;
  redirect?: string;
  icon?: string;
  sort?: number; // 显示顺序
  parentId?: string | number;
  menuType?: string; // M目录 C菜单 F按钮
  visible?: string | number; // 1显示 0隐藏
  status?: string; // 0正常 1停用
  roles?: string; // 权限标识
  query?: string;
  isFrame?: string; // 0否 1是
  isCache?: string; // 0缓存 1不缓存
  createTime?: string;
  updateTime?: string;
  children?: MenuInfo[];
}

/**
 * 查询菜单列表
 */
export const getMenuList = (params?: MenuQueryParams) =>
  defHttp.post<MenuInfo[]>({ url: Api.MenuList, params });

/**
 * 分页查询菜单列表
 */
export const getMenuPageList = (params?: MenuQueryParams) =>
  defHttp.post<{ records: MenuInfo[]; total: number }>({
    url: Api.MenuPageList,
    params,
  });

/**
 * 获取菜单详情
 */
export const getMenuDetail = (id: string) =>
  defHttp.get<MenuInfo>({ url: Api.MenuGet.replace('{id}', id) });

/**
 * 新增菜单
 */
export const saveMenu = (params: MenuInfo) =>
  defHttp.post({ url: Api.MenuSave, params });

/**
 * 更新菜单
 */
export const updateMenu = (params: MenuInfo) =>
  defHttp.post({ url: Api.MenuUpdate, params });

/**
 * 删除菜单
 */
export const deleteMenus = (ids: string) =>
  defHttp.get({ url: Api.MenuDelete.replace('{ids}', ids) });
