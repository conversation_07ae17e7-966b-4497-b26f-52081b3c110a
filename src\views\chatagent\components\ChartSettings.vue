<template>
    <!-- 对话框头部 -->
    <div class="dialog-content" >
      <!-- 左侧图表预览 -->
      <div class="chart-preview">
        <div ref="chartPreviewRef" class="chart-container" v-if="chartConfig.chartType !== 'Table'"></div>
        <VTable v-if="chartConfig.chartType === 'Table'" height="100%" :data="tableData" :columns="tableColumns"></VTable>
      </div>
      <!-- 右侧配置项 -->
      <div class="chart-config">
        <!-- 图表类型 -->
        <div class="config-section">
          <div class="section-title">图表类型</div>
          <el-select v-model="chartConfig.chartType" @change="updateChartType" placeholder="选择图表类型" class="config-select">
            <el-option-group
              v-for="group in chartTypes"
              :key="group.group"
              :label="group.group"
            >
              <el-option
                style="display: inline-block; height: 100%"
                v-for="item in group.options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <div class="chart-type-option">
                  <!-- <el-icon><component :is="item.icon" /></el-icon> -->
                  <span class="">{{ item.label }}</span>
                </div>
              </el-option>
            </el-option-group>
          </el-select>
        </div>
        
        <!-- 横轴 -->
        <div class="config-section" v-if="chartConfig.chartType !== 'Table'">
          <div class="section-title">横轴</div>
          <el-select v-model="chartConfig.xField" @change="updateChartPreview" placeholder="选择横轴字段" class="config-select">
            <el-option 
              v-for="field in availableFields" 
              :key="field.name"
              :label="field?.name || ''"
              :value="field.name"
            />
          </el-select>
        </div>
        
        <!-- 纵轴 -->
        <div class="config-section" v-if="chartConfig.chartType !== 'Table'">
          <div class="section-title">纵轴</div>
          <el-select v-model="chartConfig.yField" @change="updateChartPreview" placeholder="选择纵轴字段" class="config-select">
            <el-option 
              v-for="field in availableFields" 
              :key="field.name"
              :label="field.name"
              :value="field.name"
            />
          </el-select>
        </div>
        
        <!-- 图表选项 -->
        <div class="config-section" v-if="chartConfig.chartType !== 'Table'">
          <div class="section-title">图表选项</div>
          <div class="checkbox-options">
            <div class="checkbox-row">
              <el-checkbox v-model="chartOptions.showLegend" @change="updateChartPreview">图例</el-checkbox>
              <el-checkbox v-model="chartOptions.showLabel" @change="updateChartPreview">数据标签</el-checkbox>
            </div>
            <div class="checkbox-row">
              <el-checkbox v-model="chartOptions.showAxis" @change="updateChartPreview">坐标轴</el-checkbox>
              <el-checkbox v-model="chartOptions.showGrid" @change="updateChartPreview">网格线</el-checkbox>
            </div>
          </div>
        </div>
        
        <!-- 排序依据 -->
        <div class="config-section" v-if="chartConfig.chartType !== 'Table'">
          <div class="section-title">排序依据</div>
          <el-radio-group v-model="chartOptions.sortBy" @change="updateChartPreview">
            <el-radio label="none">不排序</el-radio>
            <el-radio label="asc">升序</el-radio>
            <el-radio label="desc">降序</el-radio>
          </el-radio-group>
        </div>
        
        <!-- 主题颜色 -->
        <div class="config-section" v-if="chartConfig.chartType !== 'Table'">
          <div class="section-title">主题颜色</div>
          <el-select v-model="chartOptions.themeColor" 
            @change="updateThemeColor"
            placeholder="选择主题颜色" 
            class="config-select theme-color-select">
            <template #prefix>
              <div class="selected-color-preview">
                <div class="color-blocks">
                  <div 
                    v-for="(color, index) in getSelectedThemeColors" 
                    :key="index" 
                    class="color-block" 
                    :style="{ backgroundColor: color }"
                  ></div>
                </div>
              </div>
            </template>
            <el-option 
              v-for="theme in themeColors" 
              :key="theme.value"
              :label="theme.label"
              :value="theme.value"
            >
              <div class="gradient-color-option">
                <div class="color-blocks">
                  <div 
                    v-for="(color, index) in theme.colors" 
                    :key="index" 
                    class="color-block" 
                    :style="{ backgroundColor: color }"
                  ></div>
                </div>
              </div>
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    
    
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick, computed } from 'vue';
import * as echarts from 'echarts';
import { Setting, Close, PieChart, Histogram, TrendCharts, LineChart, DataAnalysis, Opportunity, Grid, Document, ChatDotRound, DataLine } from '@element-plus/icons-vue';
import VTable from './VTable.vue';


// 定义字段接口
interface FieldItem {
  name: string;
  [key: string]: any; // 允许其他可能的属性
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  chartData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'save']);

// 控制对话框显示
const dialogVisible = ref(props.visible);

// 图表预览DOM引用
const chartPreviewRef = ref(null);
let chartInstance = null;
const tableData = ref([]);
const tableColumns = ref([]);
// 可用的图表类型（按分组）
const chartTypes = [
  { 
    group: '条线图',
    options: [
      { label: '柱状图', value: 'Column', icon: 'Histogram' },
      { label: '条形图', value: 'Bar', icon: 'DataAnalysis' }
    ]
  },
  { 
    group: '折线图',
    options: [
      { label: '折线图', value: 'Line', icon: 'LineChart' },
      // { label: '面积折线图', value: 'Area', icon: 'TrendCharts' },
      // { label: '平滑折线图', value: 'SmoothLine', icon: 'DataLine' },
      // { label: '阶梯折线图', value: 'StepLine', icon: 'LineChart' }
    ]
  },
  { 
    group: '饼图',
    options: [
      { label: '饼图', value: 'Pie', icon: 'PieChart' },
      // { label: '环形饼图', value: 'Doughnut', icon: 'PieChart' },
      // { label: '玫瑰饼图', value: 'Rose', icon: 'PieChart' }
    ]
  },
  { 
    group: '其他',
    options: [
      { label: '表格', value: 'Table', icon: 'Grid' },
      // { label: '漏斗图', value: 'Funnel', icon: 'Opportunity' },
      // { label: '词云', value: 'WordCloud', icon: 'ChatDotRound' },
      // { label: '统计数字', value: 'Statistic', icon: 'Document' }
    ]
  }
];

// 主题颜色选项 - 每个主题包含六个颜色值
const themeColors = [
  { label: '蓝色渐变', value: 'blue', colors: ['#5470c6', '#6a89d0', '#80a2da', '#96bbe4', '#acd4ee', '#c2edf8'] },
  { label: '橙色渐变', value: 'orange', colors: ['#ff9a45', '#fc8452', '#fac858', '#ffd666', '#ffe58f', '#fff1b8'] },
  { label: '绿色渐变', value: 'green', colors: ['#91cc75', '#3ba272', '#5bbc9d', '#87e8c4', '#a7f0d3', '#d4f8e8'] },
  { label: '红色渐变', value: 'red', colors: ['#ee6666', '#f89a9a', '#f8c4c4', '#ffa39e', '#ffccc7', '#ffe8e6'] },
  { label: '紫色渐变', value: 'purple', colors: ['#9a60b4', '#c17edb', '#dda5eb', '#e9b8f7', '#f3d5ff', '#f9ebff'] },
  { label: '多彩渐变', value: 'multi', colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272'] },
  { label: '灰度渐变', value: 'gray', colors: ['#333333', '#666666', '#999999', '#cccccc', '#e6e6e6', '#f5f5f5'] },
  { label: '暖色渐变', value: 'warm', colors: ['#ff7e5f', '#feb47b', '#ffcb91', '#ffdbaa', '#ffe9c5', '#fff6e5'] },
  { label: '冷色渐变', value: 'cold', colors: ['#2c3e50', '#4a6b8a', '#6a8caf', '#9db2c9', '#c4d4e3', '#e6eef5'] }
];


// 从传入的chartData中提取可用字段
const availableFields = ref<FieldItem[]>([]);

// 图表配置
const chartConfig = reactive({
  chartType: 'Column',
  summary: '',
  xField: '',
  yField: ''
});

// 图表选项
const chartOptions = reactive({
  showLegend: true,
  showLabel: false,
  showAxis: true,
  showGrid: false,
  sortBy: 'none',
  themeColor: themeColors[0].value
});

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
});

// 监听dialogVisible变化，同步回父组件
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
  if (newVal) {
    // 当对话框显示时，初始化图表
    nextTick(() => {
      initChartPreview();
    });
  }
});

// 监听chartData变化
watch(() => props.chartData, (newVal) => {
  if (newVal) {
    console.log('chartData changed:', newVal);
    // 更新配置
    updateConfigFromChartData(newVal);
    // 提取可用字段
    extractAvailableFields(newVal);
    nextTick(() => {
      initChartPreview();
    });
  }
}, { immediate: true, deep: true });

// 监听配置变化，更新图表预览
watch([chartConfig, chartOptions], () => {
  if (dialogVisible.value) {
    updateChartPreview();
  }
}, { deep: true });

// 从chartData中提取配置
function updateConfigFromChartData(data) {
  if (data.chartSchema) {
    chartConfig.chartType = data.chartSchema.chartType || 'Column';
    chartConfig.summary = data.chartSchema.summary || '';
    chartConfig.xField = data.chartSchema.xField || '';
    chartConfig.yField = data.chartSchema.yField || '';
    updateChartPreview()
  }
}

// 提取可用字段
function extractAvailableFields(data) {
  if (data.metaData && data.metaData.headerList) {
    availableFields.value = data.metaData.headerList;
  } else {
    availableFields.value = [];
  }
}

// 初始化图表预览
function initChartPreview() {
  // 如果是表格类型，不需要初始化echarts实例
  // if (chartConfig.chartType === 'Table') {
  //   return;
  // }
  
  // 销毁之前的实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  
  // 确保DOM元素存在
  if (chartPreviewRef.value) {
    // 创建新的echarts实例
    chartInstance = echarts.init(chartPreviewRef.value);
    
    // 更新图表
    updateChartPreview();
    
    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', () => {
      if (chartInstance) {
        chartInstance.resize();
      }
    });
  }
}

// 更新图表预览
function updateChartPreview() {
  // console.log('chartInstance',chartInstance)
  

  // 准备数据
  const xData = [];
  const yData = [];
  // 设置图表选项
  let option;
  // 表格类型
  if (chartConfig.chartType === 'Table') {
    // 表格类型使用VTable组件渲染
    // 先清空之前的数据
    tableData.value = [];
    tableColumns.value = [];
    
    // 延迟一帧，确保DOM已更新
    nextTick(() => {
      // 准备表格数据
      let chartDatas = props.chartData
      console.log('props.chartData',props.chartData)
      if (chartDatas && chartDatas.metaData && chartDatas.metaData.dataList) {
        const headerList = chartDatas.metaData.headerList;
        const dataList = chartDatas.metaData.dataList;
        
        // 提取列信息
        if (headerList && headerList.length > 0) {
          // 将所有列添加到tableColumns
          headerList.forEach(header => {
            if (header.name) {
              tableColumns.value.push(header.name);
            }
          });
          
          // 提取数据
          dataList.forEach(item => {
            const rowData = {};
            headerList.forEach((header, index) => {
              if (header.name) {
                rowData[header.name] = item[index];
              }
            });
            tableData.value.push(rowData);
          });
        }
      }
      console.log('tableData', tableData);
    });
    
    // 返回空选项，因为不使用echarts
    option = {};
    
    // 如果有图表实例，先销毁它
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
  }
  if (!chartInstance) return;
  if (props.chartData && props.chartData.metaData && props.chartData.metaData.dataList) {
    const headerList = props.chartData.metaData.headerList;
    const dataList = props.chartData.metaData.dataList;
    
    // 找到x和y字段在headerList中的索引
    const xIndex = headerList.findIndex((header) => header.name === chartConfig.xField);
    const yIndex = headerList.findIndex((header) => header.name === chartConfig.yField);
    
    if (xIndex !== -1 && yIndex !== -1) {
      // 提取数据
      dataList.forEach((item) => {
        if (item[xIndex] !== null) {
          xData.push(item[xIndex]);
          yData.push(item[yIndex]);
        }
      });
      
      // 如果需要排序
      if (chartOptions.sortBy !== 'none') {
        // 创建包含x和y值的数组对
        const pairs = xData.map((x, i) => ({ x, y: yData[i] }));
        
        // 根据y值排序
        pairs.sort((a, b) => {
          if (chartOptions.sortBy === 'asc') {
            return a.y - b.y;
          } else {
            return b.y - a.y;
          }
        });
        
        // 重新分配排序后的值
        xData.length = 0;
        yData.length = 0;
        pairs.forEach(pair => {
          xData.push(pair.x);
          yData.push(pair.y);
        });
      }
    }
  }
  
  
  if (chartConfig.chartType !== 'Table') {
    // 确保表格数据和列都被清空
    tableData.value = [];
    tableColumns.value = [];
  }
  // 饼图类型（包括普通饼图、环形饼图、玫瑰饼图）
  if (['Pie', 'Doughnut', 'Rose'].includes(chartConfig.chartType)) {
    // 饼图配置
    const pieData = xData.map((name, index) => ({
      name: name,
      value: yData[index]
    }));
    
    option = {
      title: {
        text: chartConfig.summary,
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        show: chartOptions.showLegend,
        orient: 'horizontal',
        bottom: 10
      },
      grid: {
        show: chartOptions.showGrid
      },
      series: [
        {
          name: chartConfig.yField,
          type: 'pie',
          // 环形饼图设置内外半径
          radius: chartConfig.chartType === 'Doughnut' ? ['40%', '70%'] : '60%',
          // 玫瑰饼图设置
          roseType: chartConfig.chartType === 'Rose' ? 'radius' : false,
          center: ['50%', '50%'],
          data: pieData,
          label: {
            show: chartOptions.showLabel,
            formatter: '{b}: {c} ({d}%)'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ],
      color: getThemeColors(chartOptions.themeColor)
    };
  } 
  
  // 词云类型
  else if (chartConfig.chartType === 'WordCloud') {
    // 词云需要特殊处理数据
    const wordCloudData = xData.map((name, index) => ({
      name: name,
      value: yData[index]
    }));
    
    option = {
      title: {
        text: chartConfig.summary,
        left: 'center'
      },
      tooltip: {
        show: true
      },
      series: [{
        type: 'wordCloud',
        shape: 'circle',
        left: 'center',
        top: 'center',
        width: '70%',
        height: '80%',
        right: null,
        bottom: null,
        sizeRange: [12, 60],
        rotationRange: [-90, 90],
        rotationStep: 45,
        gridSize: 8,
        drawOutOfBound: false,
        textStyle: {
          fontFamily: 'sans-serif',
          fontWeight: 'bold',
          color: function () {
            return 'rgb(' + [
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160)
            ].join(',') + ')';
          }
        },
        emphasis: {
          focus: 'self',
          textStyle: {
            shadowBlur: 10,
            shadowColor: '#333'
          }
        },
        data: wordCloudData
      }]
    };
  }
  // 统计数字类型
  else if (chartConfig.chartType === 'Statistic') {
    // 计算总和或平均值等统计数据
    const sum = yData.reduce((acc, val) => acc + val, 0);
    const avg = sum / yData.length;
    const max = Math.max(...yData);
    const min = Math.min(...yData);
    
    option = {
      title: {
        text: chartConfig.summary,
        left: 'center'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      graphic: [{
        type: 'group',
        left: 'center',
        top: 'center',
        children: [
          {
            type: 'text',
            z: 100,
            left: 'center',
            top: 'middle',
            style: {
              fill: getThemeColors(chartOptions.themeColor)[0],
              text: `${sum.toFixed(2)}`,
              font: 'bold 40px sans-serif'
            }
          },
          {
            type: 'text',
            z: 100,
            left: 'center',
            top: 'middle',
            style: {
              fill: '#999',
              text: `\n\n平均值: ${avg.toFixed(2)}\n最大值: ${max}\n最小值: ${min}`,
              font: '14px sans-serif'
            }
          }
        ]
      }]
    };
  }
  // 漏斗图
  else if (chartConfig.chartType === 'Funnel') {
    const funnelData = xData.map((name, index) => ({
      name: name,
      value: yData[index]
    }));
    
    option = {
      title: {
        text: chartConfig.summary,
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c}'
      },
      legend: {
        show: chartOptions.showLegend,
        orient: 'horizontal',
        bottom: 10
      },
      series: [
        {
          name: chartConfig.yField,
          type: 'funnel',
          left: '10%',
          top: 60,
          bottom: 60,
          width: '80%',
          min: 0,
          max: Math.max(...yData) * 1.2,
          minSize: '0%',
          maxSize: '100%',
          sort: 'descending',
          gap: 2,
          label: {
            show: chartOptions.showLabel,
            position: 'inside'
          },
          emphasis: {
            label: {
              fontSize: 20
            }
          },
          data: funnelData,
          itemStyle: {
            color: function(params) {
              return getThemeColors(chartOptions.themeColor)[params.dataIndex % getThemeColors(chartOptions.themeColor).length];
            }
          }
        }
      ]
    };
  }
  // 其他图表类型（柱状图、条形图、折线图、面积图等）
  else {
    // 确定图表类型
    let chartType = 'line';
    let isHorizontal = false;
    
    switch(chartConfig.chartType) {
      case 'Column':
        chartType = 'bar';
        break;
      case 'Bar':
        chartType = 'bar';
        isHorizontal = true;
        break;
      case 'Line':
        chartType = 'line';
        break;
      case 'SmoothLine':
        chartType = 'line';
        break;
      case 'StepLine':
        chartType = 'line';
        break;
      case 'Area':
        chartType = 'line';
        break;
      default:
        chartType = 'line';
    }

    option = {
      title: {
        text: chartConfig.summary,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        show: chartOptions.showLegend,
        bottom: 10
      },
      xAxis: {
        type: isHorizontal ? 'value' : 'category',
        data: isHorizontal ? null : xData,
        // name: chartConfig.xField,
        show: chartOptions.showAxis,
        splitLine: {
          show: chartOptions.showGrid
        }
      },
      yAxis: {
        type: isHorizontal ? 'category' : 'value',
        data: isHorizontal ? xData : null,
        // name: chartConfig.yField,
        show: chartOptions.showAxis
      },
      grid: {
        show: chartOptions.showGrid,
        containLabel: true
      },
      series: [
        {
          name: chartConfig.yField,
          data: yData,
          type: chartType,
          // 面积图样式
          areaStyle: ['Area'].includes(chartConfig.chartType) ? {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: getThemeColors(chartOptions.themeColor)[0] },
              { offset: 1, color: getThemeColors(chartOptions.themeColor)[1] || getThemeColors(chartOptions.themeColor)[0] }
            ])
          } : null,
          // 平滑曲线
          smooth: ['SmoothLine', 'Area'].includes(chartConfig.chartType),
          // 阶梯线图
          step: chartConfig.chartType === 'StepLine' ? 'middle' : false,
          label: {
            show: chartOptions.showLabel,
            position: 'top'
          },
          itemStyle: {
            color: ['Column', 'Bar'].includes(chartConfig.chartType) ? 
              new echarts.graphic.LinearGradient(isHorizontal ? 1 : 0, isHorizontal ? 0 : 1, isHorizontal ? 0 : 0, isHorizontal ? 0 : 0, [
                { offset: 0, color: getThemeColors(chartOptions.themeColor)[0] },
                { offset: 1, color: getThemeColors(chartOptions.themeColor)[1] || getThemeColors(chartOptions.themeColor)[0] }
              ]) : getThemeColors(chartOptions.themeColor)[0]
          }
        }
      ]
    };
  }
  console.log('option', option,chartInstance);
  // debugger
  // 设置图表
  if(chartInstance && chartConfig.chartType !== 'Table'){
    nextTick(() => {
      chartInstance.setOption(option);
    })
  }
  // chartConfig.chartType !== 'Table' && chartInstance.setOption(option);
}

// 更新图表类型
function updateChartType(type) {
  console.log('updateChartType', type);
  chartConfig.chartType = type;
  
  // 清空表格数据和列
  tableData.value = [];
  tableColumns.value = [];
  
  // 如果从表格切换到其他图表类型，需要重新初始化图表
  if (type !== 'Table') {
    nextTick(() => {
      initChartPreview();
    });
  } else {
    updateChartPreview();
  }
}

// 更新主题颜色
function updateThemeColor(colorValue) {
  chartOptions.themeColor = colorValue;
  updateChartPreview();
}

// 获取主题颜色数组
function getThemeColors(colorValue) {
  const selectedTheme = themeColors.find(theme => theme.value === colorValue);
  return selectedTheme ? selectedTheme.colors : themeColors[0].colors;
}

// 获取当前选中主题的颜色数组
const getSelectedThemeColors = computed(() => {
  const selectedTheme = themeColors.find(theme => theme.value === chartOptions.themeColor);
  return selectedTheme ? selectedTheme.colors : themeColors[0].colors;
});

// 获取当前选中主题的渐变色样式
const getSelectedThemeGradient = computed(() => {
  const selectedTheme = themeColors.find(theme => theme.value === chartOptions.themeColor);
  if (!selectedTheme) return 'transparent';
  return `linear-gradient(to right, ${selectedTheme.colors.join(', ')})`;
});

// 关闭对话框
function handleClose() {
  dialogVisible.value = false;
}

// 保存设置
function saveSettings() {
  // 构建更新后的图表配置
  const updatedChartSchema = {
    chartType: chartConfig.chartType,
    summary: chartConfig.summary,
    xField: chartConfig.xField,
    yField: chartConfig.yField
  };
  
  // 发送保存事件到父组件
  emit('save', {
    chartSchema: updatedChartSchema,
    chartOptions: { ...chartOptions }
  });
  
  // 关闭对话框
  dialogVisible.value = false;
}

// 组件卸载时清理
onMounted(() => {
  if (chartInstance) {
    window.removeEventListener('resize', chartInstance.resize);
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style lang="scss" scoped>
.chart-settings-dialog :deep(.el-dialog__header) {
  display: none;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  overflow: hidden;
}

.close-icon {
  font-size: 20px;
  cursor: pointer;
}

.dialog-content {
  display: flex;
  gap: 20px;
  height: 600px;
}

.chart-preview {
  flex: 1;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chart-container {
  flex: 1;
  min-height: 400px;
}

.chart-config {
  width: 300px;
  overflow-y: auto;
  padding-right: 10px;
}

.config-section {
  margin-bottom: 20px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

/* 图表类型选项样式 */
.chart-type-option {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 8px;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.chart-type-option .el-icon {
  font-size: 18px;
}

.chart-type-item .el-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

:deep(.el-select-dropdown__item) {
  padding: 8px 32px 0 20px !important;
}
:deep(.el-select-dropdown){
  max-width: 300px !important;
  min-width: 100px;
}
:deep(.el-select-dropdown__item.hover, .el-select-dropdown__item:hover) {
  background-color: #fff !important;
}
:deep(.el-select-group .el-select-dropdown__item){
  display: inline-block;
  width: 30%;
}
:deep(.el-select-dropdown__wrap) {
  padding: 4px;
}

/* 设置选项组样式 */
:deep(.el-select-group__wrap) {
  margin: 0 !important;
}

:deep(.el-select-group__title) {
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 设置选项列表为flex布局，允许换行 */
:deep(.el-select-group__wrap .el-select-dropdown__list) {
  /* display: flex;
  flex-wrap: wrap; */
  margin: 0 -4px;
}

/* 设置每个选项宽度为三分之一，实现每行最多3个 */
:deep(.el-select-group__wrap .el-select-dropdown__item) {
  width: calc(33.33% - 8px);
  margin: 0 4px 8px;
  box-sizing: border-box;
  flex-shrink: 0;
  padding: 4px;
}

/* 调整选项内容样式 */
:deep(.chart-type-option) {
  width: 100%;
  height: 100%;
  justify-content: center;
  padding: 2px 8px;
  text-align: center;
  font-size: 12px;
}

:deep(.el-select-dropdown__item:hover) .chart-type-option {
  border: 1px solid #d9d9d9;
  background-color: #f0f0f0;
}

:deep(.el-select-dropdown__item.selected) .chart-type-option {
  background-color: #e6f7ff;
  color: #1890ff;
  border-color: #1890ff;
}


.config-select {
  width: 100%;
}

.checkbox-options {
  margin-top: 10px;
}

.checkbox-row {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 12px;
}

.checkbox-row .el-checkbox {
  margin-right: 20px;
  flex: 0 0 calc(50% - 10px);
}

.gradient-color-option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding-top: 12px;
}

.color-blocks {
  display: flex;
  width: 100%;
  height: 12px;
  line-height: 12px;
  border-radius: 4px;
  overflow: hidden;
}

.color-block {
  flex: 1;
  height: 100%;
}

/* 选中状态样式 */
:deep(.el-select-dropdown__item.selected) .color-blocks {
  box-shadow: 0 0 0 2px #409EFF;
}

/* 主题颜色选择器样式 */
.theme-color-select {
  position: relative;
}

/* 隐藏默认的文本显示 */
.theme-color-select :deep(.el-input__inner) {
  color: transparent !important;
  padding-left: 15px;
}

.theme-color-select :deep(.el-input__suffix) {
  right: 5px;
}

/* 选中颜色预览区域 */
.selected-color-preview {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: calc(100% - 60px);
  z-index: 2;
}

.selected-color-preview .color-blocks {
  height: 10px;
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

/* 下拉选项中的颜色块样式 */
:deep(.el-select-dropdown__item) .gradient-color-option {
  padding: 4px 0;
}

:deep(.el-select-dropdown__item:hover) .color-blocks {
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.5);
}
</style>