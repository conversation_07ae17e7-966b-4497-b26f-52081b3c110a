import { ref } from 'vue';
import type { UploadProps, UploadRawFile } from 'element-plus';
import { genFileId } from 'element-plus';
export default function useUpload(upload) {
  const fileVisible = ref(false);
  // const upload = ref<UploadInstance>();
  const fileData = ref<any>();
  const fileName = ref('');
  const fileStatus = ref(false);
  const handleExceed: UploadProps['onExceed'] = (files) => {
    upload.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    upload.value!.handleStart(file);
  };

  const handleChange = (file) => {
    console.log('file11', file);
    fileData.value = file;
  };
  const handleSuccess = (response, file) => {
    console.log('上传response', response); // 获取文件名
    console.log('上传的文件名:', file.name); // 获取文件名
    fileName.value = file.name;
    fileStatus.value = true;
  };

  // const uploadFile = () => {
  //   console.log(fileData.value);
  //   const form = new FormData();
  //   form.append('file', fileData.value?.raw);
  //   form.append('targetId', formData.value?.id ? formData.value.id.toString() : '');
  //   form.append(
  //     'targetInstCode',
  //     formData.value?.instCode ? formData.value.instCode.toString() : '',
  //   );
  //   uploadApi(form).then(() => {
  //     fileVisible.value = false;
  //     ElMessage({
  //       type: 'success',
  //       message: '导入成功',
  //     });
  //   });
  // };
  return {
    fileVisible,
    fileData,
    handleExceed,
    handleChange,
    handleSuccess,
    fileName,
    fileStatus,
    // uploadFile,
  };
}
