declare interface MenuSetting {
  collapsed: boolean;
}
declare interface ThemeColorObject {
  nav: string;
  menu: string;
  main: string;
  content: string;
}
declare interface ProjectConfigs {
  settingConfig?: number;
  cacheType?: number;
  permissionMode?: string;
  menuSetting: MenuSetting;
  menuWidth: number;
  collapseWidth: number;
  layout?: string;
  theme?: ThemeColorObject;
  locale?: string;
  upms?: boolean;
  storageName?: string;
  prefixCls?: string;
  verifyCode?: string;
  loginCode?: boolean;
  loginType?: number;
  loginMsg?: boolean;
  useLoginPage?: boolean;
  loginTypes: any[];
  oss?: boolean;
  isEncrypt?: boolean;
  encryptClearStorage?: boolean;
  useTab?: boolean;
}
