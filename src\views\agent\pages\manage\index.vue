<template>
  <div class="agent-container">
    <div class="main">
      <!-- MCP工具管理  智能体配置  智能体编排 -->

      <div class="sidebar">
        <h2 class="sidebar-title">智能体管理</h2>
        <div class="divider"></div>
        <div class="menu-list">
          <div
            v-for="(item, index) in menuList"
            :key="item.id"
            @click="selectItem(index)"
            :class="['menu-item', { active: selectedIndex === index }]"
          >
            <span class="menu-label">{{ item.label }}</span>
          </div>
        </div>
      </div>
      <div class="content">
        <component :is="currentComponent" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, shallowRef } from 'vue';
  import AgentSet from './agentSet.vue';
  import Arrange from './arrange.vue';
  import Tools from './tools.vue';
  import GlobalConfig from './globalConfig.vue';

  const menuList = shallowRef([
    {
      label: '智能体编排',
      value: Arrange,
      id: 1,
    },
    {
      label: '智能体配置',
      value: AgentSet,
      id: 2,
    },
    {
      label: '工具管理',
      value: Tools,
      id: 3,
    },
    {
      label: '全局配置',
      value: GlobalConfig,
      id: 4,
    },
  ]);
  const selectedIndex = ref(0); //   0
  const currentComponent = shallowRef(Arrange); //Arrange
  const selectItem = (index) => {
    selectedIndex.value = index; // 更新选中的索引
    currentComponent.value = menuList.value[index].value;
  };
</script>

<style scoped lang="scss">
  .agent-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .main {
      display: flex;
      flex: 1;
      .sidebar {
        flex: 0 0 180px;
        background: #ffffff;
        border-right: 1px solid #e6e8ee;
        .menu-item {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          // margin: 0 12px;
          // border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s;
          font-size: 14px;
          color: #606266;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            color: #409eff;
            background-color: #ecf5ff;
            border-right: 3px solid #409eff;
          }

          .menu-label {
            flex: 1;
          }
        }

        .sidebar-title {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
          padding: 0 20px;
          margin: 15px 0 15px 0;
        }

        .divider {
          height: 1px;
          background-color: #ebeef5;
          margin-bottom: 15px;
        }
      }
      .content {
        flex: 1;
        overflow: auto;
        background: #f6f8fa;
      }
    }
  }
</style>
