/**
 * 工具详情API服务
 * 用于获取ThinkActRecord的详细信息，包括工具参数和执行结果
 */

export interface ToolDetailResponse {
  id: number
  parentExecutionId: number
  status: string
  toolName: string
  thinkStartTime: string
  thinkEndTime?: string
  actStartTime?: string
  actEndTime?: string
  thinkDetails: {
    thinkInput: string
    thinkOutput: string
  }
  actionDetails: {
    toolParameters: any
    actionResult: any
  }
  errorInfo?: string
}

export interface ToolParametersResponse {
  thinkActId: number
  toolName: string
  toolParameters: any
  actStartTime?: string
}

export interface ActionResultResponse {
  thinkActId: number
  toolName: string
  actionResult: any
  actEndTime?: string
  status: string
}

export class ToolDetailApiService {
  private static readonly BASE_URL = '/api/streaming-events'

  /**
   * 获取工具的完整详细信息
   * @param thinkActId ThinkActRecord的ID
   * @returns 完整的工具详情
   */
  public static async getToolDetail(thinkActId: string | number): Promise<ToolDetailResponse | null> {
    try {
      const url = `${this.BASE_URL}/think-act/${thinkActId}/detail`
      console.log('🔍 [ToolDetailApiService] 获取工具详细信息:', thinkActId)
      console.log('🔍 [ToolDetailApiService] 请求URL:', url)

      const response = await fetch(url)
      console.log('🔍 [ToolDetailApiService] 响应状态:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      })

      if (response.status === 404) {
        console.warn('⚠️ 工具详情不存在:', thinkActId)
        return null
      }

      if (!response.ok) {
        throw new Error(`获取工具详情失败: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('✅ 工具详情获取成功:', data)

      return data
    } catch (error: any) {
      console.error('❌ 获取工具详情失败:', error)
      throw error
    }
  }

  /**
   * 获取工具参数
   * @param thinkActId ThinkActRecord的ID
   * @returns 工具参数信息
   */
  public static async getToolParameters(thinkActId: string | number): Promise<ToolParametersResponse | null> {
    try {
      console.log('🔍 获取工具参数:', thinkActId)

      const response = await fetch(`${this.BASE_URL}/think-act/${thinkActId}/tool-parameters`)

      if (response.status === 404) {
        console.warn('⚠️ 工具参数不存在:', thinkActId)
        return null
      }

      if (!response.ok) {
        throw new Error(`获取工具参数失败: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('✅ 工具参数获取成功:', data)

      return data
    } catch (error: any) {
      console.error('❌ 获取工具参数失败:', error)
      throw error
    }
  }

  /**
   * 获取执行结果
   * @param thinkActId ThinkActRecord的ID
   * @returns 执行结果信息
   */
  public static async getActionResult(thinkActId: string | number): Promise<ActionResultResponse | null> {
    try {
      console.log('🔍 获取执行结果:', thinkActId)

      const response = await fetch(`${this.BASE_URL}/think-act/${thinkActId}/action-result`)

      if (response.status === 404) {
        console.warn('⚠️ 执行结果不存在:', thinkActId)
        return null
      }

      if (!response.ok) {
        throw new Error(`获取执行结果失败: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('✅ 执行结果获取成功:', data)

      return data
    } catch (error: any) {
      console.error('❌ 获取执行结果失败:', error)
      throw error
    }
  }

  /**
   * 批量获取多个工具的详细信息
   * @param thinkActIds ThinkActRecord ID数组
   * @returns 工具详情数组
   */
  public static async getBatchToolDetails(thinkActIds: (string | number)[]): Promise<(ToolDetailResponse | null)[]> {
    try {
      console.log('🔍 批量获取工具详情:', thinkActIds)

      const promises = thinkActIds.map(id => this.getToolDetail(id))
      const results = await Promise.allSettled(promises)

      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value
        } else {
          console.error(`❌ 获取工具详情失败 [${thinkActIds[index]}]:`, result.reason)
          return null
        }
      })
    } catch (error: any) {
      console.error('❌ 批量获取工具详情失败:', error)
      throw error
    }
  }

  /**
   * 格式化工具参数为可读的JSON字符串
   * @param parameters 工具参数对象
   * @returns 格式化的JSON字符串
   */
  public static formatParameters(parameters: any): string {
    if (!parameters) return '暂无参数'

    try {
      if (typeof parameters === 'string') {
        // 如果已经是字符串，尝试解析再格式化
        const parsed = JSON.parse(parameters)
        return JSON.stringify(parsed, null, 2)
      } else {
        // 如果是对象，直接格式化
        return JSON.stringify(parameters, null, 2)
      }
    } catch (e) {
      // 如果解析失败，返回原始字符串
      return String(parameters)
    }
  }

  /**
   * 格式化执行结果为可读的JSON字符串
   * @param result 执行结果对象
   * @returns 格式化的JSON字符串
   */
  public static formatResult(result: any): string {
    if (!result) return '暂无结果'

    try {
      if (typeof result === 'string') {
        // 如果已经是字符串，尝试解析再格式化
        const parsed = JSON.parse(result)
        return JSON.stringify(parsed, null, 2)
      } else {
        // 如果是对象，直接格式化
        return JSON.stringify(result, null, 2)
      }
    } catch (e) {
      // 如果解析失败，返回原始字符串
      return String(result)
    }
  }

  /**
   * 检查工具详情是否可用
   * @param thinkActId ThinkActRecord的ID
   * @returns 是否可用
   */
  public static async isToolDetailAvailable(thinkActId: string | number): Promise<boolean> {
    try {
      const response = await fetch(`${this.BASE_URL}/think-act/${thinkActId}/detail`, { method: 'HEAD' })
      return response.ok
    } catch (error) {
      return false
    }
  }
}

export default ToolDetailApiService
