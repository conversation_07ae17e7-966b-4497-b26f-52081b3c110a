# 图表容器ID冲突问题修复

## 问题描述
之前的实现中，图表容器ID使用 `round.id` 生成，如果多个round的ID相同，会导致图表容器ID冲突，从而影响图表的正确渲染。

## 问题分析
```javascript
// 之前的实现 - 可能导致ID冲突
const containerId = `summary-chart-${round.id}-${index}`
```

当多个对话轮次的 `round.id` 相同时，会生成相同的容器ID，导致：
1. 后渲染的图表会覆盖先渲染的图表
2. 图表实例管理混乱
3. 图表无法正确显示

## 解决方案
使用 `chartItem.stepId` 来生成唯一的容器ID，因为每个工具执行步骤的ID都是唯一的。

### 修改前后对比

#### 修改前
```vue
<!-- 模板中的容器ID -->
:id="`summary-chart-${round.id}-${index}`"

<!-- JavaScript中的容器ID -->
const containerId = `summary-chart-${round.id}-${index}`
```

#### 修改后
```vue
<!-- 模板中的容器ID -->
:id="`summary-chart-${chartItem.stepId}`"

<!-- JavaScript中的容器ID -->
const containerId = `summary-chart-${chartItem.stepId}`
```

## 具体修改内容

### 1. 模板修改
```vue
<div 
  v-for="(chartItem, index) in round.chartData" 
  :key="`chart-${chartItem.stepId}-${index}`"
  class="summary-chart-item"
  :data-chart-type="chartItem.chartType"
>
  <!-- 图表显示 -->
  <div v-else class="summary-chart-container">
    <div class="chart-title">{{ chartItem.title }}</div>
    <div
      :id="`summary-chart-${chartItem.stepId}`"
      class="summary-chart-canvas"
      style="width: 100%; height: 400px;"
    ></div>
  </div>
</div>
```

### 2. JavaScript渲染逻辑修改
```javascript
// 监听conversationRounds变化，自动渲染图表
watch(
  () => conversationRounds.value,
  (newRounds) => {
    nextTick(() => {
      newRounds.forEach((round) => {
        if (round.chartData && round.chartData.length > 0) {
          round.chartData.forEach((chartItem) => {
            if (chartItem.chartType !== 'table' && chartItem.chartType !== 'TABLE') {
              const containerId = `summary-chart-${chartItem.stepId}`
              console.log('渲染图表，容器ID:', containerId, '图表数据:', chartItem)
              renderSummaryChart(chartItem, containerId)
            }
          })
        }
      })
    })
  },
  { deep: true }
)
```

### 3. 调试信息增强
在 `renderSummaryChart` 函数中添加了详细的调试信息：
```javascript
console.log('🎨 renderSummaryChart 调用:', {
  containerId,
  containerFound: !!container,
  chartType: chartItem.chartType,
  dataLength: chartItem.data?.length || 0
})
```

## 数据结构说明

### chartItem 数据结构
```javascript
{
  toolName: "sql_query_chart",
  stepId: "1751937108248001", // 唯一的步骤ID
  chartType: "line",
  data: [...],
  xFields: "month",
  yFields: "value",
  title: "图表标题",
  agentName: "SQL_EXE_CHART_AGENT"
}
```

### 容器ID生成规则
- **新规则**: `summary-chart-${chartItem.stepId}`
- **示例**: `summary-chart-1751937108248001`
- **优势**: 每个工具执行步骤的ID都是唯一的，确保容器ID不会冲突

## 预期效果

1. **唯一性保证**: 每个图表都有唯一的容器ID
2. **正确渲染**: 多个图表可以同时正确显示
3. **实例管理**: 图表实例管理更加清晰
4. **调试友好**: 增加了详细的调试日志

## 测试建议

1. 测试包含多个 `_chart` 工具的对话
2. 测试历史数据加载时的图表显示
3. 检查浏览器控制台的调试信息
4. 验证每个图表容器的ID是否唯一
5. 确认图表实例是否正确创建和销毁

通过这个修复，图表显示问题应该得到解决，每个图表都能正确渲染在自己的容器中。
