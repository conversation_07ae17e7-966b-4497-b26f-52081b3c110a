worker_processes  1;

events {
    worker_connections  1024;
}

http {

    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;
    gzip on;

    server {
        listen 80;
        server_name localhost;

        location / {
            add_header Access-Control-Allow-Origin *;
            root   /usr/share/nginx/html;    # 指定前端项目绝对路径
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;# 处理刷新重定向404问题
        }

        location /api {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods *;
            add_header Access-Control-Allow-Headers *;
            if ($request_method = 'OPTIONS') {
                return 204;
            }
            # 将
            # /api/sys 转发到 http://upms-server.wfwkj.svc.cluster.local:9090/sys
            # /api/sso 转发到 http://upms-server.wfwkj.svc.cluster.local:9090/sso
            proxy_pass http://upms-server.wfwkj.svc.cluster.local:9090/;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}

