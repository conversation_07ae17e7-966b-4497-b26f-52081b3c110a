<template>
  <el-menu
    :default-active="currentRoute.path"
    :class="{
      'el-menu-vertical': isVertical,
    }"
    router
    :mode="mode"
    :collapse="collapsed"
    :text-color="isVertical ? '#fff' : ''"
    :active-text-color="isVertical ? '#fff' : ''"
    :background-color="isVertical ? 'rgba(31, 41, 55, 1)' : ''"
  >
    <template v-for="item in menuList">
      <sys-sub-menu
        v-if="item.children && item.children?.length > 0"
        :item="item"
        :key="item.path"
        :flod-path="flod"
        @change-flod="setFlod"
      />
      <el-menu-item v-else :index="item.path" :key="item.path">
        <sys-icon :type="item.categoryIcon" />
        <!-- <span>{{ item.meta?.title }}</span> -->
        <template #title>{{ item.meta?.title }}</template>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script setup lang="ts" name="SysMenu">
  import { PropType } from 'vue';
  import SysSubMenu from './SysSubMenu.vue';
  import { Menu } from '/@/stores/types';
  import router from '/@/router';
  import { useMenuFlod } from './MenuFlod';
  import { useMenuSettings } from '/@/hooks/settings/useMenuSettings';
  import SysIcon from '/@/components/sys/Icon';

  const { push, currentRoute } = router;
  const { isVertical } = useMenuSettings();

  const props = defineProps({
    menuList: {
      type: Array as PropType<Menu[]>,
      default: () => [],
    },
    mode: {
      type: String as PropType<string>,
      default: 'vertical',
    },
    deeps: {
      type: Number as PropType<number>,
      default: 0,
    },
    isFirstLevel: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    collapsed: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    useI18n: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    i18nType: {
      type: String as PropType<string>,
      default: 'menus.',
    },
  });
  console.log('8888888');
  console.log(props.menuList);

  const { flod, setFlod } = useMenuFlod(props);
  // 解决currentRoute => All destructured elements are unused的问题，使用defineExpose导出
  defineExpose({ push, currentRoute, flod, setFlod });
</script>
<style scoped>
  .el-menu-vertical {
    border-right: 0 !important;
  }

  .el-menu-vertical > .el-menu-item.is-active {
    background-color: rgba(205, 208, 220, 0.8);
  }

  .el-menu-vertical .el-menu-item.is-active .el-menu-tooltip__trigger {

    padding: 0;
  }

  .el-menu--horizontal {
    border-bottom: 0 !important;
  }

  .el-menu-horizontal > .el-menu-item.is-active {
    background-color: rgba(205, 208, 220, 0.8);
  }

  .el-menu--horizontal > .el-menu-item {
    color: #fff;
  }

  :deep(.el-sub-menu__title:hover) {
    background-color: transparent !important;
  }

  .el-sub-menu {
    color: rgba(38, 36, 76, 0.88);

    margin: 8px 18px;
    border-radius: 5px;
  }

  /* :deep(.el-sub-menu__title:hover) {
    background-color: rgba(205, 208, 220, 0.8);
  } */

  .el-menu-item {
    color: rgba(38, 36, 76, 0.88);
    height: 36px;
    line-height: 36px;
    margin: 8px 18px;
    border-radius: 5px;
  }

  :deep(.el-sub-menu__title) {
    color: rgba(38, 36, 76, 0.88) !important;
    height: 36px;
    line-height: 36px;
    border-radius: 5px;
    padding: 3px;
  }

  .el-menu-item:hover {
    background-color: rgba(205, 208, 220, 0.8);
  }

  .el-menu {
    width: 100%;
  }

  .el-menu.el-menu--collapse > .el-menu-item {
    padding: 0 6px;
    margin: 8px 10px;
  }

  .el-menu.el-menu--collapse > .el-sub-menu {
    padding: 0 6px;
    margin: 8px 10px;
  }

  :deep(.el-menu-item .el-menu-tooltip__trigger) {
    padding: 0 8px;
  }
</style>
