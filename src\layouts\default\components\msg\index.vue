<template>
  <el-tabs v-model="activeName" class="tabs" @tab-click="handleClick">
    <el-tab-pane
      v-for="item in tabs"
      :label="item.label"
      :name="item.name"
      :key="item.label"
    >
      <el-empty :image-size="200" v-if="list.length == 0" />
      <ul v-else>
        <li class="msg-item" v-for="msgItem in list" :key="msgItem.type">
          <div class="icon">
            <el-icon v-if="msgItem.type == '3'"><Bell /></el-icon>
            <el-icon v-else-if="msgItem.type == '2'"><User /></el-icon>
            <el-icon v-else><Setting /></el-icon>
          </div>
          <div class="text">
            <div class="msg">
              <div class="content">{{ msgItem.title }}</div>
              <div class="detail" @click="handleDetail(msgItem)">查看详情</div>
            </div>
            <!--<div class="date-name">
              <div class="date">
                <el-icon class="icon"><Clock /></el-icon>
                {{msgItem.sendTime}}
              </div>
              <div class="name"
                ><el-icon class="icon"><User /></el-icon>{{msgItem.creatorName}}</div
              >
            </div>-->
          </div>
        </li>
        <el-pagination
          style="float: right"
          :hide-on-single-page="true"
          :page-size="page.pageSize"
          v-model:current-page="page.pageNum"
          :total="page.total"
          layout="prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </ul>
    </el-tab-pane>
  </el-tabs>
  <el-dialog
    modal-class="model-dialog"
    v-model="viewDialogVisible"
    :width="600"
    title="查看我的消息"
  >
    <div class="view-content">
      <div class="remind">
        <div class="remind-content">
          <div v-dompurify-html="formData.content"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import type { TabsPaneContext } from 'element-plus';
  // import {getMsgList, getMsgId, getNumber} from '/@/views/message/api/msg';
  import { myNotices, getNumber, getNoticeId } from '/@/api/sys';
  const activeName = ref('');
  const viewDialogVisible = ref(false);
  const list = ref([]);
  const formData = ref({});
  // const vueWangeditor = ref();
  const page = ref({
    total: 10,
    pageSize: 20,
    pageNum: 1,
  });
  const tabs = ref([
    {
      label: '全部',
      name: '',
    },
    {
      label: '已读',
      name: '1',
    },
    {
      label: '未读',
      name: '0',
    },
  ]);
  const handleClick = (tab: TabsPaneContext) => {
    console.log(tab.props.name);
    activeName.value = tab.props.name;
    msgList();
  };
  /**
   *请求我的消息列表数据
   */
  const msgList = () => {
    const { pageNum, pageSize } = page.value;
    myNotices({
      pageNum,
      pageSize,
      read: activeName.value,
    }).then((res) => {
      list.value = res.list;
      page.value.total = res.total;
    });
  };
  /**
   * 切换分页，每页显示数量
   */
  // const sizeChange = (val: number) => {
  //   page.value.pageSize = val;
  //   msgList();
  // };
  const handleCurrentChange = (val: number) => {
    page.value.pageNum = val;
    msgList();
  };
  /**
   * 查看我的消息
   */
  const handleDetail = (item) => {
    viewDialogVisible.value = true;
    // let viewData = {
    //   ...item,
    // };
    // formData.value = viewData;
    getNoticeId(item.id).then((res) => {
      formData.value = res;
      // nextTick(() => {
      //   vueWangeditor.value.setHtml(formData.value.content);
      // });
      init();
    });
  };
  /**
   * 消息数
   */
  const getMsgNumber = () => {
    getNumber().then((res) => {
      const { total, read, unread } = res;
      tabs.value = [
        {
          label: '全部(' + total + ')',
          name: '',
        },
        {
          label: '已读(' + read + ')',
          name: '1',
        },
        {
          label: '未读(' + unread + ')',
          name: '0',
        },
      ];
    });
  };
  const init = () => {
    msgList();
    getMsgNumber();
  };
  init();
</script>
<style scoped lang="scss">
  .tabs {
    .msg-item {
      width: 100%;
      display: flex;
      margin-bottom: 20px;
      .icon {
        width: 30px;
        // padding-top: 5px;
        font-size: 20px;
      }
      .text {
        width: 100%;
        .msg {
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
          font-size: 16px;
          .content {
            width: calc(100% - 64px);
          }
          .detail {
            width: 64px;
            color: rgba(59, 130, 246);
            cursor: pointer;
          }
        }
        .date-name {
          display: flex;
          // justify-content: space-between;
          .data {
            margin-right: 10px;
          }
          .icon {
            font-size: 14px;
            position: relative;
            top: 2px;
          }
        }
      }
    }
  }
  .modal-dialog {
    .my-header {
      display: block;
      // flex-direction: row;
      // justify-content: space-between;
      // gap: 16px;
      // .header-right {
      //   .el-icon {
      //     cursor: pointer;
      //     margin-left: 10px;
      //   }
      // }
      .platform {
        width: calc(100% - 20px);
        display: flex;
        justify-content: space-between;
        .right {
          color: #999;
        }
      }
      .form {
        .label {
          color: #999;
        }
      }
    }
    .title {
      width: 100%;
      text-align: center;
      font-size: 20px;
    }
    .view-content {
      color: #999;
      text-indent: 2rem;
      // background: #fff;
    }
  }
</style>
