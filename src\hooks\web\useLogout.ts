import WujieVue from 'wujie-vue3';
import { usemicroState } from '/@/views/micro/libs/microStore';
import { useUserStore } from '/@/stores/modules/user';

export function useLogout() {
  const { destroyApp, bus } = WujieVue;
  const microState = usemicroState();
  const userStore = useUserStore();

  const logout = () => {
    // 子应用取消事件监听
    bus.$clear();
    microState.getAppList.map((item) => {
      bus.$emit('sendMicroMessage', {
        message: '登出',
        type: 'logout',
      });
      destroyApp(item);
    });
    microState.clearApp();
    userStore.logout();
  };

  return { logout };
}
