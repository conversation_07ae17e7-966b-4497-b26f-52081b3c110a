import { FormOptions } from '/@/components/sys/BasicForm/types';
import { TableOptions } from '/@/components/sys/BasicTable/types';

// 表格列配置
export const columns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
  },
  {
    type: 'index',
    width: 60,
    label: '序号',
    align: 'center',
  },
  {
    label: '账号',
    prop: 'username',
    width: 120,
    align: 'center',
  },
  {
    label: '昵称',
    prop: 'name',
    width: 120,
    align: 'center',
  },
  {
    label: '手机号',
    prop: 'phone',
    width: 130,
    align: 'center',
  },
  {
    label: '邮箱',
    prop: 'email',
    width: 180,
    align: 'center',
  },
  {
    label: '性别',
    prop: 'sex',
    width: 80,
    align: 'center',
    slot: 'sex',
  },
  {
    label: '状态',
    prop: 'enabled',
    width: 80,
    align: 'center',
    slot: 'enabled',
  },
  {
    label: '最后登录IP',
    prop: 'loginIp',
    width: 130,
    align: 'center',
  },
  {
    label: '最后登录时间',
    prop: 'loginTime',
    width: 160,
    align: 'center',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    width: 200,
    align: 'center',
  },
];

// 搜索表单配置
export const searchFormSchema: FormOptions[] = [
  {
    field: 'username',
    label: '账号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入账号',
    },
    span: 6,
  },
  {
    field: 'name',
    label: '昵称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入昵称',
    },
    span: 6,
  },
  {
    field: 'phone',
    label: '手机号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入手机号',
    },
    span: 6,
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
    componentProps: {
      placeholder: '请输入邮箱',
    },
    span: 6,
  },
  {
    field: 'sex',
    label: '性别',
    component: 'Select',
    componentProps: {
      placeholder: '请选择性别',
      options: [
        { label: '全部', value: '' },
        { label: '女', value: '0' },
        { label: '男', value: '1' },
        { label: '未知', value: '2' },
      ],
    },
    span: 6,
  },
  {
    field: 'enabled',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' },
      ],
    },
    span: 6,
  },
];

// 用户表单配置
export const userFormSchema: FormOptions[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'username',
    label: '账号',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入账号',
    },
    span: 12,
  },
  {
    field: 'name',
    label: '昵称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入昵称',
    },
    span: 12,
  },
  {
    field: 'phone',
    label: '手机号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入手机号',
    },
    span: 12,
    rules: [
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号',
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
    componentProps: {
      placeholder: '请输入邮箱',
    },
    span: 12,
    rules: [
      {
        type: 'email',
        message: '请输入正确的邮箱格式',
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'sex',
    label: '性别',
    component: 'Select',
    componentProps: {
      placeholder: '请选择性别',
      options: [
        { label: '女', value: '0' },
        { label: '男', value: '1' },
        { label: '未知', value: '2' },
      ],
    },
    span: 12,
  },
  {
    field: 'enabled',
    label: '状态',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' },
      ],
    },
    span: 12,
  },
];
