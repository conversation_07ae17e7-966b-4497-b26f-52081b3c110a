import { Mock<PERSON>ethod } from 'vite-plugin-mock';
enum Api {
  LOGIN = '/sys/doLogin',
  USER_INFO = '/sys/user/info',
  USER_LOGIN_INFO = '/sys/user/loginInfo',
  Logout = '/sys/server-logout',
  checkUrl = '/sys/employee/check',
}

export default [
  {
    url: '/basic-api/_user/login',
    method: 'post',
    response: () => {
      // response: ({ body }) => {
      // const { name, password } = body;
      // const checkUser = createFakeUserList().find(
      //   (item) => item.user.username === name && password === item.user.password,
      // );
      // if (!checkUser) {
      //   return resultError('用户名或者密码错误！');
      // }
      // const { user, roles } = checkUser;
      setTimeout(() => {
        return {
          deptAvliable: true,
          needRestPassword: false,
          user: 'admin',
          roles: [{ roleName: 'Super Admin', value: 'super' }],
          access_token: '8cd1ed51-0670-428d-93bf-df855e1efd22',
          expires_in: 2511977,
          license: 'made by cpsdc',
          need_rest_password: false,
          refresh_token: 'b0fb6d6c-703a-4189-bc2d-c546eb690768',
          scope: 'server',
          token_type: 'bearer',
          model: 'simple',
        };
      }, 1000);
    },
  },
  // {
  //   url: Api.LOGIN,
  //   method: 'post',
  //   response: () => {
  //     return {
  //       message: '成功',
  //       code: 1,
  //       data: {
  //         id: 1,
  //         username: 'admin',
  //         token:
  //           'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoieU1KTnRGdTNPV3dJMTU2WUg1WGQwTldpZjhtdjZ2R1giLCJpZCI6MSwidXNlcm5hbWUiOiJhZG1pbiJ9.Q0jqlmHnCuOX6OIoayxXNZ30_1wNHrysb9DCCWPDU7c',
  //       },
  //     };
  //   },
  // },
  {
    url: Api.USER_INFO,
    method: 'get',
    response: () => {
      return {
        message: '成功',
        code: 1,
        data: {
          id: 1,
          username: 'admin',
          name: '超级管理员',
          sex: '1',
          email: '<EMAIL>',
          phone: '',
          avatar: null,
          createTime: '2025-06-28 16:17:53',
          roles: ['user', 'admin'],
          permissions: [
            'system::users::query',
            'system::menus::query',
            'system::roles::query',
            'system::logs::operlog::query',
            'system::logs::loginlog::query',
            'monitor::online::query',
            'monitor::server::query',
            'monitor::cacheList::query',
            'system::users::insert',
            'system::users::update',
            'system::users::delete',
            'system::users::auth',
            'system::menus::insert',
            'system::menus::update',
            'system::menus::delete',
            'system::roles::insert',
            'system::roles::update',
            'system::roles::delete',
            'system::roles::auth',
            'system::logs::operlog::all',
            'system::logs::loginlog::all',
            'system::files::query',
            'system::files::insert',
            'system::files::delete',
            'tools::gen::query',
            'tools::swagger::query',
            'monitor::online::kickout',
            'monitor::cacheList::delete',
          ],
        },
      };
    },
  },
  {
    url: Api.USER_LOGIN_INFO,
    method: 'get',
    response: () => {
      return {
        message: '成功',
        code: 1,
        result: {
          user: {
            id: '1',
            pageNum: 1,
            pageSize: 10,
            createdBy: '0',
            createdDate: '2022-06-23 15:31:19',
            createdName: 'sys',
            lastModifiedDate: '2022-06-23 15:31:19',
            description: null,
            delFlag: '0',
            version: 0,
            deptId: null,
            deptName: null,
            userName: 'admin',
            userPasswd: '21232f297a57a5a743894a0e4a801fc3',
            realName: 'admin',
            email: '<EMAIL>',
            phone: null,
            cardType: null,
            cardNum: null,
            avatar: null,
            qqOpenId: null,
            vxOpenId: null,
            available: '1',
            tenantId: null,
            projectId: null,
            permissionList: ['tenant_add', 'tenant_delete', 'tenant_edit'],
            roleList: ['administrator'],
            secretkey: 'kQwIOrYvnXmSDkwEiFngrKidMcdrgKor',
            dataScope: {
              scopeName: 'dept_id',
              creatorName: 'created_by',
              deptIds: [],
              userId: '1',
              all: true,
              self: false,
            },
            sysEmployee: {
              createdBy: '1',
              creationDate: '2023-03-30 15:50:14',
              empCode: '00001111',
              empName: '超级管理员',
              gender: 'M',
              id: '1',
              lastModifiedBy: '1',
              lastUpdateDate: '2023-03-30 15:50:14',
              lineId: 0,
              opType: 'system',
              srcSystem: 'upms',
              version: 0,
            },
          },
          permissions: null,
          roles: [1],
          deptAvliable: null,
          needRestPassword: null,
          isSystemAdmin: true,
        },
      };
    },
  },
  {
    url: Api.Logout,
    method: 'post',
    response: () => {
      return { type: 'success', code: 1, message: '操作成功', result: null };
    },
  },
  {
    url: Api.checkUrl,
    method: 'get',
    response: () => {
      return { type: 'success', code: 1, message: '操作成功', result: true };
    },
  },
] as MockMethod[];
