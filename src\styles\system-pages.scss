/* 系统管理页面通用样式 */

// 通用容器样式
.system-page-container {
  padding: 24px;
  background-color: #fff;
  border-radius: 12px;
  margin: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  min-height: calc(100vh - 120px);

  // 页面头部
  .page-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f0f2f5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 22px;
      font-weight: 600;
      color: #1f2937;
      display: flex;
      align-items: center;
      gap: 8px;

      .page-icon {
        color: #3b82f6;
      }
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  // 搜索区域
  .search-section {
    margin-bottom: 20px;
    padding: 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);

    .el-form {
      margin-bottom: 0;
    }

    .el-form-item {
      margin-bottom: 16px;
    }

    .el-input {
      border-radius: 8px;
    }

    .el-button {
      border-radius: 8px;
      font-weight: 500;
    }
  }

  // 操作按钮区域
  .action-section {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 16px 0;

    .el-button {
      border-radius: 8px;
      font-weight: 500;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &.el-button--primary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        border: none;
      }

      &.el-button--info {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        border: none;
      }

      &.el-button--warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border: none;
      }
    }
  }

  // 表格区域
  .table-section {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .el-table {
      border-radius: 12px;
      overflow: hidden;

      .el-table__header {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        
        th {
          background: transparent;
          color: #374151;
          font-weight: 600;
          border-bottom: 2px solid #e5e7eb;
        }
      }

      .el-table__row {
        transition: all 0.3s ease;

        &:hover {
          background-color: #f8fafc;
        }
      }

      .el-table__cell {
        border-bottom: 1px solid #f3f4f6;
      }
    }

    // 分页区域
    .pagination-section {
      margin-top: 0;
      padding: 20px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
      border-top: 1px solid #e5e7eb;

      .pagination-info {
        color: #6b7280;
        font-size: 14px;
      }

      .el-pagination {
        .el-pager li {
          border-radius: 6px;
          margin: 0 2px;
          transition: all 0.3s ease;

          &:hover {
            background-color: #e5e7eb;
          }

          &.is-active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
          }
        }

        .btn-prev,
        .btn-next {
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            background-color: #e5e7eb;
          }
        }
      }
    }
  }
}

// 标签样式优化
.el-tag {
  border-radius: 6px;
  font-weight: 500;
  
  &.el-tag--success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
  }

  &.el-tag--danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: white;
  }

  &.el-tag--primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: none;
    color: white;
  }

  &.el-tag--info {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border: none;
    color: white;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .system-page-container {
    margin: 8px;
    padding: 16px;

    .search-section {
      padding: 16px;

      .el-form--inline .el-form-item {
        display: block;
        margin-right: 0;
      }
    }

    .action-section {
      flex-wrap: wrap;
    }

    .table-section {
      .pagination-section {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }
    }
  }
}
