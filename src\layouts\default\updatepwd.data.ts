import { FormOptions } from '/@/components/sys/BasicForm/types';
import { PwdCheck } from '/@/utils/operate/PwdCheckUtils';

export const updatepwdFormSchema: FormOptions[] = [
  {
    field: 'userName',
    label: '账号名',
    component: 'Text',
  },
  {
    field: 'userPasswd',
    label: '旧密码',
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入旧密码',
    },
    ifShow: true,
    required: true,
  },
  {
    field: 'newPasswd',
    label: '新密码',
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入新密码',
    },
    dynamicRules: [
      {
        required: true,
        validator: (_, value) => {
          const result = PwdCheck(value);
          if (result) {
            return Promise.reject(result);
          }
          return Promise.resolve();
        },
      },
    ],
    ifShow: true,
    required: true,
  },
  {
    field: 'confirmnewPasswd',
    label: '确认新密码',
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入新密码',
    },
    dynamicRules: [
      {
        required: true,
        validator: (_, value) => {
          const result = PwdCheck(value);
          if (result) {
            return Promise.reject(result);
          }
          return Promise.resolve();
        },
      },
    ],
    ifShow: true,
    required: true,
  },
];
