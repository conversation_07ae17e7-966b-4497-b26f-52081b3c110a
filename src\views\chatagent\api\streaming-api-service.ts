/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// 流式事件 API 封装（TypeScript 版，适用于 Vue 项目）

export interface StreamingRequest {
  params: Record<string, any>
  chatId: string
  userId: string
  message: string
  toolContext: Record<string, any>
}

export interface StreamEvent {
  type: string
  planId?: string
  entityId?: string
  payload: string | any
  compressed?: boolean
}

export class StreamingApiService {
  private static readonly STREAMING_URL = '/api/streaming-events'

  /**
   * 发送消息并获取流式响应
   * @param planTemplateId 计划模板ID
   * @param request 请求体
   * @param onEvent 事件处理回调
   * @param onError 错误处理回调
   * @param onComplete 完成回调
   */
  public static async sendMessageWithStreaming(
    planTemplateId: string,
    request: StreamingRequest,
    onEvent: (event: StreamEvent) => void,
    onError: (error: Error) => void,
    onComplete: () => void
  ): Promise<void> {
    try {
      console.log('🚀 发送流式消息请求:', { planTemplateId, request })

      // 构建API URL
      const url = `${this.STREAMING_URL}/template/${encodeURIComponent(planTemplateId)}`

      // 发送POST请求
      const response = await fetch(url, {
        method: 'POST',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      console.log('✅ POST请求成功，开始读取流式响应')

      // 获取响应的reader
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = '' // 用于累积不完整的数据

      // 处理流式响应
      const readStream = async (): Promise<void> => {
        try {
          const { done, value } = await reader.read()

          if (done) {
            console.log('✅ 流式响应完成')

            // 处理缓冲区中剩余的数据
            if (buffer.trim()) {
              this.processSSEChunk(buffer, onEvent)
            }

            onComplete()
            return
          }

          // 解码数据并添加到缓冲区
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          console.log('🔄 收到数据块:', chunk)

          // 按行分割处理完整的事件
          const lines = buffer.split('\n')

          // 保留最后一行（可能不完整）
          buffer = lines.pop() || ''

          // 处理完整的行 - 逐行处理确保实时性
          for (const line of lines) {
            if (line.trim()) {
              this.processSSEChunk(line + '\n', onEvent)
            }
          }

          // 立即继续读取下一块数据，确保实时处理
          return readStream()
        } catch (error) {
          console.error('❌ 读取流式响应失败:', error)
          onError(error as Error)
        }
      }

      await readStream()
    } catch (error) {
      console.error('❌ 发送流式请求失败:', error)
      onError(error as Error)
    }
  }

  /**
   * 处理SSE数据块
   */
  private static processSSEChunk(chunk: string, onEvent: (event: StreamEvent) => void): void {
    console.log('🔍 处理SSE数据块:', chunk)

    const lines = chunk.split('\n')

    for (const line of lines) {
      const trimmedLine = line.trim()

      if (trimmedLine.startsWith('data:')) {
        const eventData = trimmedLine.substring(5).trim() // 移除 "data:" 前缀并去除空格

        if (eventData && eventData !== '') {
          try {
            console.log('📨 解析SSE事件数据:', eventData)
            const parsedData = JSON.parse(eventData)
            onEvent(parsedData)
          } catch (e) {
            console.error('❌ 解析SSE事件失败:', e, eventData)
          }
        }
      } else if (trimmedLine === '') {
        // 空行表示事件结束，这是SSE标准格式
        continue
      } else if (trimmedLine.startsWith('event:') || trimmedLine.startsWith('id:') || trimmedLine.startsWith('retry:')) {
        // 其他SSE字段，暂时忽略
        console.log('📋 SSE元数据:', trimmedLine)
      }
    }
  }

  /**
   * 获取聊天历史
   * @param chatId 聊天ID
   */
  public static async getChatHistory(chatId: string): Promise<any> {
    try {
      const response = await fetch(`${this.STREAMING_URL}/chat/history/${encodeURIComponent(chatId)}`)
      if (!response.ok) {
        throw new Error(`获取聊天历史失败: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error('获取聊天历史失败:', error)
      throw error
    }
  }

  /**
   * 获取聊天会话列表
   * @param userId 用户ID
   */
  public static async getChatSessions(userId: string): Promise<any> {
    try {
      const response = await fetch(`${this.STREAMING_URL}/chat/sessions/${encodeURIComponent(userId)}`)
      if (!response.ok) {
        throw new Error(`获取聊天会话列表失败: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error('获取聊天会话列表失败:', error)
      throw error
    }
  }

  /**
   * 删除聊天会话
   * @param chatId 聊天ID
   */
  public static async deleteChatSession(chatId: string): Promise<any> {
    try {
      const response = await fetch(`${this.STREAMING_URL}/chat/session/${encodeURIComponent(chatId)}`, {
        method: 'DELETE'
      })
      if (!response.ok) {
        throw new Error(`删除聊天会话失败: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error('删除聊天会话失败:', error)
      throw error
    }
  }
}
