import { defineStore } from 'pinia';
import { store } from '/@/stores';
import { reqDict } from '/@/api/sys/dict';
import { Dict, ResDict } from '/#/store';
import { getDictStorage, setDictStorage } from '/@/utils/storage/dict';

interface CodeState {
  codeDict: ResDict;
}

export const useDictStore = defineStore({
  id: 'app-dict',
  state: (): CodeState => ({
    codeDict: {},
  }),
  actions: {
    get(code: string): Dict[] {
      return this.codeDict[code] || getDictStorage()[code];
    },
    setCodeDict(codeDict: ResDict) {
      this.codeDict = codeDict;
      setDictStorage(codeDict);
    },
    reqDict() {
      // 读取数据
      reqDict().then((res) => {
        this.setCodeDict(res);
      });
    },
  },
});

export function useDictStoreWithOut() {
  return useDictStore(store);
}
