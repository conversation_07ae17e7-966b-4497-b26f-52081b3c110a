import { MockMethod } from 'vite-plugin-mock';
enum Api {
  QUERY_TENANT = '/sys/tenant/qryTenant',
  QUERY_PROJECT = '/sys/project/qryProject',
  // PROJECT =
}

export default [
  {
    url: Api.QUERY_TENANT,
    method: 'get',
    response: () => {
      return {
        message: '成功',
        code: 1,
        result: [
          {
            createdBy: '1',
            createdDate: '2022-08-30 22:59:23',
            createdName: 'admin',
            lastModifiedBy: '1',
            lastModifiedDate: '2022-08-31 06:59:33',
            version: 1,
            description: '低代码平台',
            id: '1564870444796116994',
            tenantName: '低代码平台',
            tenantAlias: 'teamcode',
            adminId: '1564870330794934274',
            adminName: null,
          },
        ],
      };
    },
  },
  {
    url: Api.QUERY_PROJECT,
    method: 'get',
    response: () => {
      return {
        message: '成功',
        code: 1,
        result: [
          {
            createdBy: '1564870330794934274',
            createdDate: '2022-08-31 07:08:01',
            createdName: 'lishougang',
            lastModifiedBy: '1564870330794934274',
            lastModifiedDate: '2022-08-31 07:08:01',
            version: 0,
            description: '低代码平台',
            id: '1564872619266007041',
            tenantId: '1564870444796116994',
            projectName: '低代码平台',
            projectAlias: 'teamcode',
            adminId: '1564870330794934274',
            adminName: null,
            tenantName: null,
          },
          {
            createdBy: '1',
            createdDate: '2022-09-09 01:34:45',
            createdName: 'admin',
            lastModifiedBy: '1',
            lastModifiedDate: '2022-09-09 01:34:45',
            version: 0,
            description: '',
            id: '1568050238727618561',
            tenantId: '1564870444796116994',
            projectName: '0909-1-test',
            projectAlias: '09091test',
            adminId: '1564870330794934274',
            adminName: null,
            tenantName: null,
          },
        ],
      };
    },
  },
] as MockMethod[];
