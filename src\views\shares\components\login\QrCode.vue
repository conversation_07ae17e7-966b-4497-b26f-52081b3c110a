<template>
  <div class="h-86" v-loading="codeLoading">
    <qrcode-vue :size="220" :value="JSON.stringify(qrCodeData)" class="login-code" />
    <span class="login-code-span">请使用邮我行APP进行扫码登录</span>
  </div>
</template>

<script setup lang="ts">
  import QrcodeVue from 'qrcode.vue';
  import { PropType, ref, watch } from 'vue';
  import { getCrtLogin, getLoginCode } from '/@/api/sys';
  import { LoginEnum } from '/@/enums/appEnum';
  import { useUserStore } from '/@/stores/modules/user';
  import { useIntervalFn } from '@vueuse/core';

  const props = defineProps({
    currentType: {
      type: String as PropType<LoginEnum>,
      default: '',
    },
  });

  const qrCodeData = ref({
    businessData: '',
    businessType: 'login',
    systemID: '10011',
    toPage: '',
    codeId: '',
  });

  const codeLoading = ref(false);
  // 扫码登录时候成功标识
  const isSuccess = ref(false);
  const userStore = useUserStore();

  const { pause, resume } = useIntervalFn(
    async () => {
      isSuccess.value = await getCrtLogin({ codeId: qrCodeData.value.codeId });
      if (isSuccess.value) {
        pause();
        userStore.crtlogin(isSuccess.value);
      }
    },
    3000,
    { immediate: false },
  );

  watch(
    () => props.currentType,
    (value) => {
      if (value !== LoginEnum.QrCode) return;
      codeLoading.value = true;
      getLoginCode().then((data) => {
        qrCodeData.value = data;
        codeLoading.value = false;
        resume();
      });
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style scoped>
  .login-code {
    margin: 20px 40px;
  }

  .login-code-span {
    display: inline-block;
    width: 100%;
    text-align: center;
    color: #409eff;
  }
</style>
