import { useAppStore } from '/@/stores/modules/app';
import { computed, unref } from 'vue';
import { Layout } from '/@/enums/appEnum';

export function useMenuSettings() {
  const appStore = useAppStore();

  const getMenuCollapsed = computed(() => appStore.getMenuSetting?.collapsed);
  const isVertical = computed(
    () => appStore.getProjectConfig.layout === Layout.VERTICAL,
  );
  const isHorizontal = computed(
    () => appStore.getProjectConfig.layout === Layout.HORIZONTAL,
  );

  // Set menu configuration
  function setMenuSetting(menuSetting: Partial<MenuSetting>): void {
    appStore.setProjectConfig({ menuSetting });
  }

  function toggleCollapsed() {
    setMenuSetting({
      collapsed: !unref(getMenuCollapsed),
    });
  }

  function toggleClose() {
    setMenuSetting({
      collapsed: true,
    });
  }

  function toggleOpen() {
    setMenuSetting({
      collapsed: false,
    });
  }

  return {
    getMenuCollapsed,
    setMenuSetting,
    toggleCollapsed,
    toggleClose,
    toggleOpen,
    isVertical,
    isHorizontal,
  };
}
