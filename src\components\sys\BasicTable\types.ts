export interface TableOptions {
  label: string; // 表头
  prop?: string; // 字段名称
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  slot?: string; // 自定义slot
  action?: boolean;
  fixed?: string;
  ellipsis?: Boolean; //一行展示，溢出展示...,添加title
  minWidth?: string | number; //最小宽度
  hideColumn?: Function | Boolean; //隐藏列
  sortable?: boolean | string; //是否展示排序
  headerSlot?: string; // 自定义表头slot
  initShow?: boolean; // 初始化时是否展示，与右上角setting功能联动
  type?: string; // selection / index / expand
  selectable?: Function | boolean;
  tooltip?: Boolean;
  headerAlign?: string;
  downSetting?: Boolean;
  height?: string;
}
