<template>
  <div style="border: 1px solid #ccc">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      v-show="!readOnly"
    />
    <Editor
      style="overflow-y: hidden; height: 350px"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      @onCreated="handleCreated"
    />
  </div>
</template>
<script setup lang="ts">
  import { useUserStore } from '/@/stores/modules/user';
  import { getAuthToken } from '/@/utils/storage/auth';
  import { onBeforeUnmount, ref, shallowRef } from 'vue';
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
  const userStore = useUserStore();
  const props = defineProps({
    readOnly: { type: Boolean, default: false },
  });
  // export default {
  //   components: { Editor, Toolbar },
  // setup() {
  // 编辑器实例，必须用 shallowRef
  const editorRef = shallowRef();

  // 内容 HTML
  const valueHtml = ref('');

  // 模拟 ajax 异步获取内容
  // onMounted(() => {
  //   setTimeout(() => {
  //     valueHtml.value = '<p>模拟 Ajax 异步设置内容</p>';
  //   }, 1500);
  // });

  const toolbarConfig = {
    /* 工具栏配置 */
    toolbarKeys: [
      'headerSelect',
      'bold',
      'italic',
      // 'justify',
      // 'list',
      'clearStyle',
      'color',
      'bgColor',
      '|',
      // 菜单组，包含多个菜单
      // TODO等待后台上传图片地址
      {
        key: 'group-image', // 必填，要以 group 开头
        title: '图片', //必填
        // iconSvg: '<svg>insertImage</svg>',
        menuKeys: [
          'uploadImage',
          // 'insertImage',
          'deleteImage',
          'editImage',
          // 'viewImageLink',
        ],
      },
      // {
      //   key: 'group-video',
      //   title: '视频',
      //   iconSvg: '',
      //   menuKeys: ['insertVideo', 'uploadVideo'],
      // },
      {
        key: 'group-link',
        title: '链接',
        menuKeys: ['insertLink', 'editLink', 'unLink', 'viewLink'],
        color: '#ff0000',
        colors: [
          '#1c487f', // 深蓝
          '#4d80bf', // 中蓝
          '#ff0000', // 红色
        ],
      },
      // {
      //   key: 'group-table',
      //   title: '表格',
      //   menuKeys: [
      //     'insertTable',
      //     'deleteTable',
      //     'insertTableRow',
      //     'deleteTableRow',
      //     'insertTableCol',
      //     'deleteTableCol',
      //     'tableHeader',
      //     'tableFullWidth',
      //   ],
      // },
      'divider',
      'emotion', //表情
      'blockquote',
      'redo',
      'undo',
      'fullScreen', // 全屏
    ],
  };
  const editorConfig = {
    placeholder: '请输入内容...',
    MENU_CONF: {},
    readOnly: props.readOnly,
    colors: [
      '#1c487f', // 深蓝
      '#4d80bf', // 中蓝
      '#ff0000', // 红色
    ],
  };
  editorConfig.MENU_CONF['uploadImage'] = {
    server: '/api/msg/multiMedia/upload',
    fieldName: 'file',
    headers: {
      Authorization: getAuthToken(),
      tenantId: userStore.getUserInfo?.tenantId || '',
      projectId: userStore.getUserInfo?.projectId || '',
    },
    withCredentials: true,
    customInsert(res, insertFn) {
      const url = res.result;
      insertFn(url, '', '');
    },
  };
  // editorConfig.uploadImgServer = '/api/upload';

  // 组件销毁时，也及时销毁编辑器
  onBeforeUnmount(() => {
    const editor = editorRef.value;
    if (editor == null) return;
    editor.destroy();
  });

  const handleCreated = (editor) => {
    editorRef.value = editor; // 记录 editor 实例，重要！
  };
  const setHtml = (html) => {
    editorRef.value.setHtml(html);
  };
  // const getHtml = () => {
  //   console.log('valueHtml', valueHtml);
  //   // // return this.editor.txt.html()
  //   return editor.getHtml();
  // };

  // return {
  //   editorRef,
  //   valueHtml,
  //   mode: 'simple', // 'default', // 或 'simple'
  //   toolbarConfig,
  //   editorConfig,
  //   handleCreated,
  //   setHtml,
  //   // getHtml,
  // };
  // },
  // };
  defineExpose({ editorRef, valueHtml, toolbarConfig, handleCreated, setHtml });
</script>
<style>
  @import url('/resource/wangeditor/editor/css/style.css');

  .w-e-text-container {
    width: 100% !important; /* 确保容器宽度正确继承 */
    word-break: break-all; /* 允许任意字符处断行 */
    overflow-wrap: break-word; /* 优先在单词内断行 */
    white-space: normal !important; /* 覆盖可能的 nowrap 设置 */
  }

  .w-e-text-container a {
    color: red !important;
  }
</style>
