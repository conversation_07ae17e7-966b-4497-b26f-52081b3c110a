.wave {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: -1;
  height: 100%;
}

.login-container {
  display: grid;

  /* grid-template-columns: repeat(2, 1fr); */
  grid-gap: 18rem;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  padding: 0 2rem;
}

.img {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.img img {
  width: 500px;
}

.login-box {
  display: flex;
  align-items: center;
  text-align: center;
}

.login-form {
  width: 360px;
}

.avatar {
  width: 350px;
  height: 80px;
}

.login-form h2 {
  margin: 15px 0;
  font: bold 200% Consolas, Monaco, monospace;
  color: #999;
  text-transform: uppercase;
}

.input-group {
  position: relative;
  display: grid;
  grid-template-columns: 7% 93%;
  padding: 5px 0;
  margin: 25px 0;
  border-bottom: 2px solid #d9d9d9;
}

.input-group:nth-child(1) {
  margin-bottom: 4px;
}

.input-group::before,
.input-group::after {
  position: absolute;
  bottom: -2px;
  width: 0;
  height: 2px;
  content: "";
  background-color: #c5d3f7;
  transition: 0.5s;
}

.input-group::after {
  right: 50%;
}

.input-group::before {
  left: 50%;
}

.icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon svg {
  color: #d9d9d9;
  transition: 0.5s;
}

.input-group > div {
  position: relative;
  height: 45px;
}

.input-group > div > h5 {
  position: absolute;
  top: 50%;
  left: 10px;
  padding: 0;
  margin: 0;
  font-size: 18px;
  color: #d9d9d9;
  transition: 0.3s;
  transform: translateY(-50%);
}

.input-group.focus .icon svg {
  color: #5392f0;
}

.input-group.focus div h5 {
  top: -5px;
  font-size: 15px;
}

.input-group.focus::after,
.input-group.focus::before {
  width: 50%;
}

.input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0.5rem 0.7rem;
  font-family: "Roboto", sans-serif;
  font-size: 1.2rem;
  color: #555;
  background: none;
  border: none;
  outline: none;
}

a {
  display: block;
  font-size: 0.9rem;
  color: #999;
  text-align: right;
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: #5392f0;
}

.btn {
  display: block;
  width: 100%;
  height: 50px;
  margin: 1rem 0;
  font-family: "Roboto", sans-serif;
  font-size: 1.2rem;
  color: #fff;
  text-transform: uppercase;
  cursor: pointer;
  background-size: 200%;
  border: none;
  border-radius: 25px;
  outline: none;
  transition: 0.5s;
}

.copyright {
  position: absolute;
  bottom: 2px;
  width: 100%;
  height: 50px;
  font-family: "Roboto", sans-serif;
  font-size: 18px;
  color: #5392f0;
  text-align: center;
}

@media screen and (max-width: 1080px) {
  .login-container {
    grid-gap: 9rem;
  }
}

@media screen and (max-width: 1024px) {
  .login-form {
    width: 290px;
  }

  .login-form h2 {
    margin: 8px 0;
    font-size: 2.4rem;
  }

  .img img {
    width: 360px;
  }

  .avatar {
    width: 280px;
    height: 80px;
  }
}

@media screen and (max-width: 768px) {
  .wave {
    display: none;
  }

  .img {
    display: none;
  }

  .login-container {
    grid-template-columns: 1fr;
  }

  .login-box {
    justify-content: center;
  }
}



