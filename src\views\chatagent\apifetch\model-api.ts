/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 大模型API服务
 * 基于统一的fetch封装实现
 */

import { ApiClient } from './api-client'
import type { Model, RequestOptions, StandardApiResponse } from './types'

// 模型API响应接口
export interface ModelResponse extends StandardApiResponse<Model[]> {}

export interface ModelDetailResponse extends StandardApiResponse<Model> {}

export interface ModelTestResponse extends StandardApiResponse<{ available: boolean; message?: string }> {}

export class ModelApi extends ApiClient {
  constructor() {
    super('/cpit/ai/setting/model', {
      enableLogging: true,
      enableCache: true,
      defaultCacheTime: 10 * 60 * 1000, // 10分钟缓存
      enableRetry: true,
      defaultRetryCount: 2
    })
  }

  /**
   * 获取大模型简要列表
   */
  async getBriefModelList(options?: RequestOptions): Promise<Model[]> {
    try {
      const response = await this.get<ModelResponse | Model[]>('/brief/list', undefined, {
        cache: true,
        cacheTime: 10 * 60 * 1000, // 10分钟缓存
        ...options
      })

      // 处理不同的响应格式
      if (Array.isArray(response)) {
        return response
      }

      if (response && typeof response === 'object' && 'data' in response) {
        return response.data || []
      }

      return []
    } catch (error) {
      console.error('获取大模型简要列表失败:', error)
      throw new Error(`获取大模型列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取所有大模型列表（详细信息）
   */
  async getAllModels(options?: RequestOptions): Promise<Model[]> {
    try {
      const response = await this.get<ModelResponse | Model[]>('/list', undefined, {
        cache: true,
        cacheTime: 10 * 60 * 1000, // 10分钟缓存
        ...options
      })

      // 处理不同的响应格式
      if (Array.isArray(response)) {
        return response
      }

      if (response && typeof response === 'object' && 'data' in response) {
        return response.data || []
      }

      return []
    } catch (error) {
      console.error('获取大模型详细列表失败:', error)
      throw new Error(`获取大模型列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据模型ID获取详细信息
   */
  async getModelById(id: string, options?: RequestOptions): Promise<Model | null> {
    try {
      const response = await this.get<ModelDetailResponse | Model>(`/${encodeURIComponent(id)}`, undefined, {
        cache: true,
        cacheTime: 10 * 60 * 1000, // 10分钟缓存
        ...options
      })

      // 处理不同的响应格式
      if (response && typeof response === 'object') {
        if ('data' in response) {
          return response.data || null
        }
        
        if ('id' in response) {
          return response as Model
        }
      }

      return null
    } catch (error) {
      console.error('获取大模型详情失败:', error)
      return null
    }
  }

  /**
   * 测试模型可用性
   */
  async testModel(id: string, options?: RequestOptions): Promise<boolean> {
    try {
      const response = await this.post<ModelTestResponse | { success: boolean }>(
        `/${encodeURIComponent(id)}/test`,
        {},
        {
          retry: false, // 测试不重试
          timeout: 30000, // 30秒超时
          ...options
        }
      )

      // 处理不同的响应格式
      if (response && typeof response === 'object') {
        if ('success' in response) {
          return response.success === true
        }
        
        if ('data' in response && response.data) {
          return response.data.available === true
        }
      }

      return false
    } catch (error) {
      console.error('测试大模型失败:', error)
      return false
    }
  }

  /**
   * 获取默认模型ID
   */
  async getDefaultModelId(options?: RequestOptions): Promise<string | null> {
    try {
      const response = await this.get<ModelDetailResponse | { id: string }>('/default', undefined, {
        cache: true,
        cacheTime: 5 * 60 * 1000, // 5分钟缓存
        ...options
      })

      // 处理不同的响应格式
      if (response && typeof response === 'object') {
        if ('data' in response && response.data && response.data.id) {
          return response.data.id
        }
        
        if ('id' in response) {
          return response.id
        }
      }

      return null
    } catch (error) {
      console.error('获取默认模型失败:', error)
      return null
    }
  }

  /**
   * 创建模型配置
   */
  async createModel(model: Omit<Model, 'id'>, options?: RequestOptions): Promise<Model> {
    try {
      const response = await this.post<Model>('/', model, options)
      
      // 清除缓存
      this.clearCache()
      
      return response
    } catch (error) {
      console.error('创建模型失败:', error)
      throw new Error(`创建模型失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 更新模型配置
   */
  async updateModel(id: string, model: Partial<Model>, options?: RequestOptions): Promise<Model> {
    try {
      const response = await this.put<Model>(`/${encodeURIComponent(id)}`, model, options)
      
      // 清除缓存
      this.clearCache()
      
      return response
    } catch (error) {
      console.error('更新模型失败:', error)
      throw new Error(`更新模型失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 删除模型配置
   */
  async deleteModel(id: string, options?: RequestOptions): Promise<void> {
    try {
      await this.delete(`/${encodeURIComponent(id)}`, options)
      
      // 清除缓存
      this.clearCache()
    } catch (error) {
      console.error('删除模型失败:', error)
      throw new Error(`删除模型失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 批量测试模型
   */
  async batchTestModels(ids: string[], options?: RequestOptions): Promise<Record<string, boolean>> {
    try {
      const requests = ids.map(id => ({
        url: `/${encodeURIComponent(id)}/test`,
        method: 'POST' as const,
        data: {}
      }))

      const results = await this.batchRequest<ModelTestResponse | { success: boolean }>(requests, options)
      
      const testResults: Record<string, boolean> = {}
      ids.forEach((id, index) => {
        const result = results[index]
        if (result && typeof result === 'object') {
          if ('success' in result) {
            testResults[id] = result.success === true
          } else if ('data' in result && result.data) {
            testResults[id] = result.data.available === true
          }
        } else {
          testResults[id] = false
        }
      })

      return testResults
    } catch (error) {
      console.error('批量测试模型失败:', error)
      throw new Error(`批量测试模型失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 搜索模型
   */
  async searchModels(
    keyword: string, 
    provider?: string, 
    type?: string, 
    options?: RequestOptions
  ): Promise<Model[]> {
    try {
      const params = this.buildQueryParams({
        keyword,
        provider,
        type
      })

      const response = await this.get<ModelResponse | Model[]>('/search', params, options)

      // 处理不同的响应格式
      if (Array.isArray(response)) {
        return response
      }

      if (response && typeof response === 'object' && 'data' in response) {
        return response.data || []
      }

      return []
    } catch (error) {
      console.error('搜索模型失败:', error)
      throw new Error(`搜索模型失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 格式化模型显示名称
   */
  formatModelDisplayName(model: Model): string {
    if (model.configName && model.model) {
      return `【${model.configName}】 | ${model.model}`
    } else if (model.configName) {
      return `【${model.configName}】`
    } else if (model.model) {
      return model.model
    } else if (model.name) {
      return model.name
    }
    return `模型 ${model.id}`
  }

  /**
   * 检查模型是否支持流式输出
   */
  isStreamingSupported(model: Model): boolean {
    return model.supportStreaming === true
  }

  /**
   * 根据类型过滤模型
   */
  filterModelsByType(models: Model[], type: string): Model[] {
    return models.filter(model => model.type === type)
  }

  /**
   * 根据提供商过滤模型
   */
  filterModelsByProvider(models: Model[], provider: string): Model[] {
    return models.filter(model => model.provider === provider)
  }

  /**
   * 获取可用的模型提供商列表
   */
  async getProviders(options?: RequestOptions): Promise<string[]> {
    try {
      const models = await this.getAllModels(options)
      const providers = new Set<string>()
      
      models.forEach(model => {
        if (model.provider) {
          providers.add(model.provider)
        }
      })
      
      return Array.from(providers).sort()
    } catch (error) {
      console.error('获取提供商列表失败:', error)
      return []
    }
  }

  /**
   * 获取可用的模型类型列表
   */
  async getModelTypes(options?: RequestOptions): Promise<string[]> {
    try {
      const models = await this.getAllModels(options)
      const types = new Set<string>()
      
      models.forEach(model => {
        if (model.type) {
          types.add(model.type)
        }
      })
      
      return Array.from(types).sort()
    } catch (error) {
      console.error('获取模型类型列表失败:', error)
      return []
    }
  }
}

// 导出单例实例
export const modelApi = new ModelApi()
