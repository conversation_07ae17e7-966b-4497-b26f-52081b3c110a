import { describe, expect, test } from 'vitest';
import { hexToRGB, isHexColor } from '../../src/utils/color';

describe('颜色判断、转换工具类', () => {
  test('是否符合颜色字符串规范', () => {
    expect(isHexColor('#fff000')).toEqual(true);
    expect(isHexColor('#f00')).toEqual(true);
    expect(isHexColor('#f001')).toEqual(false);
  });

  test('颜色字符串转RGB', () => {
    const result = hexToRGB('#fff000');

    expect(result).toEqual('RGB(255,240,0)');
  });
});
