import { defHttp } from '/@/utils/axios';
import { DeptInfo, ProjectInfo, TenantInfo } from '/@/api/model/Tenant';

enum Api {
  QUERY_TENANT = '/sys/tenant/qryTenant',
  QUERY_PROJECT = '/sys/project/qryProject',
  QUERY_DEPT = '/sys/dept/getProjectDeptByUserId/',
  // PROJECT =
}

/**
 * 获取租户列表
 */
export const getTenantList = () => {
  console.log('1125')
  return defHttp.get<TenantInfo[]>({ url: Api.QUERY_TENANT });
};

/**
 * 获取应用列表
 */
export const getProjectList = () => {
  return defHttp.get<ProjectInfo[]>(
    {
      url: Api.QUERY_PROJECT,
    },
    { joinParamsToUrl: true },
  );
};

/**
 * 获取用户在指定租户、应用下绑定的机构
 */
export const getUserProjectDeptList = () => {
  return defHttp.get<DeptInfo[]>(
    {
      url: Api.QUERY_DEPT,
    },
    { joinParamsToUrl: true },
  );
};
