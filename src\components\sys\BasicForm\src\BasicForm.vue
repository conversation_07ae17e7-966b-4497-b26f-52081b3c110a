<template>
  <div class="form-all">
    <el-form
      :destroy-on-close="destroyOnClose"
      v-bind="$attrs"
      ref="ruleFormRef"
      :model="formData"
      :rules="formRules"
      class="basic-form"
      :label-width="isNumber(labelWidth) ? `${labelWidth}px` : labelWidth"
    >
      <el-row class="w-full" :gutter="10">
        <template v-for="item in formList" :key="item.field">
          <el-col
            :span="item.span"
            v-if="
              typeof item.ifShow === 'function'
                ? item.ifShow(formData) !== false && !item.slot
                : item.ifShow !== false && !item.slot
            "
          >
            <el-form-item
              v-if="!!item.formItemFlag || item.formItemFlag !== false"
              v-show="item.show !== false"
              :label="item.label"
              :prop="item.field"
              :class="showIcon ? 'el-form-item-area' : ''"
            >
              <el-input
                v-if="
                  item.component === 'Input' ||
                  item.component === 'InputPassword' ||
                  item.component === 'InputTextArea'
                "
                :placeholder="item.componentProps?.placeholder || '请输入...'"
                :rows="item.componentProps?.rows || 2"
                v-model="formValue[item.field]"
                :disabled="item.componentProps?.disabled"
                :clearable="item.componentProps?.clearable"
                :name="item.label"
                :required="item.required"
                :minlength="item.componentProps?.minlength"
                :maxlength="item.componentProps?.maxLength"
                :show-word-limit="item.componentProps?.maxLength ? true : false"
                :autocomplete="item.componentProps?.autocomplete || 'new-password'"
                @input="item.onInput"
                :show-password="item.component === 'InputPassword'"
                :type="
                  item.component === 'InputPassword'
                    ? 'password'
                    : item.component === 'InputTextArea'
                    ? 'textarea'
                    : 'text'
                "
              >
                <template v-if="item.componentProps?.appendText" #append>{{
                  item.componentProps?.appendText
                }}</template>
                <template v-if="item.componentProps?.prependText" #prepend>{{
                  item.componentProps?.prependText
                }}</template>
              </el-input>
              <el-input-number
                :placeholder="item.componentProps?.placeholder || '请输入数字...'"
                :max="item.componentProps?.max"
                :min="item.componentProps?.min"
                :step="item.componentProps?.step"
                v-else-if="item.component === 'InputNumber'"
                v-model="formValue[item.field]"
              />
              <el-radio-group
                v-else-if="item.component === 'RadioGroup'"
                v-model="formValue[item.field]"
                @change="item.onChange"
                :disabled="item.componentProps?.disabled"
              >
                <el-radio
                  v-for="radio in item.componentProps?.options"
                  :key="radio.value"
                  :value="radio.value"
                  >{{ radio.label }}</el-radio
                >
              </el-radio-group>
              <el-checkbox-group
                v-else-if="item.component === 'CheckboxGroup'"
                v-model="formValue[item.field]"
                @change="item.onChange"
              >
                <el-checkbox
                  v-for="check in item.componentProps?.options"
                  :key="check.value"
                  :value="check.value"
                  >{{ check.label }}</el-checkbox
                >
              </el-checkbox-group>
              <el-select
                v-else-if="item.component === 'Select'"
                v-model="formValue[item.field]"
                :clearable="item.componentProps?.clearable"
                :disabled="item.componentProps?.disabled"
                :filterable="item.filterable"
                :placeholder="item.componentProps?.placeholder || '请选择...'"
                @change="item.onChange"
              >
                <el-option
                  v-for="option in item.componentProps?.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <el-select
                v-else-if="item.component === 'SelectMultiple'"
                v-model="formValue[item.field]"
                :clearable="item.componentProps?.clearable"
                :placeholder="item.componentProps?.placeholder || '请选择...'"
                multiple
                @change="item.onChange"
                :disabled="item.componentProps?.disabled"
                :value-key="item.componentProps?.valueKey || 'value'"
              >
                <el-option
                  v-for="option in item.componentProps?.options"
                  :key="option.value"
                  :label="option.label"
                  multiple
                  :value="item.componentProps?.useAll ? option : option.value"
                />
              </el-select>
              <el-tree-select
                v-else-if="item.component === 'TreeSelect'"
                ref="treeSelectRef"
                :placeholder="item.componentProps?.placeholder || '请选择...'"
                v-model="formValue[item.field]"
                :data="item.componentProps?.data"
                :multiple="item.componentProps?.multiple"
                :render-after-expand="false"
                :show-checkbox="item.componentProps?.showCheckbox"
                :node-key="item.componentProps?.nodeKey"
                :check-strictly="checkStrictly"
                @change="treeSelChange"
              />
              <el-date-picker
                v-else-if="item.component === 'DatePicker'"
                v-model="formValue[item.field]"
                :type="item.componentProps?.type || 'date'"
                placeholder="选择时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
              <div v-else-if="item.component === 'Text'">
                <span v-text="formValue[item.field] || '--'"></span>
              </div>
              <el-switch
                v-else-if="item.component === 'Switch'"
                v-model="formValue[item.field]"
                :style="item.componentProps?.style || ''"
                :active-text="item.componentProps?.activeText || ''"
                :inactive-text="item.componentProps?.inactiveText || ''"
              />
              <template v-else>
                <component
                  v-model="formValue[item.field]"
                  :is="'el-' + item.component"
                  :type="item.componentProps?.type || null"
                  :is-range="item.componentProps?.isRange || false"
                  :options="item.componentProps?.optionItems || null"
                  :data="item.componentProps?.optionItems || null"
                />
              </template>
              <el-icon
                v-if="showIcon"
                :title="item.componentProps?.iconTitle"
                class="el-form-item-icon"
                ><component :is="item.componentProps?.iconType" />
              </el-icon>
              <!-- <el-icon><InfoFilled /></el-icon> -->
            </el-form-item>
            <template v-else>
              <component
                v-model="formValue[item.field]"
                :is="'el-' + item.component"
                :type="item.componentProps?.type || null"
                :is-range="item.componentProps?.isRange || false"
                :options="item.componentProps?.optionItems || null"
                :data="item.componentProps?.optionItems || null"
              />
            </template>
          </el-col>
          <el-col
            :span="item.span"
            v-if="
              item.slot &&
              (typeof item.ifShow === 'function'
                ? item.ifShow(formData) !== false
                : item.ifShow !== false)
            "
          >
            <el-form-item
              v-if="item?.formItemFlag !== false"
              :label="item.label"
              :prop="item.field"
              :class="showIcon ? 'el-form-item-area' : ''"
            >
              <slot
                :name="item.slot"
                :value="formValue[item.field]"
                :record="formValue"
                :field="item.field"
              ></slot>
              <el-icon
                v-if="showIcon"
                :title="item.componentProps?.iconTitle"
                class="el-form-item-icon"
                ><component :is="item.componentProps?.iconType" />
              </el-icon>
            </el-form-item>
            <slot
              v-else
              :name="item.slot"
              :value="formValue[item.field]"
              :record="formValue"
              :field="item.field"
            ></slot>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <slot name="footer">
      <div class="form-bottom">
        <el-button
          v-auth="auth"
          v-show="showSubmit"
          :disabled="disableSubmit"
          type="primary"
          @click="submitForm(ruleFormRef)"
          >保存</el-button
        >
        <el-button v-show="showReset" @click="resetForm(ruleFormRef)">重置</el-button>
      </div>
    </slot>
  </div>
</template>
<script setup lang="ts">
  import { ref, watch, PropType } from 'vue';
  import type { FormInstance } from 'element-plus';
  import { FormOptions } from '/@/components/sys/BasicForm/types';
  import { isDef, isNullOrUnDef, isNumber } from '/@/utils/is';
  import { cloneDeep as ldCloneDeep } from 'lodash-es';
  const props = defineProps({
    destroyOnClose: {
      // 关闭后是否销毁
      type: Boolean as PropType<boolean>,
      default: true,
    },
    formList: {
      type: Array as PropType<FormOptions[]>,
      default: () => [],
      required: true,
    },
    isCreate: {
      // 是否为创建新表单
      type: Boolean as PropType<boolean>,
      default: true,
    },
    isCloneDeep: {
      // 是否需要深拷贝
      type: Boolean as PropType<boolean>,
      default: false,
    },
    labelWidth: {
      type: [String, Number] as PropType<string | string>,
      default: '120px',
    },
    formData: {
      type: Object,
      default: () => {},
      required: true,
    },
    showSubmit: {
      // 是否显示保存按钮
      type: Boolean as PropType<boolean>,
      default: true,
    },
    disableSubmit: {
      // 保存按钮是否可用
      type: Boolean as PropType<boolean>,
      default: false,
    },
    showReset: {
      // 是否显示重置按钮
      type: Boolean as PropType<boolean>,
      default: false,
    },
    checkStrictly: {
      // 默认只有子节点可被选择
      type: Boolean as PropType<boolean>,
      default: false,
    },
    auth: {
      type: [String, Number] as PropType<string | string>,
      default: '',
    },
    // 是否显示每一行的输入图标
    showIcon: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  });
  const cloneDeep = (data) => {
    return props.isCloneDeep ? ldCloneDeep(data) : data;
  };
  let formValue = ref({});
  const treeSelectRef = ref();
  const emit = defineEmits(['success', 'fail', 'treeSelectChange']);
  formValue.value = props.isCreate ? {} : cloneDeep(props.formData);
  const formRules = {};
  const ruleFormRef = ref<FormInstance>();
  watch(
    () => props.formData,
    (val) => {
      formValue.value = cloneDeep(val) || {};
    },
    {
      immediate: true,
      deep: true,
    },
  );
  watch(
    () => props.formList,
    (value) => {
      value.map((item) => {
        if (item.ifShow !== false) {
          let addRules: any[] = [];
          if (item.required) {
            addRules.push({
              required: true,
              message: item.label + '不能为空',
              trigger: 'change',
            });
          }
          if (item.dynamicRules) {
            addRules = [...addRules, ...item.dynamicRules];
          }
          if (item.rules) {
            addRules = [...addRules, ...item.rules];
          }
          if (isDef(item.componentProps?.defaultValue)) {
            if (isNullOrUnDef(formValue.value[item.field])) {
              formValue.value[item.field] = item.componentProps?.defaultValue;
            }
          }
          formRules[item.field] = addRules;
          if (props.isCreate) {
            // item.component === 'CheckboxGroup'
            //   ? (formValue.value[item.field] = [])
            //   : (formValue.value[item.field] = '');
            switch (item.component) {
              case 'CheckboxGroup':
                formValue.value[item.field] = [];
                break;
              case 'Rate':
                formValue.value[item.field] = null;
                break;
              case 'Switch':
                formValue.value[item.field] = true;
                break;
              case 'TimePicker':
                formValue.value[item.field] = null;
                break;
              default:
                formValue.value[item.field] = '';
                break;
            }
          }
        }
      });
      // formRules = ref(...fValue);
    },
    { immediate: true, deep: true },
  );

  // 重置
  const resetForm = (formEl: FormInstance | undefined = ruleFormRef.value) => {
    if (!formEl) return;
    formEl.resetFields();
  };
  // 表单提交
  const submitForm = async (formEl: FormInstance | undefined, cb?: Fn | undefined) => {
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
      if (valid) {
        console.log('submit!', valid);
        emit('success', formValue.value);
        cb && cb('success', formValue.value);
      } else {
        console.log('error submit!', fields);
        cb && cb('fail', fields);
        emit('fail', fields);
      }
    });
  };
  const clearValidate = (key) => {
    ruleFormRef.value && ruleFormRef.value.clearValidate(key);
  };
  const validateField = (key) => {
    ruleFormRef.value && ruleFormRef.value.validateField(key);
  };
  const resetFields = () => {
    ruleFormRef.value && ruleFormRef.value.resetFields();
  };
  /**
   * 验证数据，触发success事件
   */
  function formValid(cb: Fn | undefined = undefined) {
    submitForm(ruleFormRef.value, cb);
  }

  // treeselect事件
  function treeSelChange() {
    emit('treeSelectChange', formValue.value);
  }

  defineExpose({
    formValue,
    submitForm,
    ruleFormRef,
    formValid,
    resetForm,
    clearValidate,
    validateField,
    resetFields,
  });
</script>
<style scoped lang="scss">
  .form-bottom {
    display: flex;
    justify-content: center;
  }

  .form-all {
    padding: 10px;
  }

  .el-select {
    width: 100%;
  }
  .el-form-item-area {
    :deep(.el-form-item__content) {
      display: flex;
      // width: calc(100% - 20px);
      > :first-child {
        width: calc(100% - 20px);
      }
    }
  }
  .el-form-item-icon {
    margin-left: 3px;
    font-size: 16px;
  }
</style>
