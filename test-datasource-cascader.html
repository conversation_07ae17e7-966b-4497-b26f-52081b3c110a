<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据源级联选择器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-case {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .mock-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .expected-result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>数据源级联选择器功能测试</h1>
        
        <div class="test-case">
            <div class="test-title">测试用例 1: POSTGRESQL 类型数据源</div>
            <div class="test-description">
                当数据源类型为 "POSTGRESQL" 时，应该可以选择到第三级模式
            </div>
            <div class="mock-data">
                模拟数据源: { id: 1, name: "PostgreSQL数据源", type: "POSTGRESQL" }
            </div>
            <div class="expected-result">
                预期结果: needsSchemaSelection = true，显示模式面板，数据库项显示右箭头
            </div>
            <div class="test-result" id="test1-result">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">测试用例 2: KINGBASE 类型数据源</div>
            <div class="test-description">
                当数据源类型为 "KINGBASE" 时，应该可以选择到第三级模式
            </div>
            <div class="mock-data">
                模拟数据源: { id: 2, name: "KingBase数据源", type: "KINGBASE" }
            </div>
            <div class="expected-result">
                预期结果: needsSchemaSelection = true，显示模式面板，数据库项显示右箭头
            </div>
            <div class="test-result" id="test2-result">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">测试用例 3: MYSQL 类型数据源</div>
            <div class="test-description">
                当数据源类型为 "MYSQL" 时，应该只能选择到第二级数据库
            </div>
            <div class="mock-data">
                模拟数据源: { id: 3, name: "MySQL数据源", type: "MYSQL" }
            </div>
            <div class="expected-result">
                预期结果: needsSchemaSelection = false，不显示模式面板，数据库项不显示右箭头，选择数据库后直接完成
            </div>
            <div class="test-result" id="test3-result">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">测试用例 4: 其他类型数据源</div>
            <div class="test-description">
                当数据源类型为其他类型（如 "ORACLE"）时，应该只能选择到第二级数据库
            </div>
            <div class="mock-data">
                模拟数据源: { id: 4, name: "Oracle数据源", type: "ORACLE" }
            </div>
            <div class="expected-result">
                预期结果: needsSchemaSelection = false，不显示模式面板，数据库项不显示右箭头，选择数据库后直接完成
            </div>
            <div class="test-result" id="test4-result">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 模拟 needsSchemaSelection 计算逻辑
        function needsSchemaSelection(dataSource) {
            if (!dataSource) return false;
            const type = dataSource.type?.toUpperCase();
            return type === 'POSTGRESQL' || type === 'KINGBASE';
        }

        // 测试用例数据
        const testCases = [
            { id: 1, name: "PostgreSQL数据源", type: "POSTGRESQL" },
            { id: 2, name: "KingBase数据源", type: "KINGBASE" },
            { id: 3, name: "MySQL数据源", type: "MYSQL" },
            { id: 4, name: "Oracle数据源", type: "ORACLE" }
        ];

        // 运行测试
        testCases.forEach((testCase, index) => {
            const result = needsSchemaSelection(testCase);
            const resultElement = document.getElementById(`test${index + 1}-result`);
            
            let expectedResult;
            if (testCase.type === 'POSTGRESQL' || testCase.type === 'KINGBASE') {
                expectedResult = true;
            } else {
                expectedResult = false;
            }
            
            const isSuccess = result === expectedResult;
            
            resultElement.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultElement.innerHTML = `
                <strong>测试结果:</strong> needsSchemaSelection = ${result}<br>
                <strong>预期结果:</strong> needsSchemaSelection = ${expectedResult}<br>
                <strong>状态:</strong> ${isSuccess ? '✅ 通过' : '❌ 失败'}
            `;
        });

        // 显示总结
        const allPassed = testCases.every((testCase, index) => {
            const result = needsSchemaSelection(testCase);
            const expectedResult = testCase.type === 'POSTGRESQL' || testCase.type === 'KINGBASE';
            return result === expectedResult;
        });

        console.log(`所有测试用例 ${allPassed ? '通过' : '失败'}`);
    </script>
</body>
</html>
