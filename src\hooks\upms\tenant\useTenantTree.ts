import { ref } from 'vue';
// import device from 'current-device';
export default function useZtree() {
  const ztree = ref<any>();
  const value = ref('');
  const nodeId = ref('');
  const dragId = ref('');
  const nodeStatus = ref(false);
  const sortParams = ref({});
  const handleClick = (evt, id, node) => {
    if (!!node.id) {
      const emNode = ztree.value.getNodesByParam('id', '', null);
      console.log(emNode);
      ztree.value.removeNode(emNode[0]);
      // addDisabled.value = false;
    }
    if (nodeId.value === node.id && !!nodeId.value) {
      if (!nodeStatus.value) {
        ztree.value.cancelSelectedNode(node);
        nodeId.value = '';
      } else {
        nodeStatus.value = !nodeStatus.value;
      }
    } else {
      nodeId.value = node.id;
    }
  };
  const handleBeforeDrop = (treeId, treeNodes, targetNode) => {
    let type = '';
    if (treeNodes[0].sort > targetNode.sort) {
      type = 'prev';
    }

    if (treeNodes[0].sort < targetNode.sort) {
      type = 'next';
    }

    const obj = {
      id: treeNodes[0].id,
      targetId: targetNode.id,
      type,
    };
    sortParams.value = obj;
    return targetNode.parentId == dragId.value ? true : false;
  };
  const handleBeforeDrag = (id, treeNodes) => {
    dragId.value = treeNodes.length > 0 ? treeNodes[0].parentId : '';
    return true;
  };
  const setting = ref<any>({
    data: {
      simpleData: {
        enable: true,
        pIdKey: 'parentId',
      },
      key: {
        name: 'tenantName',
      },
    },
    view: {
      showIcon: false,
      dblClickExpand: false, // 双击不允许展开节点
      fontCss: function (treeId, treeNode) {
        // console.log(treeNode);
        return !!treeNode.highlight
          ? { color: '#A60000', 'font-weight': 'bold' }
          : { color: '#000000', 'font-weight': 'normal' };
      },
    },
    edit: {
      drag: {
        isMove: true,
        prev: true,
        next: true,
        inner: false,
      },
      enable: true,
      showRenameBtn: false, //隐藏自带的修改按钮
      showRemoveBtn: false, //隐藏自带的修改按钮
    },
    callback: {
      onClick: handleClick,
      beforeDrop: handleBeforeDrop,
      beforeDrag: handleBeforeDrag,
    },
  });

  const handleCreated = (ztreeObj) => {
    ztree.value = ztreeObj;
    let node: any = {};
    if (!!nodeId.value) {
      node = ztreeObj.getNodesByParam('id', nodeId.value, null)[0];
      console.log('node111');
      console.log(node);
      nodeStatus.value = true;
      ztreeObj.selectNode(node);
      ztreeObj.setting.callback.onClick('', ztreeObj.setting.treeId, node);
    } else {
      if (ztreeObj.getNodes()[0]) {
        ztreeObj.expandNode(ztreeObj.getNodes()[0], true);
        node = ztreeObj.getNodes()[0];
        ztreeObj.selectNode(node);
        ztreeObj.setting.callback.onClick('', ztreeObj.setting.treeId, node);
      }
    }
  };

  return {
    setting,
    handleCreated,
    ztree,
    value,
    nodeId,
    nodeStatus,
    sortParams,
  };
}
