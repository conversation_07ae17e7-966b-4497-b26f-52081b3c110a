import { transformI18n } from '/@/utils/i18n';
import { projectSettings } from '/@/settings/config/projectConfig';
import { describe, expect, test } from 'vitest';

describe('多语言配置测试', () => {
  test('得到的翻译结果', () => {
    const projectLocale = projectSettings.locale;
    const result = transformI18n('buttons.updatePassword', true);
    if (['zh', 'none'].includes(projectLocale)) {
      expect(result).toEqual('修改密码');
    } else {
      expect(result).toEqual('Update Password');
    }
  });
});
