import { formatToDateTime } from '../../src/utils/dateUtil';
import { describe, expect, test } from 'vitest';
import dayjs from 'dayjs';

describe('时间转换工具类', () => {
  test('测试时分秒', () => {
    const str = '2023-03-17 16:59:24';
    const timeDefault = formatToDateTime(dayjs(new Date(str)));
    expect(timeDefault).toEqual(str);
    const timeHM = formatToDateTime(dayjs(new Date(str)), 'YYYY-MM-DD HH:mm');
    expect(timeHM).toEqual('2023-03-17 16:59');
  });
});
