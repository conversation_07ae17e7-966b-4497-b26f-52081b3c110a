import { sysHttp } from '../common/http';
import { ProjectListGetResultModel, getProjectPramas } from './model/projectModel';
enum Api {
  qryProject = '/project/', //项目列表
  workbenches = '/workbenches/', // 查询首页工作台信息
  layoutQuery = '/workbenches/layoutQuery', // 工作台布局存储查询
  layoutStorage = '/workbenches/layoutStorage', // 个人工作台布局存储
  quicklyStartSearch = '/workbenches/quicklyStartSearch', // 个人工作台快速开始查询
  quicklyStartStorage = '/workbenches/quicklyStartStorage', // 个人工作台快速开始存储
  myNotices = '/notice/myNotices',
}

// 项目列表
export const GetProjectGroup = (params?: getProjectPramas) =>
  sysHttp.get<ProjectListGetResultModel>({ url: Api.qryProject, params: params });
// 查询首页工作台信息
export const GetWorkbenches = (params) =>
  sysHttp.get({ url: Api.workbenches, params: params });
// 工作台布局存储查询
export const layoutQuery = () => sysHttp.get({ url: Api.layoutQuery });
// 个人工作台布局存储
export const layoutStorage = (params) =>
  sysHttp.post({ url: Api.layoutStorage, params: params });
// 个人工作台快速开始查询
export const quicklyStartSearch = () => sysHttp.get({ url: Api.quicklyStartSearch });
// 个人工作台快速开始存储
export const quicklyStartStorage = (params) =>
  sysHttp.post({ url: Api.quicklyStartStorage, params: params });
// 分页查询【我的公告】列表
export const myNotices = (params) => sysHttp.get({ url: Api.myNotices, params });
