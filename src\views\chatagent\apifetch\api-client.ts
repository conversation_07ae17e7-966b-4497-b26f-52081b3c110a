/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * API客户端基类
 * 提供统一的API调用接口和错误处理
 */

import type { 
  RequestConfig, 
  ApiResponse, 
  RequestOptions, 
  FetchWrapperConfig,
  StandardApiResponse,
  PaginatedResponse
} from './types'
import { FetchWrapper } from './fetch-wrapper'
import { StreamResponseHandler } from './stream-handler'

export abstract class ApiClient {
  protected fetchWrapper: FetchWrapper
  protected streamHandler: StreamResponseHandler
  protected baseURL: string

  constructor(baseURL: string, config?: Partial<FetchWrapperConfig>) {
    this.baseURL = baseURL
    this.fetchWrapper = new FetchWrapper({
      baseURL,
      ...config
    })
    this.streamHandler = new StreamResponseHandler(this.fetchWrapper)
    
    // 添加默认的请求拦截器
    this.setupDefaultInterceptors()
  }

  /**
   * 设置默认拦截器
   */
  private setupDefaultInterceptors(): void {
    // 请求拦截器：添加认证信息等
    this.fetchWrapper.addRequestInterceptor({
      onFulfilled: async (config) => {
        // 可以在这里添加认证token、租户信息等
        const token = this.getAuthToken()
        if (token) {
          config.headers = {
            ...config.headers,
            'Authorization': `Bearer ${token}`
          }
        }

        // 添加其他通用头信息
        const commonHeaders = this.getCommonHeaders()
        config.headers = {
          ...config.headers,
          ...commonHeaders
        }

        return config
      },
      onRejected: (error) => {
        console.error('请求拦截器错误:', error)
        return Promise.reject(error)
      }
    })

    // 响应拦截器：统一处理响应格式
    this.fetchWrapper.addResponseInterceptor({
      onFulfilled: async (response) => {
        // 处理标准API响应格式
        if (this.isStandardApiResponse(response.data)) {
          const standardResponse = response.data as StandardApiResponse
          if (!standardResponse.success) {
            throw new Error(standardResponse.message || 'API请求失败')
          }
          // 返回实际数据
          response.data = standardResponse.data
        }
        return response
      },
      onRejected: (error) => {
        // 统一错误处理
        this.handleApiError(error)
        return Promise.reject(error)
      }
    })
  }

  /**
   * 获取认证token
   */
  protected getAuthToken(): string | null {
    // 子类可以重写此方法来获取token
    // 这里可以从localStorage、sessionStorage或其他地方获取
    try {
      return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
    } catch (e) {
      return null
    }
  }

  /**
   * 获取通用请求头
   */
  protected getCommonHeaders(): Record<string, string> {
    // 子类可以重写此方法来添加通用头信息
    return {
      'X-Requested-With': 'XMLHttpRequest',
      'X-Client-Version': '1.0.0'
    }
  }

  /**
   * 判断是否为标准API响应格式
   */
  private isStandardApiResponse(data: any): boolean {
    return data && typeof data === 'object' && 'success' in data
  }

  /**
   * 处理API错误
   */
  protected handleApiError(error: any): void {
    console.error('API请求错误:', error)
    
    // 可以在这里添加全局错误处理逻辑
    // 比如显示错误提示、重定向到登录页等
    if (error.status === 401) {
      console.warn('认证失败，可能需要重新登录')
      // this.redirectToLogin()
    } else if (error.status === 403) {
      console.warn('权限不足')
    } else if (error.status >= 500) {
      console.error('服务器错误')
    }
  }

  /**
   * 通用GET请求
   */
  protected async get<T = any>(
    url: string, 
    params?: Record<string, any>, 
    options?: RequestOptions
  ): Promise<T> {
    const response = await this.fetchWrapper.get<T>(url, params, options)
    return response.data
  }

  /**
   * 通用POST请求
   */
  protected async post<T = any>(
    url: string, 
    data?: any, 
    options?: RequestOptions
  ): Promise<T> {
    const response = await this.fetchWrapper.post<T>(url, data, options)
    return response.data
  }

  /**
   * 通用PUT请求
   */
  protected async put<T = any>(
    url: string, 
    data?: any, 
    options?: RequestOptions
  ): Promise<T> {
    const response = await this.fetchWrapper.put<T>(url, data, options)
    return response.data
  }

  /**
   * 通用DELETE请求
   */
  protected async delete<T = any>(
    url: string, 
    options?: RequestOptions
  ): Promise<T> {
    const response = await this.fetchWrapper.delete<T>(url, options)
    return response.data
  }

  /**
   * 通用PATCH请求
   */
  protected async patch<T = any>(
    url: string, 
    data?: any, 
    options?: RequestOptions
  ): Promise<T> {
    const response = await this.fetchWrapper.patch<T>(url, data, options)
    return response.data
  }

  /**
   * 处理分页响应
   */
  protected extractPaginatedData<T>(response: PaginatedResponse<T>): T[] {
    return response.content || []
  }

  /**
   * 构建查询参数
   */
  protected buildQueryParams(params: Record<string, any>): Record<string, any> {
    const queryParams: Record<string, any> = {}
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams[key] = value
      }
    })
    
    return queryParams
  }

  /**
   * 处理文件上传
   */
  protected async uploadFile<T = any>(
    url: string,
    file: File,
    additionalData?: Record<string, any>,
    options?: RequestOptions
  ): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }
    
    return this.post<T>(url, formData, options)
  }

  /**
   * 批量请求
   */
  protected async batchRequest<T = any>(
    requests: RequestConfig[],
    options?: RequestOptions
  ): Promise<T[]> {
    const promises = requests.map(config => this.fetchWrapper.request<T>(config, options))
    const responses = await Promise.allSettled(promises)
    
    return responses.map(result => {
      if (result.status === 'fulfilled') {
        return result.value.data
      } else {
        console.error('批量请求中的某个请求失败:', result.reason)
        return null
      }
    }).filter(data => data !== null)
  }

  /**
   * 重试请求
   */
  protected async retryRequest<T = any>(
    requestFn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: any
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error
        
        if (i < maxRetries) {
          console.warn(`请求失败，${delay}ms后进行第${i + 1}次重试:`, error)
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
        }
      }
    }
    
    throw lastError
  }

  /**
   * 获取客户端统计信息
   */
  getStats() {
    return this.fetchWrapper.getStats()
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.fetchWrapper.clearCache()
  }

  /**
   * 清除统计信息
   */
  clearStats(): void {
    this.fetchWrapper.clearStats()
  }

  /**
   * 获取流式处理器
   */
  getStreamHandler(): StreamResponseHandler {
    return this.streamHandler
  }
}
