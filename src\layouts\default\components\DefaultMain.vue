<template>
  <el-main>
    <el-scrollbar>
      <div>
        <router-view v-slot="{ Component, route }">
          <keep-alive>
            <component
              :is="Component"
              v-if="route.meta.keepAlive"
              :key="route.fullPath"
            />
          </keep-alive>
          <component
            :is="Component"
            v-if="!route.meta.keepAlive"
            :key="route.fullPath"
          />
        </router-view>
        <!-- <router-view v-slot="{ Component, route }">
          <keep-alive :include="keepAliveList.join(',')">
            <component :is="Component" :key="route.fullPath" />
          </keep-alive>
        </router-view> -->
      </div>
    </el-scrollbar>
  </el-main>
</template>

<script>
  // import { keepAliveList } from '/@/hooks/web/useKeepAlive';
  export default {
    name: 'DefaultMain',
    // data() {
    //   return {
    //     keepAliveList,
    //   };
    // },
  };
</script>

<style scoped></style>
