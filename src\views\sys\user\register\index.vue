<template>
  <div>
    <div class="login-container">
      <p class="sys-logo"><img :src="logo" alt="logo" /></p>
      <div class="page-center">
        <basic-form
          class="add-account"
          :formList="FormSchema"
          :isCreate="false"
          :formData="formData"
          :showSubmit="false"
          :check-strictly="true"
          ref="formAccount"
        >
          <template #userName>
            <el-input v-model="formData.userName" placeholder="请输入账号名" />
          </template>
          <!--<template #avatar>
          <el-upload
            class="avatar-uploader"
            action="/sys/oss/upload "
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="avatarPath" :src="avatarPath" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </template>-->
          <template #status>
            <el-select style="width: 100%" v-model="formData.status">
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            /></el-select>
          </template>
          <template #deptId style="width: 500px">
            <VTreeDrop
              ref="tree"
              v-model="formData.deptId"
              :load="getDeptData"
              selectable
              titleField="instName"
              @search="handleSearch"
              dropPlaceholder="请选择机构"
              @select="handleSelect"
            >
              <template #display="scope">
                <div
                  :style="{
                    width: '420px',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    overflow: 'hidden',
                  }"
                >
                  {{ scope.selectedNode?.instName }}
                </div>
              </template>
            </VTreeDrop>
          </template>
          <template #phone>
            <el-input
              style="width: 92%"
              v-model="formData.phone"
              placeholder="请输入手机号"
              :disabled="!checkFlag.phone"
            /><el-icon class="user-check-full" @click="getAccurate('phone')"
              ><View
            /></el-icon>
          </template>
          <template #autocomplete>
            <el-autocomplete
              style="width: 100%"
              v-model="formData.email"
              :fetch-suggestions="querySearchEmail"
              :trigger-on-focus="false"
              placeholder="请输入电子邮箱"
          /></template>
          <template #idCard>
            <el-input
              style="width: 92%"
              v-model="formData.idCard"
              placeholder="请输入身份证号"
              :disabled="!checkFlag.idCard" /><el-icon
              class="user-check-full"
              @click="getAccurate('idCard')"
              ><View /></el-icon
          ></template>
        </basic-form>
      </div>
      <div class="btn">
        <el-button type="primary" @click="handleSave"> 保存 </el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref } from 'vue';
  import { VTreeDrop } from '@wsfe/vue-tree';
  import router from '/@/router';
  import { encryptedSM4 } from '/@/utils/cipher';
  import logo from '/@/assets/images/login/logo-upms.svg';
  import BasicForm from '/@/components/sys/BasicForm';
  import {
    checkKeyboardContinuousChar,
    checkPasswordStrength,
  } from '/@/utils/tools/passwordTools';
  // import { FormSchema, formData, } from './register.data';
  import {
    asyncDeptAll,
    dictCode,
    postRegister,
    checkApi,
    checkUser,
  } from '/@/api/sys';
  // const formData = ref({
  //   id: '',
  //   gender: '',
  //   email: '',
  //   industryType: '',
  //   phone: '',
  //   empName: '',
  //   instCode: '',
  //   deptName: '',
  //   deptId: '',
  //   empCode: '',
  //   empPost: '',
  //   empType: '',
  //   nodeCode: '',
  //   serviceCode: '',
  //   sortOrder: '',
  //   status: '01',
  // });
  const phoneReg =
    /^((\+86|0086)?\s*)((134[0-8]\d{7})|(((13([0-3]|[5-9]))|(14[5-9])|15([0-3]|[5-9])|(16(2|[5-7]))|17([0-3]|[5-8])|18[0-9]|19(1|[8-9]))\d{8})|(14(0|1|4)0\d{7})|(1740([0-5]|[6-9]|[10-12])\d{7}))$/;
  const card =
    /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/;

  const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  const empReg = /^([\u4e00-\u9fa5·]|[A-Za-z]){2,15}$/;
  const formData = ref({
    deptId: '',
    email: '',
    empName: '',
    phone: '',
    status: '1',
    idCard: '',
    userName: '',
    userPasswd: '',
    avatar: '',
    available: '1',
  });
  const sensitiveWords = ref([]);
  const passwordRegArr = ref([]);
  const statusList = ref([]);
  const formAccount = ref();
  const FormSchema: FormOptions[] = [
    // Text
    {
      field: 'deptId',
      label: '机构',
      slot: 'deptId',
      required: true,
    },
    {
      field: 'userName',
      label: '账号名',
      component: 'Input',
      componentProps: {
        placeholder: '请输入账号名',
        disabled: false,
      },
      slot: 'userName',
      required: true,
      rules: [
        { max: 30, message: '超过长度限制，最多30字' },
        {
          validator: (_, value) => {
            if (sensitiveWords.value.length > 0) {
              const pattern = new RegExp(sensitiveWords.value.join('|'), 'i');
              console.log('pattern', pattern);
              if (pattern.test(value)) {
                return Promise.reject('账号名中不能包含敏感词汇！');
              }
            }
            if (value) {
              return new Promise<void>((resolve, reject) => {
                checkUser({
                  id: '',
                  userName: value,
                }).then((res) => {
                  res ? resolve() : reject(res.message || '账号名已存在');
                });
              });
            }
          },
        },
      ],
    },
    {
      field: 'userPasswd',
      label: '登录密码',
      component: 'InputPassword',
      componentProps: {
        placeholder: '请输入新密码',
      },
      dynamicRules: [
        {
          validator: (_, value) => {
            for (let i = 0; i < passwordRegArr.value.length; i++) {
              //  密码长度
              if (passwordRegArr.value[i].code === 'biz_password_reg_length') {
                let lengthArr: any = [];
                if (!!passwordRegArr.value[i].value) {
                  lengthArr = passwordRegArr.value[i].value.split(',');
                } else {
                  lengthArr = [12, 32];
                }
                const regex = new RegExp(
                  '^.{' + lengthArr[0] + ',' + lengthArr[1] + '}$',
                );
                if (!regex.test(value)) {
                  return Promise.reject(passwordRegArr.value[i].label);
                }
              }
              // 不能输入连续3位或3位以上相同字符
              if (passwordRegArr.value[i].code === 'biz_password_reg_same_characters') {
                const regex = /(\w)*(\w)\2{2}(\w)*/g;
                if (regex.test(value)) {
                  return Promise.reject(passwordRegArr.value[i].label);
                }
              }
              // 不能输入连续字符  如123，abc等
              if (
                passwordRegArr.value[i].code === 'biz_password_reg_adjacent_characters'
              ) {
                const arr = value.split('');
                let flag = true;
                for (let i = 1; i < arr.length - 1; i++) {
                  const fIndex = arr[i - 1].charCodeAt();
                  const sIndex = arr[i].charCodeAt();
                  const tIndex = arr[i + 1].charCodeAt();
                  if (tIndex - sIndex === 1 && sIndex - fIndex === 1) {
                    flag = false;
                  }
                }
                if (!flag) {
                  return Promise.reject(passwordRegArr.value[i].label);
                }
              }

              // 不能输入键盘连续3位或3位以上相同字符
              if (
                passwordRegArr.value[i].code === 'biz_password_reg_continuous_keyboard'
              ) {
                if (checkKeyboardContinuousChar(value)) {
                  return Promise.reject(passwordRegArr.value[i].label);
                }
              }
              // 不能包含用户名
              if (
                passwordRegArr.value[i].code === 'biz_password_reg_include_username'
              ) {
                if (formData.value.userName.length > 0) {
                  const userNameArr = [formData.value.userName];
                  const pattern = new RegExp(userNameArr.join('|'), 'i');
                  if (pattern.test(value)) {
                    return Promise.reject(passwordRegArr.value[i].label);
                  }
                }

                //
              }

              // 请输入大小写、数字、特殊字符俩种及以上组合
              if (passwordRegArr.value[i].code === 'biz_password_reg_combination') {
                const num = passwordRegArr.value[i].value
                  ? passwordRegArr.value[i].value
                  : '2';
                if (!checkPasswordStrength(value, num)) {
                  return Promise.reject(passwordRegArr.value[i].label);
                }
              }
            }
            return Promise.resolve();
          },
        },
      ],
      ifShow: true,
      required: true,
    },
    // {
    //   field: 'avatar',
    //   label: '头像',
    //   component: 'Input',
    //   slot: 'avatar',
    // },
    // {
    //   label: '账号状态',
    //   field: 'available',
    //   component: 'RadioGroup',
    //   componentProps: {
    //     options: [
    //       {
    //         label: '已激活',
    //         value: '1',
    //       },
    //       {
    //         label: '已禁用',
    //         value: '0',
    //       },
    //       {
    //         label: '已锁定',
    //         value: '2',
    //       },
    //     ],
    //   },
    //   required: true,
    // },
    {
      field: 'empName',
      label: '员工姓名',
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名',
        disabled: false,
      },
      rules: [
        { max: 90, message: '超过长度限制，最多30字' },
        { min: 2, message: '少于长度限制，最少2字' },
        {
          trigger: 'blur',
          pattern: empReg,
          message: '请输入正确的员工姓名',
        },
      ],
      required: true,
    },

    {
      label: '手机号',
      field: 'phone',
      component: 'Input',
      componentProps: {
        placeholder: '请输入手机号',
      },
      rules: [
        {
          pattern: phoneReg,
          message: '请输入正确的手机号',
        },
        {
          validator: (rule, value) => {
            if (!value) {
              /* eslint-disable-next-line */
              return true;
            }
            return new Promise<void>((resolve, reject) => {
              const params = {
                type: 2,
                value: value,
              };
              checkApi(params).then((res) => {
                res ? resolve() : reject(res.message || '手机号码已存在');
              });
            });
          },
        },
      ],
      required: true,
    },
    {
      label: '电子邮箱',
      field: 'email',
      component: 'Slot',
      slot: 'autocomplete',
      rules: [
        {
          trigger: 'blur',
          pattern: emailReg,
          message: '请输入正确的电子邮箱',
        },
        { max: 100, message: '超过长度限制，最多100字' },
        {
          validator: (rule, value) => {
            if (!value) {
              /* eslint-disable-next-line */
              return true;
            }
            return new Promise<void>((resolve, reject) => {
              const params = {
                type: 0,
                value: value,
              };
              checkApi(params).then((res) => {
                res ? resolve() : reject(res.message || '电子邮箱已存在');
              });
            });
          },
        },
      ],
      required: true,
    },
    // {
    //   label: '员工状态',
    //   field: 'status',
    //   component: 'RadioGroup',
    //   slot: 'status',
    //   componentProps: {
    //     options: statusList.value,
    //   },
    //   required: true,
    // },
    {
      label: '身份证号',
      field: 'idCard',
      component: 'Input',
      required: true,
      rules: [
        { pattern: card, message: '请输入正确的身份证号' },
        {
          validator: (rule, value) => {
            if (!value) {
              /* eslint-disable-next-line */
              return true;
            }
            return new Promise<void>((resolve, reject) => {
              const params = {
                type: 1,
                value: value,
              };
              checkApi(params).then((res) => {
                res ? resolve() : reject(res.message || '身份证号码已存在');
              });
            });
          },
        },
      ],
    },
  ];
  const tree = ref();
  const name = ref('');
  const deptId = ref('');
  function querySearchEmail(queryString, callback) {
    const emailList = [
      { value: '@chinapost.com.cn' },
      { value: '@qq.com' },
      { value: '@163.com' },
      { value: '@sina.com' },
      { value: '@sohu.com' },
      { value: '@yahoo.com.cn' },
    ];
    let results: string[] = [];
    let queryList: any[] = [];
    emailList.map((item) =>
      queryList.push({ value: queryString.split('@')[0] + item.value }),
    );
    results = queryString ? queryList.filter(createFilter(queryString)) : queryList;
    callback(results);
  }
  // 邮箱填写过滤
  function createFilter(queryString) {
    return (item) => {
      return item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
    };
  }
  const handleSearch = (e) => {
    console.log('ssssss', e);
    name.value = e;
    getDeptData();
  };
  const handleSelect = (e) => {
    console.log('eeeeeeeee', e);
    deptId.value = e.id;
  };
  const handleSave = () => {
    // console.log('formData', formData.value);
    // const { email, empName, phone, idCard, userName, userPasswd } = formData.value;
    const getData = formAccount.value.submitForm;
    const ruleFormRef = formAccount.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        const { email, empName, phone, idCard, userName, userPasswd } = data;
        // saveUser({
        //   ...data,
        //   deptId: detaileDeptId.value,
        //   phone: data.phone ? encryptedSM4(data.phone) : undefined,
        //   idCard: data.idCard ? encryptedSM4(data.idCard) : undefined,
        //   email: data.email ? encryptedSM4(data.email) : undefined,
        // })
        //   .then(() => {
        //     drawer.value = false;
        //     ElMessage({
        //       type: 'success',
        //       showClose: true,
        //       message: data.id ? '修改员工成功' : '新增员工成功',
        //     });
        //     accountList();
        //   })
        //   .catch(() => {
        //   });
        postRegister({
          sysEmployee: {
            email: email ? encryptedSM4(email) : undefined,
            empName,
            phone: phone ? encryptedSM4(phone) : undefined,
            idCard: idCard ? encryptedSM4(idCard) : undefined,
            deptId: deptId.value,
          },
          userName: userName,
          userPasswd: userPasswd ? encryptedSM4(userPasswd) : undefined,
          // avatarPath: avatarPath.value,
        }).then((res) => {
          console.log('res', res);
          ElMessage({
            message: '注册账号成功！',
            grouping: true,
            type: 'success',
          });
          router.push({ path: '/login' });
        });
      } else {
      }
    });
  };
  const getDeptData = (node, resolve) => {
    asyncDeptAll({
      id: node?.id ? encodeURIComponent(encryptedSM4(node.id)) : '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  // const handleAvatarSuccess: UploadProps['onSuccess'] = (
  //   response,
  //   uploadFile
  // ) => {
  //   avatarPath.value =uploadFile.response.result;
  // }

  // const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  //   if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png' && rawFile.type !== 'image/jpg') {
  //     ElMessage.error('请上传"jpeg、png、jpg"格式图片!')
  //     return false
  //   } else if (rawFile.size / 1024 / 1024 > 2) {
  //     ElMessage.error('图片大小不能超过2MB!')
  //     return false
  //   }
  //   return true
  // }
  const getDict = () => {
    const code = 'biz_sensitive_words,biz_password_reg,biz_status';
    dictCode(encryptedSM4(code)).then((res) => {
      console.log('res', res);
      sensitiveWords.value = res.biz_sensitive_words.map((obj) => obj.value);
      passwordRegArr.value = res.biz_password_reg;
      statusList.value = res.biz_status;
    });
  };
  getDict();
</script>
<style lang="scss" scoped>
  // @import url('/src/styles/login/login.css');
  .sys-logo {
    padding: 15px 0 0 30px;
  }
  .page-center {
    // width: 100%;
    display: flex;
    justify-content: center;
    .add-account {
      width: 600px;
    }
  }
  .btn {
    width: 100%;
    display: flex;
    justify-content: center;
  }
</style>
<route lang="yaml">
name: Register
meta:
  layout: BlankLayout
</route>
