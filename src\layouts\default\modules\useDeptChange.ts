import { computed, nextTick } from 'vue';
import { useTenantStore } from '/@/stores/modules/tenant';
import { DeptInfo } from '/@/api/model/Tenant';
import { getHeaderInfo, setHeaderInfo } from '/@/utils/storage/tenant';
import { useUserStore } from '/@/stores/modules/user';
import { cloneDeep } from 'lodash-es';

export function useDeptChange() {
  const useTenant = useTenantStore();
  const userStore = useUserStore();
  const depts = computed(() => useTenant.getDeptInfos);
  const curDept = computed(() => useTenant.getCurDept);
  async function setCurDept(dept: DeptInfo) {
    // 修改本地缓存
    const headerInfo = getHeaderInfo();
    headerInfo.deptId = dept.id;
    setHeaderInfo(headerInfo);
    const userInfo = cloneDeep(userStore.getUserInfo);
    // 数据请求前，要先刷新当前租户信息
    const currentDept: any = {};
    currentDept.id = dept.id;
    currentDept.deptName = dept.deptName;
    userInfo['currentDept'] = currentDept;
    userStore.setUserInfo(userInfo);
    // 刷新用户的权限数据
    await userStore.reFreshUser();
    useTenant.setCurDept(dept);
    // 刷新租户数据
    nextTick(async () => {
      await userStore.refreshUserData();
    });
  }
  return { depts, curDept, setCurDept };
}
