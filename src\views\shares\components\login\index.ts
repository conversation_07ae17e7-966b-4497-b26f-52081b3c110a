/**
 * 登录页统一 的动画数据 initial enter
 * @constructor
 */
function useAnimateData() {
  function initial(y = 100) {
    return {
      opacity: 0,
      y: y,
    };
  }
  function enter(delay = 300) {
    return {
      opacity: 1,
      y: 0,
      transition: {
        delay: delay,
      },
    };
  }
  return { initial, enter };
}

import QrCode from '/@/views/shares/components/login/QrCode.vue';
import MSG from '/@/views/shares/components/login/MSG.vue';
import USER from '/@/views/shares/components/login/USER.vue';

export { useAnimateData, QrCode, MSG, USER };
