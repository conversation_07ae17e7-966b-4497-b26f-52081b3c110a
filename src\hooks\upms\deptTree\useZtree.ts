import { ref } from 'vue';
// import device from 'current-device';
export default function useZtree(formRef, deleteNode) {
  const clickNode = ref<any>({});
  const value = ref('');
  const dragId = ref('');
  const nodeId = ref('');
  const ztree = ref<any>();
  const callbackType = ref('1');
  const addDisabled = ref(false);
  const regionArr = ref<string[]>([]);
  const sortParams = ref({});
  const type = ref('edit');
  const handleClick = (evt, id, node) => {
    console.log(evt);
    console.log(id);
    console.log(node);
    type.value = 'edit';
    let activeId: any = '';
    if (!!document && !!document.getElementById(id)) {
      activeId = document
        .getElementById(id)
        ?.getElementsByTagName('li')[0]
        ?.getElementsByTagName('a')[0]
        ?.getElementsByTagName('span')[1]
        ?.getAttribute('id');
    }
    if (!!evt.target) {
      if (nodeId.value === evt.target.getAttribute('id')) {
        evt.target.parentNode.classList.remove('curSelectedNode');
        nodeId.value = '';
        clickNode.value = {};
      } else {
        evt.target.parentNode.classList.add('curSelectedNode');
        nodeId.value = evt.target.getAttribute('id');
        clickNode.value = node;
      }
    } else {
      // 第一次进来走这里
      console.log(node);
      nodeId.value = activeId;
      if (!!node && !!node.id) {
        clickNode.value = node;
      }
    }
    // clickNode.value = node;
  };
  const handleBeforeDrop = (treeId, treeNodes, targetNode) => {
    console.log('handleBeforeDrop');
    console.log(treeNodes);
    console.log(targetNode);
    let type = '';
    if (treeNodes[0].sort > targetNode.sort) {
      type = 'prev';
    }

    if (treeNodes[0].sort < targetNode.sort) {
      type = 'next';
    }

    const obj = {
      id: treeNodes[0].id,
      targetId: targetNode.id,
      type,
    };
    sortParams.value = obj;
    return targetNode.parentId == dragId.value ? true : false;
  };
  const handleBeforeDrag = (id, treeNodes) => {
    dragId.value = treeNodes.length > 0 ? treeNodes[0].parentId : '';
    return true;
  };
  const setting = ref<any>({
    data: {
      simpleData: {
        enable: true,
        // pIdKey: "parentId",
        pIdKey: 'parentId',
      },
      key: {
        // children: 'children',
        name: 'instName',
      },
    },
    view: {
      showIcon: false,
      dblClickExpand: false, // 双击不允许展开节点
      fontCss: function (treeId, treeNode) {
        // console.log(treeNode);
        return !!treeNode.highlight
          ? { color: '#A60000', 'font-weight': 'bold' }
          : { color: '#000000', 'font-weight': 'normal' };
      },
    },
    edit: {
      drag: {
        isMove: true,
        prev: true,
        next: true,
        inner: false,
      },
      enable: true,
      showRenameBtn: false, //隐藏自带的修改按钮
      showRemoveBtn: false, //隐藏自带的修改按钮
    },
    callback: {
      onClick: handleClick,
      beforeDrop: handleBeforeDrop,
      beforeDrag: handleBeforeDrag,
      // beforeClick: handleBeforeClick,
    },
  });

  const handleCreated = (ztreeObj) => {
    console.log(ztreeObj);
    console.log(clickNode.value);
    ztree.value = ztreeObj;
    if (!clickNode.value.id) {
      ztreeObj.expandNode(ztreeObj.getNodes()[0], true);
    }
    console.log(clickNode.value);
    if (!!clickNode.value && !!clickNode.value.id) {
      // alert(11);
      ztreeObj.selectNode(clickNode.value);

      ztreeObj.setting.callback.onClick('', ztreeObj.setting.treeId, clickNode.value);
    } else {
      const node = ztreeObj.getNodes()[0];
      ztreeObj.selectNode(node);
      console.log(node);
      console.log(ztreeObj.selectNode(node));
      ztreeObj.setting.callback.onClick('', ztreeObj.setting.treeId, node);
    }

    // let node = ztreeObj.getNodes()[0];
    // ztreeObj.selectNode(node);
    // ztreeObj.setting.callback.onClick('', ztreeObj.setting.treeId, node);
  };

  const addNode = () => {
    callbackType.value = '1';
    regionArr.value = [];
    console.log(ztree.value);
    // console.log(ztree.value.getSelectedNodes());
    const nodes =
      ztree.value && ztree.value.getSelectedNodes()
        ? ztree.value.getSelectedNodes()
        : [];
    console.log(nodes);
    console.log(clickNode.value);
    const newNode: any = {
      parentId: clickNode.value.id,
      parentInstName: clickNode.value.instName,
      parentInstCode: clickNode.value.instCode,
    };
    newNode[setting.value.data.key.name] = '未命名';

    // newNode.id = -1;
    let newTreeNode;
    if (!!clickNode.value.id) {
      newTreeNode = ztree.value.addNodes(nodes[0], [-1], newNode);
    } else {
      newTreeNode = ztree.value.addNodes(null, [-1], newNode);
    }
    ztree.value.selectNode(newTreeNode[0]);
    ztree.value.setting.callback.onClick(
      '',
      ztree.value.setting.treeId,
      newTreeNode[0],
    );
    clickNode.value = newTreeNode[0];

    console.log(clickNode.value);
    addDisabled.value = true;

    const ruleFormRef = formRef.value && formRef.value.ruleFormRef;
    if (ruleFormRef) {
      formRef.value?.resetForm(ruleFormRef);
    }
  };
  const removeNode = () => {
    callbackType.value = '0';
    if (!!clickNode.value.id) {
      if (callbackType.value === '0') {
        deleteNode('tree');
      }
    } else {
      vnodeRemove();
    }
  };

  const vnodeRemove = () => {
    const node = ztree.value.getSelectedNodes();
    ztree.value.removeNode(node[0]);
    addDisabled.value = false;
    nodeId.value = '';
  };

  return {
    setting,
    handleCreated,
    handleBeforeDrop,
    handleBeforeDrag,
    value,
    clickNode,
    handleClick,
    addNode,
    removeNode,
    addDisabled,
    regionArr,
    ztree,
    sortParams,
    type,
  };
}
