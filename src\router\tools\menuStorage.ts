import { getStorage, setStorage } from '/@/utils/storage';
import { MENU_KEY, MICRO_DATA, ALL_MENU_KEY } from '/@/enums/cacheEnum';
import { Menu } from '/@/stores/types';

export function getMenuStorage(): Menu[] {
  return getStorage(MENU_KEY);
}

export function setMenuStorage(value: Menu[]) {
  setStorage(MENU_KEY, value);
}
// 包含隐藏菜单
export function getAllMenuStorage(): Menu[] {
  return getStorage(ALL_MENU_KEY);
}
// 包含隐藏菜单
export function setAllMenuStorage(value: Menu[]) {
  setStorage(ALL_MENU_KEY, value);
}
export function setMicroData(value: Menu[]) {
  setStorage(MICRO_DATA, value);
}
export function GetMicroData(): Menu[] {
  return getStorage(MICRO_DATA);
}
