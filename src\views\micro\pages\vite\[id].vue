<template>
  <div v-if="microUrl">
    <!-- <span @click="setMessage()">改变子应用路由{{ appInfo.url }}</span> -->
    <WujieVue
      width="100%"
      height="calc(100vh - 70px)"
      :name="appInfo.name"
      :url="microUrl"
      :sync="false"
      :alive="true"
      :activated="activatedMount"
    />
  </div>
</template>

<script setup lang="ts" name="microapp">
  import { ref, watch, computed, onMounted } from 'vue';
  import { usemicroState } from '../../libs/microStore';
  import { useRouter } from 'vue-router';
  import { getAllMenuStorage } from '/@/router/tools/menuStorage';
  import { usePermissionStoreWithOut } from '/@/stores/modules/permission';
  import { Meta, Menu } from '/@/stores/types';
  // 如果使用wujie-vue
  import WujieVue from 'wujie-vue3';
  import { getAuthToken } from '/@/utils/storage/auth';
  import { useUserStoreWithOut } from '/@/stores/modules/user';
  // import { cloneDeep } from 'lodash-es';
  interface appInfoType {
    name: string;
    url: string;
  }
  const allMenuList = getAllMenuStorage();
  // 获取微前端的路由
  let microRouter: any = [];
  const findMicroRouter = (list: Menu[] = []) => {
    list.map((item: Menu) => {
      if (item.type === '2' && item?.meta?.microName == appInfo.value.name) {
        microRouter.push({ ...item, ...{ path: item.meta.microPath, type: '1' } });
      }
      if (item.children && item.children.length > 0) {
        findMicroRouter(item.children);
      }
    });
  };
  const userStore = useUserStoreWithOut();
  const { bus } = WujieVue;
  // 主应用监听事件
  const microOnMessage = () => {
    bus.$on('onMicroMessage-' + appInfo.value.name, function (data) {
      console.log('接收到子应用' + appInfo.value.name + '的数据:', data);
      microState.setmicroState(data);
      if (microState.getAppList.indexOf(appInfo.value.name) === -1) {
        microState.setAppList(appInfo.value.name);
      }
      if (data?.type == 'beforeLogin') {
        const token = getAuthToken();
        bus.$emit('sendMicroMessage-' + appInfo.value.name, {
          message: { username: '', password: '', token },
          type: 'login',
        });
      } else if (data?.type == 'needRouter') {
        microRouter = [];
        findMicroRouter(allMenuList);
        bus.$emit('sendMicroMessage-' + appInfo.value.name, {
          message: { routerList: microRouter },
          type: 'routerList',
        });
      } else if (data?.type == '401') {
        userStore.logout(401);
      } else if (data?.type == 'addTabs') {
        // 解决微应用中的隐藏菜单，在主应用的tab中不显示的问题
        let result: Menu = {
          path: '',
          id: undefined,
          name: '',
        }; // 运行结果
        function getTreeItem(data, id) {
          data.map((item) => {
            if (item.meta.microPath == id) {
              result = item; // 结果赋值
            } else {
              if (item.children) {
                getTreeItem(item.children, id);
              }
            }
          });
        }
        getTreeItem(allMenuList, data?.message?.path);
        route.push(result.path);
      } else {
        appObjectCreated.value[appInfo.value.name] = true;
      }
    });
  };
  // 微应用创建完成
  const activatedMount = () => {
    if (microState.getAppList.indexOf(appInfo.value.name) > -1) {
      appObjectCreated.value[appInfo.value.name] = true;
    }
  };
  const route = useRouter();
  const appInfo = ref<appInfoType>({ name: '', url: '' });
  const microAppData = ref({
    message: {
      path: '',
    },
    type: 'router',
  });
  // 主应用控制子应用路由跳转
  const microPath = ref('');
  // 保存子应用通信的数据
  const microState = usemicroState();
  const setRouter = (type, message) => {
    microState.setActiveApp(appInfo.value.name);
    microAppData.value = {
      message: { ...message, queryObject },
      type: type, //'router'
    };
    bus.$emit('sendMicroMessage-' + appInfo.value.name, microAppData.value);
  };
  const appObjectCreated = ref({});
  const needSend = ref({});
  const permissionStore = usePermissionStoreWithOut();
  let queryObject = {};
  // const appChange = ref(false);
  onMounted(() => {
    let meta: Meta = {} as Meta;
    if (route.currentRoute.value.path.indexOf('microOpenDetail') > -1) {
      meta.microName = route.currentRoute.value.query.microName;
      meta.url = route.currentRoute.value.query.url;
      meta.microPath = route.currentRoute.value.query.microPath;
    } else {
      console.log(
        '微前端展示页初始化',
        route.currentRoute.value.path,
        permissionStore.getCurrentMenu(route.currentRoute.value.path),
      );
      meta = permissionStore.getCurrentMenu(route.currentRoute.value.path)?.meta;
    }
    if (!meta.microName) return;
    appInfo.value.name = meta?.microName || '';
    appInfo.value.url = meta?.url as string;
    queryObject = {};
    meta.metaConfig.map((item) => {
      item.key && (queryObject[item.key] = item.value);
    });
    microOnMessage();
    if (microState.getActiveApp && microState.getActiveApp != appInfo.value.name) {
      // 解决保活模式，切换微应用后，会缓存当前微应用最后一个页面的问题。修改为，微应用切换前，跳转到微应用的空白页
      // appChange.value = true;
      bus.$emit('sendMicroMessage-' + microState.getActiveApp, {
        message: { path: '/blank/nopage' },
        type: 'router',
      });
    }
    const path = meta?.microPath;
    if (path) {
      microPath.value = path + '';
      if (appObjectCreated.value[appInfo.value.name]) {
        setRouter('router', { path });
      } else {
        needSend.value[appInfo.value.name] = true;
      }
    }
  });
  watch(
    () => appObjectCreated.value[appInfo.value.name],
    (val) => {
      if (val && needSend.value[appInfo.value.name] && microPath.value) {
        needSend.value[appInfo.value.name] = false;
        microPath.value && setRouter('router', { path: microPath.value });
      }
    },
  );
  const microUrl = computed(() => {
    return appInfo.value.url;
  });
</script>
