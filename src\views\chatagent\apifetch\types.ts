/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 统一的API类型定义
 */

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS'

// 请求配置接口
export interface RequestConfig {
  url: string
  method?: HttpMethod
  headers?: Record<string, string>
  params?: Record<string, any>
  data?: any
  timeout?: number
  signal?: AbortSignal
  mode?: RequestMode
  credentials?: RequestCredentials
  cache?: RequestCache
  redirect?: RequestRedirect
  referrer?: string
  referrerPolicy?: ReferrerPolicy
  integrity?: string
  keepalive?: boolean
}

// 响应接口
export interface ApiResponse<T = any> {
  data: T
  status: number
  statusText: string
  headers: Headers
  config: RequestConfig
}

// 错误响应接口
export interface ApiError {
  message: string
  status?: number
  statusText?: string
  code?: string
  data?: any
  config?: RequestConfig
}

// 拦截器接口
export interface RequestInterceptor {
  onFulfilled?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
  onRejected?: (error: any) => any
}

export interface ResponseInterceptor {
  onFulfilled?: (response: ApiResponse) => ApiResponse | Promise<ApiResponse>
  onRejected?: (error: ApiError) => any
}

// 流式响应处理器接口
export interface StreamHandler<T = any> {
  onEvent: (event: T) => void
  onError: (error: Error) => void
  onComplete: () => void
}

// 通用API响应格式
export interface StandardApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  code?: number
  timestamp?: string
}

// 分页响应格式
export interface PaginatedResponse<T = any> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  empty: boolean
}

// 请求选项
export interface RequestOptions {
  // 是否显示加载状态
  showLoading?: boolean
  // 是否显示错误消息
  showError?: boolean
  // 错误消息模式
  errorMessageMode?: 'message' | 'modal' | 'none'
  // 是否重试
  retry?: boolean
  // 重试次数
  retryCount?: number
  // 重试延迟
  retryDelay?: number
  // 是否缓存
  cache?: boolean
  // 缓存时间（毫秒）
  cacheTime?: number
  // 是否去重复请求
  dedupe?: boolean
  // 请求标识
  requestId?: string
}

// 缓存项接口
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expireTime: number
}

// 请求队列项接口
export interface RequestQueueItem {
  config: RequestConfig
  resolve: (value: any) => void
  reject: (reason: any) => void
  timestamp: number
}

// 日志级别
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'none'

// 配置接口
export interface FetchWrapperConfig {
  // 基础URL
  baseURL?: string
  // 默认超时时间
  timeout?: number
  // 默认请求头
  headers?: Record<string, string>
  // 是否启用日志
  enableLogging?: boolean
  // 日志级别
  logLevel?: LogLevel
  // 是否启用缓存
  enableCache?: boolean
  // 默认缓存时间
  defaultCacheTime?: number
  // 是否启用请求去重
  enableDedupe?: boolean
  // 去重时间窗口
  dedupeWindow?: number
  // 是否启用重试
  enableRetry?: boolean
  // 默认重试次数
  defaultRetryCount?: number
  // 默认重试延迟
  defaultRetryDelay?: number
  // 错误处理模式
  errorHandlingMode?: 'throw' | 'return' | 'silent'
}

// 数据源相关类型
export interface DataSource {
  id: number
  name: string
  type: string
  host?: string
  port?: number
  database?: string
  schema?: string
  description?: string
  status?: string
  supportDatabase?: boolean
  supportSchema?: boolean
}

export interface Database {
  name: string
  description?: string
  count?: number
  schemas?: Schema[]
}

export interface Schema {
  name: string
}

// 模型相关类型
export interface Model {
  id: string
  name: string
  model: string
  configName?: string
  provider?: string
  description?: string
  status?: string
  type?: string
  maxTokens?: number
  supportStreaming?: boolean
}

// 工具详情相关类型
export interface ToolDetailResponse {
  id: number
  parentExecutionId: number
  status: string
  toolName: string
  thinkStartTime: string
  thinkEndTime?: string
  actStartTime?: string
  actEndTime?: string
  thinkDetails: {
    thinkInput: string
    thinkOutput: string
  }
  actionDetails: {
    toolParameters: any
    actionResult: any
  }
  errorInfo?: string
}

export interface ToolParametersResponse {
  thinkActId: number
  toolName: string
  toolParameters: any
  actStartTime?: string
}

export interface ActionResultResponse {
  thinkActId: number
  toolName: string
  actionResult: any
  actEndTime?: string
  status: string
}

// 流式请求相关类型
export interface DirectStreamingRequest {
  chatId: string
  userId: string
  message: string
  toolContext?: Record<string, any>
  modelId?: string
}

export interface DirectStreamEvent {
  type: string
  planId?: string
  entityId?: string
  payload: string | any
  compressed?: boolean
}

// SSE事件类型
export interface SSEEvent {
  data: string
  event?: string
  id?: string
  retry?: number
}

// 请求统计信息
export interface RequestStats {
  total: number
  success: number
  error: number
  cached: number
  retried: number
  averageTime: number
  lastRequestTime: number
}
