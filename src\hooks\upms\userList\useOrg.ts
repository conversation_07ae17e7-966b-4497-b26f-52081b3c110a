import { ref } from 'vue';
// import device from 'current-device';
export default function useZtree() {
  const ztree = ref<any>();
  const value = ref('');
  const nodeId = ref('');
  const nodeCode = ref('');
  const nodeStatus = ref(false);
  const deptName = ref('');
  const instName = ref('');
  // const getLevel = (node) => {
  //   let name = node.instName;
  //   if (node.getParentNode()) {
  //     console.log(node.getParentNode().instName)
  //     name += '/' + node.getParentNode().instName
  //     getLevel(node.getParentNode())
  //   }
  //   return name

  // }

  const getParentNodeNames = (treeObj, node) => {
    const parentNames: any = [];
    const parentNode = treeObj.getNodeByParam('id', node.parentId); // 获取父节点
    if (parentNode != null) {
      parentNames.push(parentNode.instName); // 将父节点名称添加到数组中
      // 递归获取更高层次的父节点名称
      const parentParentNames = getParentNodeNames(treeObj, parentNode);
      return parentNames.concat(parentParentNames); // 返回所有父节点名称
    }
    return parentNames; // 如果没有父节点了，返回空数组
  };
  const handleClick = (evt, id, node) => {
    const parentNames = getParentNodeNames(
      ztree.value,
      ztree.value.getSelectedNodes()[0],
    );
    parentNames.reverse();
    const parentName = parentNames.join('/');
    if (nodeId.value === node.id && !!nodeId.value) {
      if (!nodeStatus.value) {
        ztree.value.cancelSelectedNode(node);
        nodeId.value = '';
        nodeCode.value = '';
      } else {
        nodeStatus.value = !nodeStatus.value;
      }
    } else {
      nodeId.value = node.id;
      nodeCode.value = node.instCode;
      deptName.value = parentName ? parentName + '/' + node.instName : node.instName;
      instName.value = node.instName;
    }
  };
  const setting = ref<any>({
    data: {
      simpleData: {
        enable: true,
        pIdKey: 'parentId',
      },
      key: {
        name: 'instName',
      },
    },
    view: {
      showIcon: false,
      dblClickExpand: false, // 双击不允许展开节点
      fontCss: function (treeId, treeNode) {
        // console.log(treeNode);
        return !!treeNode.highlight
          ? { color: '#A60000', 'font-weight': 'bold' }
          : { color: '#000000', 'font-weight': 'normal' };
      },
    },
    edit: {
      drag: {
        isMove: true,
        prev: true,
        next: true,
        inner: false,
      },
      enable: true,
      showRenameBtn: false, //隐藏自带的修改按钮
      showRemoveBtn: false, //隐藏自带的修改按钮
    },
    callback: {
      onClick: handleClick,
    },
  });

  const handleCreated = (ztreeObj) => {
    ztree.value = ztreeObj;
    let node: any = {};
    if (!!nodeId.value) {
      node = ztreeObj.getNodesByParam('id', nodeId.value, null)[0];
      nodeStatus.value = true;
      ztreeObj.selectNode(node);
      ztreeObj.setting.callback.onClick('', ztreeObj.setting.treeId, node);
    }
    if (!nodeId.value) {
      if (ztreeObj.getNodes()[0]) {
        ztreeObj.expandNode(ztreeObj.getNodes()[0], true);
      }
    }
  };

  return {
    setting,
    handleCreated,
    ztree,
    value,
    nodeId,
    nodeStatus,
    nodeCode,
    deptName,
    instName,
    getParentNodeNames,
  };
}
