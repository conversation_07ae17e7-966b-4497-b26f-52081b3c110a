<template>
  <div class="workflow-management">
    <!-- 工具栏 -->
    <!-- <div class="toolbar">
      <div class="toolbar-left">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索编排名称..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <el-select
          v-model="statusFilter"
          placeholder="所有状态"
          clearable
          @change="handleFilter"
        >
          <el-option label="所有状态" value="" />
          <el-option label="已激活" value="active" />
          <el-option label="禁用" value="draft" />
        </el-select>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="createWorkflow">
          <el-icon><Plus /></el-icon>
          新建编排
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div> -->
    <div class="agent-set-top">
      <el-input
        v-model="searchKeyword"
        style="width: 240px; margin-right: 12px"
        placeholder="搜索编排名称"
        :prefix-icon="Search"
        clearable
        @clear="handleSearch"
      />
      <el-select
        style="width: 240px; margin-right: 12px"
        v-model="statusFilter"
        placeholder="所有状态"
        clearable
        @change="handleFilter"
        @clear="handleSearch"
      >
        <el-option label="所有状态" value="" />
        <el-option label="启用" value="active" />
        <el-option label="禁用" value="draft" />
      </el-select>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button type="primary" @click="createWorkflow" :icon="Plus">新建</el-button>
    </div>
    <!-- 编排列表 -->
    <div v-loading="loading" class="workflow-grid">
      <el-row :gutter="20">
        <el-col
          v-for="(workflow, index) in filteredItems"
          :key="index"
          :xs="24"
          :sm="24"
          :md="12"
          :lg="12"
          :xl="8"
        >
          <div class="workflow-card" @click="handleWorkflowClick(workflow)">
            <div
              class="mark"
              :class="{
                open: workflow.isPublish === true,
                close: workflow.isPublish === false,
              }"
            >
              <span>{{ workflow.isPublish === true ? '启用' : '禁用' }}</span>
            </div>
            <div class="card-header">
              <div class="workflow-info">
                <h3 class="workflow-title">{{ workflow.title || workflow.name }}</h3>
                <div class="workflow-meta">
                  <span class="meta-item">
                    <el-icon><Calendar /></el-icon>
                    {{ formatDate(workflow.createTime) }}
                  </span>
                  <span class="meta-item">
                    <el-icon><User /></el-icon>
                    {{ workflow.creator || '系统' }}
                  </span>
                </div>
              </div>
              <!-- <div class="workflow-status">
                <el-tag
                  :type="workflow.isPublish === true ? 'success' : 'warning'"
                  size="small"
                >
                  {{ workflow.isPublish === true ? '启用' : '禁用' }}
                </el-tag>
              </div> -->
            </div>

            <div class="card-content">
              <p class="workflow-description">{{
                workflow.description || '暂无描述'
              }}</p>

              <div class="workflow-stats">
                <div class="stat-item">
                  <el-icon><Avatar /></el-icon>
                  <span>{{ workflow.agentCount || 0 }} 个智能体</span>
                </div>
                <div class="stat-item">
                  <el-icon><List /></el-icon>
                  <span>{{ workflow.stepCount || 0 }} 个步骤</span>
                </div>
              </div>
            </div>

            <div class="card-bottom">
              <div class="card-bottom-actions">
                <!-- <el-button @click="handleEdit(item)" :icon="Edit">编辑</el-button> -->
                <el-button :icon="Edit" @click.stop="editWorkflow(workflow)">
                  编辑
                </el-button>
                <!-- <el-icon><Top /></el-icon><el-icon><Bottom /></el-icon> -->
                <el-button
                  :icon="workflow.isPublish === true ? Bottom : Top"
                  @click.stop="handlePublish(workflow)"
                >
                  {{ workflow.isPublish === true ? '禁用' : '启用' }}
                </el-button>
                <!-- <el-button :icon="CopyDocument" @click.stop="copyWorkflow(workflow)">
                  复制
                </el-button> -->
                <!-- <el-button :icon="DeleteFilled" @click.stop="deleteWorkflow(workflow)">
                  删除
                </el-button> -->

                <el-popconfirm
                  :icon="InfoFilled"
                  title="确定要删除吗?"
                  @confirm="handleDelete(workflow.id)"
                >
                  <template #reference>
                    <el-button @click.stop :icon="DeleteFilled">删除</el-button>
                  </template>
                </el-popconfirm>
                <!-- <el-popconfirm
                    :icon="InfoFilled"
                    title="确定要删除吗?"
                    @confirm="handleDelete(item.id)"
                  >
                    <template #reference>
                      <el-button :icon="DeleteFilled">删除</el-button>
                    </template>
                  </el-popconfirm> -->
              </div>
            </div>
            <!-- 
            <div class="card-actions">
              <el-button size="small" @click.stop="editWorkflow(workflow)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button size="small" @click.stop="copyWorkflow(workflow)">
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click.stop="deleteWorkflow(workflow)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div> -->
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && filteredItems.length === 0" class="empty-state">
      <el-empty description="暂无编排数据">
        <el-button type="primary" @click="createWorkflow">创建第一个编排</el-button>
      </el-empty>
    </div>

    <!-- 智能体编排对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑智能体编排' : '新建智能体编排'"
      width="1200px"
      @close="resetForm"
      class="workflow-dialog"
    >
      <div class="workflow-designer-content">
        <!-- 左侧配置面板 -->
        <div class="config-section">
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>编排配置</span>
          </div>
          <div class="section-content">
            <el-form
              ref="formRef"
              :model="formData"
              :rules="formRules"
              label-width="100px"
            >
              <el-form-item label="编排名称" prop="title">
                <el-input
                  v-model="formData.title"
                  placeholder="例如：天气查询助手"
                  class="modern-input"
                />
                <div class="form-tip">为您的智能体编排起一个清晰的名称</div>
              </el-form-item>

              <el-form-item label="编排描述">
                <el-input
                  v-model="formData.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请描述这个编排的用途和功能"
                  class="modern-textarea"
                />
              </el-form-item>

              <el-form-item label="执行统计">
                <div class="stats-grid">
                  <div class="stat-card">
                    <div class="stat-number">{{ agentSequence.length }}</div>
                    <div class="stat-label">智能体</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-number">{{ configuredAgentsCount }}</div>
                    <div class="stat-label">已配置</div>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 右侧编排区域 -->
        <div class="workflow-section">
          <div class="section-header">
            <div class="header-left">
              <el-icon><Connection /></el-icon>
              <span>智能体序列</span>
            </div>
            <el-button type="primary" :icon="Plus" @click="addNewAgent">
              添加智能体
            </el-button>
          </div>
          <div class="section-content">
            <!-- 智能体列表 -->
            <!-- {{ agentSequence }} -->
            <div class="agent-list" v-if="agentSequence.length > 0">
              <draggable
                v-model="agentSequence"
                item-key="id"
                handle=".drag-handle"
                @end="onDragEnd"
                class="agent-sequence"
              >
                <template #item="{ element: agent, index }">
                  <div class="agent-item" :key="agent.id">
                    <div class="agent-content">
                      <div class="drag-handle" title="拖拽排序">
                        <el-icon><Rank /></el-icon>
                      </div>
                      <div class="agent-number">{{ index + 1 }}</div>
                      <div class="agent-main">
                        <el-select
                          v-model="agent.agentName"
                          placeholder="选择智能体类型..."
                          @change="handleAgentChange(index, $event)"
                          class="agent-selector"
                          filterable
                        >
                          <el-option
                            v-for="agentType in availableAgents"
                            :key="agentType.id"
                            :label="agentType.name"
                            :value="agentType.id"
                          >
                            <div class="agent-option">
                              <div class="agent-option-header">
                                <span class="agent-option-name">{{
                                  agentType.name
                                }}</span>
                                <el-tag v-if="agentType.type" size="small" type="info">
                                  {{ agentType.type }}
                                </el-tag>
                              </div>
                              <div class="agent-option-desc">{{
                                agentType.description
                              }}</div>
                            </div>
                          </el-option>
                        </el-select>
                        <div v-if="agent.agentName" class="agent-info">
                          <div class="agent-name">{{ agent.agentName }}</div>
                          <div class="agent-description">{{
                            getAgentDescription(agent.agentName)
                          }}</div>
                        </div>
                      </div>
                      <div class="agent-actions">
                        <el-button
                          size="small"
                          @click="duplicateAgent(index)"
                          title="复制"
                          class="action-btn"
                        >
                          <el-icon><CopyDocument /></el-icon>
                        </el-button>
                        <el-button
                          size="small"
                          type="danger"
                          @click="removeAgent(index)"
                          title="删除"
                          class="action-btn"
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>

            <!-- 空状态 -->
            <div v-else class="agent-list-empty">
              <div class="empty-icon">
                <el-icon size="64"><Avatar /></el-icon>
              </div>
              <h3 class="empty-title">开始创建您的智能体编排</h3>
              <p class="empty-description"
                >添加智能体并配置它们的执行顺序，构建强大的自动化工作流</p
              >
              <el-button type="primary" @click="addNewAgent" class="empty-add-btn">
                <el-icon><Plus /></el-icon>
                添加第一个智能体
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <!-- <el-button @click="previewWorkflow" class="modern-btn-secondary">
          <el-icon><View /></el-icon>
          预览
        </el-button> -->
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新编排' : '保存编排' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue';
  //   import { useRouter } from 'vue-router';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { cloneDeep } from 'lodash-es';
  import {
    Plus,
    Edit,
    Delete,
    Search,
    Calendar,
    User,
    Avatar,
    List,
    CopyDocument,
    Setting,
    Connection,
    Rank,
    DeleteFilled,
    InfoFilled,
    Top,
    Bottom,
  } from '@element-plus/icons-vue';
  import draggable from 'vuedraggable';

  // 添加Robot图标的替代方案
  //   const Robot = { name: 'Robot' };
  import {
    getWorkflowListApi,
    createWorkflowApi,
    // updateWorkflowApi,
    deleteWorkflowApi,
    getWorkflowDetailApi,
    getAgentsApi,
    planTemplatePublishApi,
  } from '../../api/agentSet';

  // 路由
  //   const router = useRouter();

  // 响应式数据
  const loading = ref(false);
  const submitting = ref(false);
  const dialogVisible = ref(false);
  const isEdit = ref(false);
  const searchKeyword = ref('');
  const statusFilter = ref('');
  const workflows = ref<any[]>([]);
  const formRef = ref();
  const availableAgents = ref<any[]>([]);
  const filteredItems = ref<any[]>([]);
  // 表单数据
  const formData = reactive({
    id: '',
    title: '',
    description: '',
    content: '',
    status: 'draft',
  });

  // 智能体编排相关数据
  const agentSequence = ref<any[]>([]);
  const sequenceCounter = ref(1);

  // 可用智能体列表
  // const availableAgents = ref<any[]>([]);

  // 加载可用智能体列表
  const loadAvailableAgents = async () => {
    try {
      console.log('开始加载可用智能体列表...');
      const response = await getAgentsApi();
      console.log('智能体API响应:', response);

      // 处理响应数据
      const agents = response.data || response || [];

      availableAgents.value = agents.map((agent) => ({
        id: agent.id || agent.agentId || agent.name,
        name: agent.name || agent.agentName || agent.id,
        description:
          agent.description || agent.prompt || `${agent.name || '智能体'}的描述信息`,
        type: agent.type || 'general',
        tools: agent.tools || [],
        config: agent.config || {},
      }));

      // 如果没有数据，使用默认智能体
      if (availableAgents.value.length === 0) {
        availableAgents.value = [
          {
            id: 'DEFAULT_AGENT',
            name: 'DEFAULT_AGENT',
            description:
              '一个通用的智能代理，可以使用各种工具来完成任务。它具有强大的推理能力，并能根据用户需求选择合适的工具。',
            type: 'general',
          },
          {
            id: 'SQL_GENERATOR_AGENT',
            name: 'SQL_GENERATOR_AGENT',
            description:
              '专门用于生成SQL查询语句的智能代理，能够根据自然语言描述生成准确的SQL。',
            type: 'sql',
          },
          {
            id: 'SQL_EXECUTION_INTERPRETER_AGENT',
            name: 'SQL_EXECUTION_INTERPRETER_AGENT',
            description: '专门用于执行SQL查询并解释结果的智能代理。',
            type: 'sql',
          },
        ];
      }

      console.log('可用智能体加载完成:', availableAgents.value);
    } catch (error) {
      console.error('加载智能体列表失败:', error);
      // 使用默认智能体作为备选
      availableAgents.value = [
        {
          id: 'DEFAULT_AGENT',
          name: 'DEFAULT_AGENT',
          description: '一个通用的智能代理，可以使用各种工具来完成任务。',
          type: 'general',
        },
      ];
      ElMessage.warning('加载智能体列表失败，使用默认配置');
    }
  };

  // 表单验证规则
  const formRules = {
    title: [{ required: true, message: '请输入编排名称', trigger: 'blur' }],
    content: [{ required: true, message: '请输入编排内容', trigger: 'blur' }],
  };

  // 计算属性 - 过滤后的编排列表
  // const filteredWorkflows = computed(() => {
  //   let result = workflows.value;

  //   if (searchKeyword.value) {
  //     const keyword = searchKeyword.value.toLowerCase();
  //     result = result.filter(
  //       (workflow) =>
  //         workflow.title?.toLowerCase().includes(keyword) ||
  //         workflow.name?.toLowerCase().includes(keyword) ||
  //         workflow.description?.toLowerCase().includes(keyword),
  //     );
  //   }

  //   if (statusFilter.value) {
  //     result = result.filter((workflow) => workflow.status === statusFilter.value);
  //   }

  //   return result;
  // });

  // 计算属性 - 已配置的智能体数量
  const configuredAgentsCount = computed(() => {
    return agentSequence.value.filter((agent) => agent.agentId).length;
  });

  // 页面加载时获取数据
  onMounted(() => {
    loadWorkflowData();
    loadAvailableAgents();
  });

  // 加载编排数据
  const loadWorkflowData = async () => {
    loading.value = true;
    try {
      console.log('开始加载编排数据...');
      const response = await getWorkflowListApi();
      console.log('编排API响应:', response);

      // 处理响应数据 - 适配不同的API响应格式
      let data: any = [];
      if (response.success && response.data) {
        data = response.data.templates || response.data.list || response.data || [];
      } else if (response.templates) {
        data = response.templates;
      } else if (response.data) {
        data = response.data;
      } else if (Array.isArray(response)) {
        data = response;
      }

      workflows.value = data.map((workflow) => {
        // 解析content中的智能体序列
        let agentSequenceData = [];
        let agentCount = 0;
        let stepCount = 0;

        try {
          if (workflow.content) {
            const contentObj =
              typeof workflow.content === 'string'
                ? JSON.parse(workflow.content)
                : workflow.content;

            agentSequenceData = contentObj.agentSequence || [];
            agentCount = contentObj.metadata?.totalAgents || agentSequenceData.length;
            stepCount = contentObj.metadata?.stepCount || agentSequenceData.length;
          }
        } catch (e) {
          console.warn('解析编排内容失败:', e);
          agentCount = extractAgentCount(
            workflow.description || workflow.content || '',
          );
          stepCount = extractStepCount(workflow.description || workflow.content || '');
        }

        return {
          id: workflow.id || workflow.planId || Math.random().toString(36).substr(2, 9),
          title: workflow.title || workflow.name || workflow.planName || '未命名编排',
          name: workflow.name || workflow.planName,
          description: workflow.description || workflow.planDescription || '',
          content: workflow.content || '',
          isPublish: workflow.isPublish,
          createTime:
            workflow.createTime ||
            workflow.createdAt ||
            workflow.createDate ||
            new Date().toISOString(),
          updateTime: workflow.updateTime || workflow.updatedAt || workflow.updateDate,
          creator: workflow.creator || workflow.author || workflow.createBy || '系统',
          agentCount: agentCount,
          stepCount: stepCount,
          agentSequence: agentSequenceData,
        };
      });
      filteredItems.value = cloneDeep(workflows.value);
      console.log('编排数据加载完成:', workflows.value);
    } catch (error) {
      console.error('加载编排数据失败:', error);
      ElMessage.error('加载编排数据失败: ' + (error.message || '网络错误'));
      // 设置空数组避免页面报错
      workflows.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 提取智能体数量
  const extractAgentCount = (text) => {
    if (!text) return 0;
    const matches = text.match(/agent|智能体|代理/gi);
    return matches ? Math.min(matches.length, 10) : Math.floor(Math.random() * 5) + 1;
  };

  // 提取步骤数量
  const extractStepCount = (text) => {
    if (!text) return 0;
    const matches = text.match(/步骤|step|阶段|phase/gi);
    return matches ? Math.min(matches.length, 20) : Math.floor(Math.random() * 8) + 2;
  };

  // 格式化日期
  const formatDate = (dateStr) => {
    if (!dateStr) return '-';
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('zh-CN');
    } catch {
      return '-';
    }
  };

  // 刷新数据
  // const refreshData = () => {
  //   loadWorkflowData();
  //   loadAvailableAgents();
  // };

  // 搜索处理
  const handleSearch = () => {
    // 搜索逻辑在计算属性中处理
    filteredItems.value = [...workflows.value];
    console.log(filteredItems.value);
    const searchTerm = searchKeyword.value.toLowerCase();
    filteredItems.value = workflows.value.filter((item) =>
      item.title.toLowerCase().includes(searchTerm),
    );
    console.log(statusFilter.value);
    if (statusFilter.value) {
      if (statusFilter.value === 'active') {
        filteredItems.value = filteredItems.value.filter(
          (item) => item.isPublish === true,
        );
      } else {
        filteredItems.value = filteredItems.value.filter(
          (item) => item.isPublish === false,
        );
      }
    }
  };

  // 筛选处理
  const handleFilter = () => {
    // 筛选逻辑在计算属性中处理
  };

  // 创建编排
  const createWorkflow = () => {
    isEdit.value = false;
    dialogVisible.value = true;
    resetForm();
    // 重置智能体序列
    agentSequence.value = [];
    sequenceCounter.value = 1;
  };

  // 生成智能体ID
  const generateAgentId = () => {
    return 'agent_' + sequenceCounter.value++;
  };

  // 添加新智能体
  const addNewAgent = () => {
    const newAgent = {
      id: generateAgentId(),
      agentId: '',
      agentName: '',
      description: '',
      taskDescription: '',
    };
    agentSequence.value.push(newAgent);
    ElMessage.success('智能体已添加，请选择类型');
  };

  // 处理智能体选择变化
  const handleAgentChange = (index, agentId) => {
    if (agentId) {
      const agent = availableAgents.value.find((a) => a.id === agentId);
      if (agent) {
        agentSequence.value[index].agentId = agentId;
        agentSequence.value[index].agentName = agent.name;
        agentSequence.value[index].taskDescription = `使用 ${agent.name} 处理任务`;
      }
    } else {
      agentSequence.value[index].agentId = '';
      agentSequence.value[index].agentName = '';
      agentSequence.value[index].taskDescription = '';
    }
  };

  // 获取智能体描述
  const getAgentDescription = (name) => {
    const agent = availableAgents.value.find((a) => a.name === name);
    return agent ? agent.description : '';
  };

  // 复制智能体
  const duplicateAgent = (index) => {
    const originalAgent = agentSequence.value[index];
    const duplicatedAgent = {
      id: generateAgentId(),
      agentId: originalAgent.agentId,
      agentName: originalAgent.agentName,
      description: originalAgent.description,
      taskDescription: originalAgent.taskDescription,
    };
    agentSequence.value.splice(index + 1, 0, duplicatedAgent);
    ElMessage.success('智能体已复制');
  };

  // 删除智能体
  const removeAgent = async (index) => {
    try {
      await ElMessageBox.confirm('确定要删除这个智能体吗？', '确认删除', {
        type: 'warning',
      });
      agentSequence.value.splice(index, 1);
      ElMessage.success('智能体已删除');
    } catch {
      // 用户取消删除
    }
  };

  // 拖拽结束处理
  const onDragEnd = () => {
    console.log('拖拽排序完成');
  };

  // 预览工作流
  // const previewWorkflow = () => {
  //   if (!formData.title.trim()) {
  //     ElMessage.warning('请先输入编排名称');
  //     return;
  //   }

  //   if (agentSequence.value.length === 0) {
  //     ElMessage.warning('请至少添加一个智能体');
  //     return;
  //   }

  //   // 显示预览信息
  //   const previewData = {
  //     title: formData.title,
  //     agents: agentSequence.value.filter((agent) => agent.agentId),
  //   };

  //   console.log('预览数据:', previewData);
  //   ElMessage.info('预览功能开发中...');
  // };

  // 编排点击处理
  const handleWorkflowClick = (workflow) => {
    console.log('点击编排:', workflow);
    // 可以添加查看详情或进入编辑器的逻辑
  };

  // 编辑编排
  const editWorkflow = async (workflow) => {
    try {
      console.log('开始编辑编排:', workflow);

      // 如果有详细API，先获取完整数据
      let workflowDetail = workflow;
      if (workflow.id) {
        try {
          const detailResponse = await getWorkflowDetailApi({ planId: workflow.id });
          console.log('编排详情API响应:', detailResponse);

          if (detailResponse && detailResponse) {
            workflowDetail = detailResponse;
          } else if (detailResponse) {
            workflowDetail = detailResponse;
          }
        } catch (error) {
          console.warn('获取编排详情失败，使用列表数据:', error);
        }
      }

      isEdit.value = true;
      dialogVisible.value = true;

      // 填充表单数据
      Object.assign(formData, {
        id: workflowDetail.id || workflow.id,
        title: workflowDetail.title || workflowDetail.name || workflow.title,
        // description: workflowDetail.description || workflow.description,
        description: '',
        content: workflowDetail.content || workflow.content,
        status: workflowDetail.status || workflow.status || 'active',
      });

      // 解析并恢复智能体序列
      agentSequence.value = [];

      // [
      //   {
      //     id: 'agent_1',
      //     agentId: '59',
      //     agentName: 'SQL_EXECUTION_VISUALIZER_AGENT',
      //     description: '',
      //     taskDescription: '使用 SQL_EXECUTION_VISUALIZER_AGENT 处理任务',
      //   },
      // ];
      sequenceCounter.value = 1;
      console.log('workflowDetail.content');
      console.log(workflowDetail.content);
      console.log(workflowDetail);

      // "{"planId":"planTemplate-1753690787492","title":"test1111","description":"test1111test1111test1111test1111test1111","steps":[{"stepRequirement":"[TEXT_FILE_AGENT]使用 TEXT_FILE_AGENT 处理任务"},{"stepRequirement":"[SQL_QUERY_AGENT_TEST]使用 SQL_QUERY_AGENT_TEST 处理任务"}]}"
      try {
        if (workflowDetail.content) {
          const contentObj =
            typeof workflowDetail.content === 'string'
              ? JSON.parse(workflowDetail.content)
              : workflowDetail.content;
          formData.description = contentObj.description;

          if (contentObj.steps && Array.isArray(contentObj.steps)) {
            console.log('contentObj.steps');
            console.log(contentObj.steps);
            agentSequence.value = contentObj.steps.map((agent, index) => ({
              id: agent.id || `agent_${index + 1}`,
              agentId: agent.agentId || '',
              agentName: agent.stepRequirement.match(/\[(.*?)\]/)[1] || '',
              description: '',
              taskDescription: agent.stepRequirement.split(']')[1].trim() || '',
            }));
            sequenceCounter.value = agentSequence.value.length + 1;
          }
        }
      } catch (error) {
        console.warn('解析智能体序列失败:', error);
      }

      console.log('编排数据已加载:', {
        formData: formData,
        agentSequence: agentSequence.value,
      });
    } catch (error) {
      console.error('编辑编排失败:', error);
      ElMessage.error('加载编排数据失败: ' + (error.message || '未知错误'));
    }
  };

  // 复制编排
  // const copyWorkflow = async (workflow) => {
  //   try {
  //     const copyData = {
  //       title: `${workflow.title} - 副本`,
  //       description: workflow.description,
  //       content: workflow.content,
  //       status: 'draft',
  //     };

  //     await createWorkflowApi(copyData);
  //     ElMessage.success('复制成功');
  //     loadWorkflowData();
  //   } catch (error) {
  //     console.error('复制编排失败:', error);
  //     ElMessage.error('复制失败: ' + (error.message || '未知错误'));
  //   }
  // };

  const handleDelete = (id) => {
    console.log(id);

    deleteWorkflowApi(id).then(() => {
      loadWorkflowData();
      ElMessage({
        type: 'success',
        message: '删除成功！',
      });
    });
  };

  const handlePublish = (workflow) => {
    console.log(workflow);
    // planTemplatePublishApi
    planTemplatePublishApi(workflow.id, !workflow.isPublish).then(() => {
      if (workflow.isPublish) {
        ElMessage.success('禁用成功');
        loadWorkflowData();
      } else {
        ElMessage.success('启用成功');
        loadWorkflowData();
      }
    });
  };
  // 删除编排
  // const deleteWorkflow = async (workflow) => {
  //   try {
  //     await ElMessageBox.confirm(
  //       `确定要删除编排 "${workflow.title}" 吗？`,
  //       '确认删除',
  //       {
  //         confirmButtonText: '确定',
  //         cancelButtonText: '取消',
  //         type: 'warning',
  //       },
  //     );

  //     await deleteWorkflowApi(workflow.id);
  //     ElMessage.success('删除成功');
  //     loadWorkflowData();
  //   } catch (error) {
  //     if (error !== 'cancel') {
  //       console.error('删除编排失败:', error);
  //       ElMessage.error('删除失败: ' + (error.message || '未知错误'));
  //     }
  //   }
  // };

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      id: '',
      title: '',
      description: '',
      content: '',
      status: 'draft',
    });

    // 重置智能体序列
    agentSequence.value = [];
    sequenceCounter.value = 1;

    if (formRef.value) {
      formRef.value.clearValidate();
    }
  };

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return;

    try {
      await formRef.value.validate();

      // 验证编排名称
      if (!formData.title.trim()) {
        ElMessage.warning('请输入编排名称');
        return;
      }

      // 验证智能体序列
      if (agentSequence.value.length === 0) {
        ElMessage.warning('请至少添加一个智能体');
        return;
      }

      // 验证所有智能体都已配置
      // for (let i = 0; i < agentSequence.value.length; i++) {
      //   const agent = agentSequence.value[i];
      //   if (!agent.agentId) {
      //     ElMessage.warning(`第 ${i + 1} 个智能体未选择类型，请检查`);
      //     return;
      //   }
      // }

      submitting.value = true;
      const planId = 'planTemplate-' + Date.now();
      // 准备提交数据
      console.log('agentSequence.value');
      console.log(agentSequence.value);
      const configuredAgents = agentSequence.value.filter((agent) => agent.agentName);
      console.log(configuredAgents);
      const steps: any[] = [];

      for (let i = 0; i < configuredAgents.length; i++) {
        let item = {
          stepRequirement:
            '[' +
            configuredAgents[i].agentName +
            ']' +
            configuredAgents[i].taskDescription,
        };
        steps.push(item);
      }
      console.log('step');
      console.log(steps);
      const json = {
        planId: isEdit.value ? formData.id : planId,
        title: formData.title,
        description: formData.description,
        steps,
      };
      const submitData = {
        planId: isEdit.value ? formData.id : planId,
        planJson: JSON.stringify(json),
        // title: formData.title,
        // description: formData.description,
        // content: JSON.stringify({
        //   agentSequence: configuredAgents,
        //   metadata: {
        //     totalAgents: agentSequence.value.length,
        //     configuredAgents: configuredAgents.length,
        //     stepCount: configuredAgents.length,
        //     createTime: new Date().toISOString(),
        //     version: '1.0',
        //   },
        // }),
        // status: formData.status,
      };

      console.log('提交数据:', submitData);

      if (isEdit.value) {
        // 更新编排
        // const updateData = { planJson: JSON.stringify(json), planId: formData.id };
        // console.log('更新编排数据:', updateData);

        const response = await createWorkflowApi(submitData);
        console.log('更新响应:', response);

        ElMessage.success('编排更新成功');
      } else {
        // 创建编排
        console.log('创建编排数据:', submitData);

        const response = await createWorkflowApi(submitData);
        console.log('创建响应:', response);

        ElMessage.success('编排创建成功');
      }

      dialogVisible.value = false;
      loadWorkflowData();
    } catch (error) {
      console.error('提交失败:', error);
      ElMessage.error('操作失败: ' + (error.message || '未知错误'));
    } finally {
      submitting.value = false;
    }
  };
</script>

<style scoped lang="scss">
  .workflow-management {
    padding: 20px;
    background: #ffffff;
    min-height: calc(100vh - 64px);
  }
  .agent-set-top {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-bottom: 24px;
    margin-top: 4px;
  }
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-box {
        width: 300px;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 12px;
    }
  }

  .workflow-grid {
    overflow-y: auto;
    overflow-x: hidden;
    height: calc(100vh - 180px);
    .el-row {
      margin-top: 8px;
    }
  }

  .workflow-card {
    background: #f9fafb;
    border-radius: 16px;
    border: 1px solid #e5e7eb;
    padding: 24px;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    cursor: pointer;
    overflow: hidden;
    margin-bottom: 12px;
    .mark {
      position: absolute;
      // position: relative;
      width: 60px;
      height: 60px;
      top: 0;
      right: 0;

      transform: rotate(45deg) translateY(-42px);
      span {
        // margin-top: 80px;
        position: absolute;
        // background: #000;
        bottom: 0;
        text-align: center;
        left: 20px;
        font-size: 12px;
        color: #ffffff;
      }
    }
    .mark.open {
      background: #059669;
    }
    .mark.close {
      background: #e6a23c;
    }
    // .mark::before {
    //   position: absolute;
    //   top: 0;
    //   right: 0;
    //   width: 120px; /* 调整大小以适应文字 */
    //   height: 30px;
    //   background-color: #ff4757;
    //   color: white;
    //   font-size: 12px;
    //   font-weight: bold;
    //   text-align: center;
    //   line-height: 30px;
    //   transform: rotate(45deg) translateY(-10px);
    //   transform-origin: bottom left;
    //   box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    // }
    // .mark::after {
    //   content: '不发';
    //   position: absolute;
    //   bottom: 0;
    //   right: 0;
    //   border-width: 8px;
    //   border-style: solid;
    //   border-color: transparent #c23616 transparent transparent;
    // }
    &:hover {
      border-color: #409eff;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
      transform: translateY(-4px);
    }

    .card-header {
      // padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;
      // background: #fafbfc;

      .workflow-info {
        .workflow-title {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          line-height: 1.3;
        }

        .workflow-meta {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: #666;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }

      .workflow-status {
        margin-top: 8px;
      }
    }

    .card-content {
      padding: 12px 0 36px;

      .workflow-description {
        margin: 0 0 16px 0;
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .workflow-stats {
        display: flex;
        gap: 16px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 13px;
          color: #666;
        }
      }
    }
    .card-bottom {
      position: absolute;
      bottom: 18px;
      right: 25px;
      width: 100%;

      .card-bottom-actions {
        display: flex;
        gap: 4px;
        justify-content: flex-end;
      }
    }
    // .card-actions {
    //   padding: 12px 20px;
    //   border-top: 1px solid #f0f0f0;
    //   display: flex;
    //   justify-content: flex-end;
    //   gap: 8px;
    //   background: #fafbfc;
    // }
  }
  .workflow-card:hover::before {
    opacity: 1;
  }
  .workflow-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #409eff;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* 智能体编排对话框样式 - 现代化重构 */
  .workflow-dialog {
    :deep(.el-dialog) {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 32px 64px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05);
      border: none;
      background: #ffffff;
      max-width: 1200px;
      width: 90vw;
    }

    :deep(.el-dialog__header) {
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
      color: white;
      padding: 24px 32px;
      margin: 0;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            circle at 20% 50%,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 255, 255, 0.08) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 80%,
            rgba(255, 255, 255, 0.06) 0%,
            transparent 50%
          );
        pointer-events: none;
      }

      .el-dialog__title {
        color: white;
        font-weight: 600;
        font-size: 20px;
        position: relative;
        z-index: 1;
        display: flex;
        align-items: center;
        gap: 12px;

        &::before {
          content: '🤖';
          font-size: 24px;
        }
      }

      .el-dialog__headerbtn {
        top: 24px;
        right: 32px;
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.05);
        }

        .el-dialog__close {
          color: white;
          font-size: 18px;
          font-weight: 600;

          &:hover {
            color: white;
          }
        }
      }
    }

    :deep(.el-dialog__body) {
      padding: 0;
      background: #fafbfc;
    }

    :deep(.el-dialog__footer) {
      background: #ffffff;
      padding: 24px 32px;
      border-top: 1px solid #f1f5f9;
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  .workflow-designer-content {
    display: grid;
    grid-template-columns: 380px 1fr;
    gap: 0;
    min-height: 600px;
    background: #fafbfc;
    position: relative;
  }

  .config-section,
  .workflow-section {
    .section-header {
      background: #ffffff;
      color: #1e293b;
      padding: 20px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 600;
      border-bottom: 1px solid #f1f5f9;
      position: relative;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-icon {
          color: #6366f1;
          font-size: 20px;
        }

        span {
          font-size: 16px;
          font-weight: 600;
          color: #0f172a;
        }
      }

      .add-btn {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: none;
        color: white;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
          background: linear-gradient(135deg, #5b5bf6 0%, #8b5cf6 100%);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .section-content {
      padding: 24px;
      background: #ffffff;
      // min-height: 580px;
      height: 560px;
      overflow: auto;
    }
  }

  .config-section {
    border-right: 1px solid #f1f5f9;
    background: #ffffff;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
      background: linear-gradient(
        180deg,
        transparent 0%,
        #e2e8f0 20%,
        #e2e8f0 80%,
        transparent 100%
      );
    }

    .section-content {
      background: #ffffff;
      border-radius: 0;
    }
  }

  .modern-input,
  .modern-textarea {
    :deep(.el-input__wrapper),
    :deep(.el-textarea__inner) {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      background: #ffffff;
      transition: all 0.2s ease;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      min-height: 44px;

      &:hover {
        border-color: #cbd5e1;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &.is-focus {
        border-color: #6366f1;
        background: #ffffff;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      }
    }

    :deep(.el-input__inner),
    :deep(.el-textarea__inner) {
      color: #1e293b;
      font-weight: 500;
      font-size: 14px;

      &::placeholder {
        color: #94a3b8;
        font-weight: 400;
      }
    }
  }

  .form-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #64748b;
    line-height: 1.4;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .stat-card {
    background: #ffffff;
    border: 1px solid #f1f5f9;
    border-radius: 12px;
    padding: 24px 20px;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      border-color: #e2e8f0;
    }

    .stat-number {
      font-size: 2.5rem;
      font-weight: 700;
      color: #6366f1;
      margin-bottom: 8px;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.875rem;
      color: #64748b;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .agent-list {
    height: 520px;
    // overflow-y: auto;
  }

  .agent-sequence {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .agent-item {
    background: #ffffff;
    border: 1px solid #f1f5f9;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
    position: relative;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      border-color: #e2e8f0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .agent-content {
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
  }

  .drag-handle {
    cursor: grab;
    color: #cbd5e1;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #f8fafc;
      color: #64748b;
    }

    &:active {
      cursor: grabbing;
      background: #f1f5f9;
    }
  }

  .agent-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.25);
    border: none;
  }

  .agent-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .agent-selector {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      background: white;
      transition: all 0.2s ease;
      min-height: 44px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: #cbd5e1;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &.is-focus {
        border-color: #6366f1;
        background: white;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      }
    }

    :deep(.el-input__inner) {
      font-weight: 500;
      color: #1e293b;
      font-size: 14px;
    }

    :deep(.el-select__placeholder) {
      color: #94a3b8;
      font-weight: 400;
    }
  }

  .agent-option {
    padding: 8px 0;

    .agent-option-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 4px;
    }

    .agent-option-name {
      font-weight: 600;
      color: #1e293b;
      font-size: 14px;
    }

    .agent-option-desc {
      font-size: 12px;
      color: #64748b;
      line-height: 1.4;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .agent-info {
    .agent-name {
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 2px;
      font-size: 0.75rem;
    }

    .agent-description {
      font-size: 0.6875rem;
      color: #475569;
      line-height: 1.4;
    }
  }

  .agent-actions {
    display: flex;
    gap: 4px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .agent-list-empty {
    padding: 80px 40px;
    text-align: center;
    color: #64748b;
    background: #ffffff;
    border-radius: 12px;
    border: 2px dashed #e2e8f0;
    margin: 24px;
    position: relative;
  }

  .empty-icon {
    color: #cbd5e1;
    margin-bottom: 24px;
    animation: pulse 2s ease-in-out infinite;
  }

  .empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 16px 0;
  }

  .empty-description {
    font-size: 1rem;
    color: #64748b;
    line-height: 1.6;
    margin: 0 0 32px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  .empty-add-btn {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    border-radius: 10px;
    padding: 14px 28px;
    font-weight: 600;
    font-size: 15px;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.25);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(99, 102, 241, 0.35);
    }

    &:active {
      transform: translateY(0);
    }
  }

  /* 现代化表单标签样式 */
  .modern-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #0f172a;
    font-size: 14px;

    &::before {
      content: '';
      width: 3px;
      height: 16px;
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      border-radius: 2px;
    }
  }

  /* 现代化按钮样式 */
  .modern-btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    color: white;
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.25);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.35);
      background: linear-gradient(135deg, #5b5bf6 0%, #8b5cf6 100%);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .modern-btn-secondary {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    color: #64748b;
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;

    &:hover {
      border-color: #cbd5e1;
      color: #475569;
      background: #f8fafc;
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  .agent-list::-webkit-scrollbar {
    width: 6px;
  }

  .agent-list::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .agent-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .agent-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
</style>
