/**
 * Configure and register global directives
 */
import { setupResizeDirective } from './elResizeDetector';
import { setupAuthDirective } from './auth';
// import { setupPermissionDirective } from './permission';
import { setupLoadDirective } from './load';
import { setupDebounceDirective } from './debounce';
import { setupThrottleDirective } from './throttle';

export function installGlobDirectives(app: any) {
  // setupPermissionDirective(app);
  setupResizeDirective(app);
  setupAuthDirective(app);
  setupLoadDirective(app);
  setupDebounceDirective(app);
  setupThrottleDirective(app);
  // setupLoadingDirective(app);
}
