import { BasicKeys } from '/@/utils/storage/typing';
import { CacheTypeEnum } from '/@/enums/cacheEnum';
import { projectSettings } from '/@/settings/config/projectConfig';
import { storageLocal, storageSession } from '/@/utils/storage/storageProxy';

import { AesEncryption, cacheCipher } from '/@/utils/cipher';
const encryption = new AesEncryption(cacheCipher);
const { cacheType, storageName, isEncrypt, encryptClearStorage } = projectSettings;

const fn = cacheType === CacheTypeEnum.LOCAL ? storageLocal : storageSession;

export function getStorage<T>(key: BasicKeys) {
  try {
    // 生产环境缓存加密和解密
    if (import.meta.env.MODE == 'production' && isEncrypt) {
      // console.log('mport.meta.env',import.meta.env.MODE == 'production');
      // console.log('fn.getItem<string>(`${storageName}${key}`)',fn.getItemString<string>(`${storageName}${key}`));
      const res = encryption.pwdDecryptByAES(
        fn.getItemString<string>(`${storageName}${key}`) || '',
      );
      return res ? JSON.parse(res) : res;
      // console.log('fn.res',res);
    } else {
      return fn.getItem<T>(`${storageName}${key}`) as T;
    }
  } catch (error) {
    console.log('getStorageerror', error);
    // 由于缓存加密导致的读取报错时，清空缓存
    if (encryptClearStorage) {
      fn.clear();
    }
  }
}

export function setStorage(key: BasicKeys, value) {
  try {
    // 生产环境缓存加密和解密
    if (import.meta.env.MODE == 'production' && isEncrypt) {
      return fn.setItemString(
        `${storageName}${key}`,
        encryption.pwdEncryptByAES(JSON.stringify(value)),
      );
    } else {
      return fn.setItem(`${storageName}${key}`, value);
    }
  } catch (error) {
    console.log('setStorageerror', error);
    // 由于缓存加密导致的读取报错时，清空缓存
    if (encryptClearStorage) {
      fn.clear();
    }
  }
}

export function removeStorage(key: BasicKeys) {
  return fn.removeItem(`${storageName}${key}`);
}

export function clearStorage() {
  return fn.clear();
}
