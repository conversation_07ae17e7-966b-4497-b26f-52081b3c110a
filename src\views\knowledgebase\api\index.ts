
import { defHttp } from '/@/utils/axios'
import type { RequestOptions } from '/#/axios'
// 接口返回数据类型定义
export interface ResponseData<T = any> {
  code: number;
  data: T;
  message: string;
}
export interface Knowledge {
  id?: string;
  promptName: string;
  promptContent: string;
  promptType: string;
  dataSourceId: number;
  schemaName: string;
  databaseName: string;
  tableName: string;
  version: number | string;
}
// 名词解释相关接口,业务逻辑解释相关接口,案例优化相关接口 
// promptType 类型如下
// NOUN_DESCRIPTION:名词解释；
// BUSINESS_LOGIC:业务逻辑解释；
// SQL_TEMPLATE:案例优化
export class AxiosKnowledgeService {
  static async getList(promptType: string, promptName?: string, page?: number, pageSize?: number, options?: RequestOptions): Promise<any> {
    return defHttp.get({
      url: `/cpit/knowledge/page?promptType=${promptType}&promptName=${promptName}&page=${page}&pageSize=${pageSize}`,
    }, options)
  }
  // 补足更新接口
  static async update(data: Knowledge, options?: RequestOptions): Promise<any> {
    return defHttp.put({
      url: `/cpit/knowledge`,
      data,
    }, options)
  }
  // 补足添加接口
  static async add(data: Knowledge, options?: RequestOptions): Promise<any> {
    return defHttp.post({
      url: `/cpit/knowledge`,
      data,
    }, options)
  }
  // 补足删除接口，删除id是数组
  static async delete(ids: string[], options?: RequestOptions): Promise<any> {
    return defHttp.delete({
      url: `/cpit/knowledge`,
      data: ids,
    }, options)
  }
}


