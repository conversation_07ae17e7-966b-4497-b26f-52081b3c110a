<template>
  <KnowledgeTable :config="tableConfig" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import KnowledgeTable from '../../components/KnowledgeTable.vue';
import type { KnowledgeTableConfig } from '../../composables/useKnowledgeTable';

// 名词解释表格配置
const tableConfig = computed<KnowledgeTableConfig>(() => ({
  promptType: 'NOUN_DESCRIPTION',
  title: '名词名称',
  desc: '名词解释',
  searchPlaceholder: '搜索名词解释',
  addButtonText: '新增名词解释',
  columns: [
    {
      prop: 'promptName',
      label: '名词名称',
      minWidth: 150,
      editable: true,
      required: true,
      placeholder: '请输入名词'
    },
    {
      prop: 'promptContent',
      label: '名词解释',
      minWidth: 250,
      editable: true,
      required: true,
      type: 'textarea',
      placeholder: '请输入描述'
    },
    {
      prop: 'dataSources',
      label: '绑定数据源',
      minWidth: 180,
      type: 'dataSource',
      editable: true
    },
    {
      prop: 'tableName',
      label: '数据表',
      width: 200,
      type: 'table',
      editable: true
    }
  ]
}));
</script>