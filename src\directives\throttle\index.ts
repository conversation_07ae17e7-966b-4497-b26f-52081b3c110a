// throttleDirective.ts
import { Directive } from 'vue';
import type { DirectiveBinding } from 'vue';
import { useThrottleFn } from '@vueuse/core';

// 使用 WeakMap 存储节流函数
const throttleMap = new WeakMap<HTMLElement, (event: Event) => void>();

export const throttle: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding<(value: string) => void>) {
    // 从 binding 中获取节流时间和回调函数
    const { value: callback, arg: wait = 300 } = binding;

    if (typeof callback !== 'function') {
      console.warn('v-throttle 指令的值必须是一个函数');
      return;
    }

    // 使用 useThrottleFn 创建节流函数
    const throttledFn = useThrottleFn((event: Event) => {
      const target = event.target as HTMLInputElement;
      callback(target.value); // 直接传递 value 给回调函数
    }, Number(wait)); // 将 arg 转换为数字

    // 监听 input 事件，并调用节流函数
    el.addEventListener('input', throttledFn);

    // 将节流函数保存到 WeakMap 中
    throttleMap.set(el, throttledFn);
  },
  unmounted(el: HTMLElement) {
    // 从 WeakMap 中获取节流函数并移除监听器
    const throttledFn = throttleMap.get(el);
    if (throttledFn) {
      el.removeEventListener('input', throttledFn);
      throttleMap.delete(el);
    }
  },
};

export function setupThrottleDirective(app: any) {
  app.directive('throttle', throttle);
}
