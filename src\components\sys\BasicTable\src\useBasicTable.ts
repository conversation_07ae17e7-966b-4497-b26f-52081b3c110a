import { TableOptions } from '/@/components/sys/BasicTable/types';
import { cloneDeep } from 'lodash-es';
import { isUnDef } from '/@/utils/is';
import { getStorage } from '/@/utils/storage';

export function useBasicTable(props: any, localKey: string) {
  /**
   * 表格初始化字段内容,在表格中起作用
   * @return TableOptions[]
   */
  function initColumns(): TableOptions[] {
    const tempColumns: TableOptions[] = [];
    let useColumns = props.columns;
    if (props.uselocal) {
      useColumns = getStorage(localKey) || props.columns;
    }
    for (const column of useColumns) {
      if (isUnDef(column?.initShow) || column?.initShow) {
        tempColumns.push(column);
      }
    }
    return cloneDeep(tempColumns);
  }
  return { initColumns };
}
